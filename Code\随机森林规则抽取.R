library(inTrees)
library(dplyr)
inws1 = 'C:\\Users\\<USER>\\Desktop\\Water\\Validation\\Global_join_rf0.csv'
df = read.csv(inws1)

data = subset(df, select=-c(1,3,5,6,17,24,29,30,33))

data = subset(df, select=-c(1,3,5,6,20,32,39,41,49))

data$refer <- ifelse(data$refer==2, "water", 
                    ifelse(data$refer==1, "other", "null"))
data$refer <- ifelse(data$type==2, "water", 
                  ifelse(data$type==10 | data$type==11 | data$type==12, "shadow", 
                          ifelse(data$type==100 | data$type==101 | data$type==102 | data$type==110 | data$type==111 | data$type==112, 'snow', 
                                 ifelse(data$type==1, "other", "null"))))
data = data[data$refer != 'null', ]

remove_outliers <- function(column) {
  threshold <- 2 * sd(column)
  column[abs(column - mean(column)) >= threshold] <- NA
  return(column)
}

data[,-c(22,24)] <- apply(data[,-c(22,24)], 2, remove_outliers)

data[,-c(32,40)] <- apply(data[,-c(32,40)], 2, remove_outliers)

data <- data[complete.cases(data), ]

set.seed(123)

train_index <- sample(nrow(data), round(0.7 * nrow(data)))

data_train <- data[train_index, ]
data_test <- data[-train_index, ]

library(RRF)
X_train <- data_train[,-c(22,24)]
X_train <- data_train[,-c(32,40)]
X_test <- data_test[,-c(22,24)]
X_test <- data_test[,-c(32,40)]

target_train <- data_train[,"refer"]
target_test <- data_test[,"refer"]
rf <- RRF(X_train,as.factor(target_train),ntree=500) # build an ordinary RF
xgb <- xgboost(model_mat, label = as.numeric(Y) - 1, nrounds = 20,
               objective ="multi:softprob", num_class = 3)
treeList <- RF2List(rf)
ruleExec <- extractRules(treeList,X_train, ntree=500, maxdepth = 5, random=TRUE, digits=3)
ruleExec <- unique(ruleExec)
ruleMetric <- getRuleMetric(ruleExec,X_test,target_test) # measure rules
ruleMetric <- pruneRule(ruleMetric,X_test,target_test) # prune each rule
ruleMetric <- selectRuleRRF(ruleMetric,X_test,target_test) # rule selection
learner <- buildLearner(ruleMetric,X_test,target_test)
pred <- applyLearner(learner,X_test)
read <- presentRules(learner,colnames(X_test)) # more readable format
# format the rule and metrics as a table in latex code
library(xtable)
print(read, include.rownames=FALSE)
print(ruleMetric, include.rownames=FALSE)

# 提取规则对比
library(caret)
data_test$refer <- ifelse(data_test$refer=="water", TRUE, 
                     ifelse(data_test$refer=="shadow" | data_test$refer=='snow' | data_test$refer=='other', FALSE, "null"))
# data_test$predict <- data_test$green_nir_divide>0.935 | data_test$nir<0.100 # 最优方法
# data_test$predict <- data_test$EVI<776 & data_test$swir<0.088
# data_test$predict <- data_test$blue_swir1_divide>1.044 | data_test$green_nir_divide>0.935 | data_test$nir<=0.101
shadow <- data_test$MNDWI<=-375 & data_test$nir>0.023
snow <- data_test$MNDWI>=1776 & data_test$nir>0.103
water <- data_test$green_nir_divide>0.935 | data_test$nir<0.100 | data_test$red_swir1_divide>1.254
water <- data_test$green_nir_divide>1.053 | data_test$nir<0.07 | data_test$red_swir1_divide>1.254

data_test$predict <-  water
data_test$predict <- data_test$green_nir_divide>0.935 | data_test$nir<0.100 | data_test$swir1<=0.052
data_test$predict <- data_test$green_nir_divide>0.638 | data_test$green_red_divide<1.023
data_test$predict <- data_test$EVI<322.5 & data_test$nir<0.276
data_test$predict <- data_test$green_nir_divide>=1.053  | data_test$nir<0.07 #最优
data_test$predict <- data_test$EVI<=546 & data_test$red<0.514 & data_test$swir1<=0.054
data_test$predict <- data_test$green_swir1_divide>0.802 & data_test$nir<=0.164
data_test$predict <- predict(rf, data_test)
data_test$predict <- ifelse(data_test$predict=="water", TRUE, 
                          ifelse(data_test$predict=="shadow" | data_test$predict=='snow' | data_test$predict=='other', FALSE, "null"))

data_test$zou <- (data_test$MNDWI>data_test$EVI | data_test$MNDWI>data_test$NDVI) & (data_test$EVI<1000)
data_test$before <- data_test$MNDWI>data_test$EVI | data_test$MNDWI>data_test$NDVI | (data_test$LSWI>data_test$NDVI)
data_test$before2 <- (data_test$MNDWI>data_test$EVI | data_test$MNDWI>data_test$BSI | data_test$LSWI>data_test$NDVI) & data_test$EVI<1000

data_test$mndwi <- data_test$MNDWI>0
confusionMatrix(data=as.factor(data_test$predict), reference = as.factor(data_test$refer))

difference <- data_test[data_test$predict!=data_test$refer, ]
