{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.metrics import accuracy_score, cohen_kappa_score, confusion_matrix\n", "import random\n", "from tqdm import tqdm"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## 水体提取探索"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# df1 = pd.read_csv(r\"C:\\Users\\<USER>\\Desktop\\Water\\Validation\\Global_join_water2.csv\").set_index('system:index')\n", "# df1 = pd.read_csv(r\"C:\\Users\\<USER>\\Desktop\\Water\\Validation\\Global_join_water10.csv\").set_index('OBJECTID')\n", "# df1 = pd.read_csv(r\"C:\\Users\\<USER>\\Desktop\\Water\\Validation\\Global_join_water_multiBands.csv\").set_index('ID')\n", "df1 = pd.read_csv(r\"C:\\Users\\<USER>\\Desktop\\Water\\Validation\\Global_join_water_method5.csv\").set_index('ID')"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>system:index</th>\n", "      <th>Classified</th>\n", "      <th>GrndTruth</th>\n", "      <th>fractional_years</th>\n", "      <th>random</th>\n", "      <th>refer</th>\n", "      <th>water_this</th>\n", "      <th>water_this_wt</th>\n", "      <th>water_zou</th>\n", "      <th>.geo</th>\n", "    </tr>\n", "    <tr>\n", "      <th>ID</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>98471</th>\n", "      <td>00000000000000003487_0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>2018.834486</td>\n", "      <td>0.862859</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71932</th>\n", "      <td>0000000000000000291d_0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>2021.830784</td>\n", "      <td>0.862862</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>58308</th>\n", "      <td>0000000000000000233c_0</td>\n", "      <td>0</td>\n", "      <td>-1</td>\n", "      <td>2020.415897</td>\n", "      <td>0.862879</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99766</th>\n", "      <td>00000000000000003528_0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>2021.916612</td>\n", "      <td>0.862901</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>74457</th>\n", "      <td>00000000000000002a37_0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>2021.330531</td>\n", "      <td>0.862902</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>89819</th>\n", "      <td>000000000000000030da_0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>2021.833814</td>\n", "      <td>0.892637</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91127</th>\n", "      <td>0000000000000000316d_0</td>\n", "      <td>0</td>\n", "      <td>-1</td>\n", "      <td>2021.333183</td>\n", "      <td>0.892654</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47120</th>\n", "      <td>00000000000000001e81_0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>2019.417089</td>\n", "      <td>0.892657</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>60450</th>\n", "      <td>0000000000000000243f_0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>2018.328919</td>\n", "      <td>0.892716</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>62810</th>\n", "      <td>0000000000000000253d_0</td>\n", "      <td>0</td>\n", "      <td>-1</td>\n", "      <td>2020.249860</td>\n", "      <td>0.892719</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2045 rows × 10 columns</p>\n", "</div>"], "text/plain": ["                 system:index  Classified  GrndTruth  fractional_years  \\\n", "ID                                                                       \n", "98471  00000000000000003487_0           1         -1       2018.834486   \n", "71932  0000000000000000291d_0           1         -1       2021.830784   \n", "58308  0000000000000000233c_0           0         -1       2020.415897   \n", "99766  00000000000000003528_0           1         -1       2021.916612   \n", "74457  00000000000000002a37_0           1         -1       2021.330531   \n", "...                       ...         ...        ...               ...   \n", "89819  000000000000000030da_0           1         -1       2021.833814   \n", "91127  0000000000000000316d_0           0         -1       2021.333183   \n", "47120  00000000000000001e81_0           1         -1       2019.417089   \n", "60450  0000000000000000243f_0           1         -1       2018.328919   \n", "62810  0000000000000000253d_0           0         -1       2020.249860   \n", "\n", "         random  refer  water_this  water_this_wt  water_zou  \\\n", "ID                                                             \n", "98471  0.862859      0           0              0          0   \n", "71932  0.862862      2           1              1          1   \n", "58308  0.862879      0           0              0          0   \n", "99766  0.862901      2           1              1          1   \n", "74457  0.862902      1           0              0          0   \n", "...         ...    ...         ...            ...        ...   \n", "89819  0.892637      2           1              1          1   \n", "91127  0.892654      1           0              0          0   \n", "47120  0.892657      0           0              0          0   \n", "60450  0.892716      0           0              0          0   \n", "62810  0.892719      0           0              0          0   \n", "\n", "                                         .geo  \n", "ID                                             \n", "98471  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "71932  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "58308  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "99766  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "74457  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "...                                       ...  \n", "89819  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "91127  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "47120  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "60450  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "62810  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "\n", "[2045 rows x 10 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df3 = df1\n", "df3"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["df1 = df1[(df1['array'].map(len)%5)!=0]"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/plain": ["'-3124.0, -4028.4602446987437, -4028.0, 6311.0, 6892.233443932476, -2492.0, 3124.0, -4028.4602446987437, 0.0, 1991.835965563832, -3946.0, -5845.388648184723, -5845.0, 7148.0, 8690.463584574007, -3545.0, 3946.0, -5845.388648184723, 0.0, 1994.3318476501458, -3134.0, -3614.310103988825, -3614.0, 7058.0, 6972.418606062446, -2646.0, 3134.0, -3614.310103988825, 0.0, 1997.7483109329337, -3436.0, -6318.50569067866, -6318.0, 7573.0, 8360.964706662311, -3025.0, 3436.0, -6318.50569067866, 0.0, 2000.2462692769998, -3453.0, -5486.787635957487, -5486.0, 6832.0, 7830.960315056043, -2875.0, 3453.0, -5486.787635957487, 1.0, 2004.4949173736654, -2418.0, -4773.6741441428085, -4773.0, 3311.0, 6803.294723294723, -1678.0, 2418.0, -4773.6741441428085, 0.0, 2006.159303985572, -3564.0, -5801.037892881621, -5801.0, 6777.0, 8564.628820960697, -3131.0, 3564.0, -5801.037892881621, 1.0, 2006.2469764166033, -2250.0, -1618.8023459648969, -1618.0, 5587.0, 4303.8822800652315, -1543.0, 2250.0, -1618.8023459648969, 0.0, 2008.8309860839468, -3205.0, -6170.748313354928, -6170.0, 6226.0, 8418.262168027906, -2780.0, 3205.0, -6170.748313354928, 1.0, 2009.7510831861048, -3356.0, -3460.8110343240537, -3460.0, 6655.0, 6720.090644509119, -2560.0, 3356.0, -3460.8110343240537, 1.0, 2011.329164746956, -2987.0, -3410.49723305296, -3410.0, 6073.0, 6364.723242463083, -2368.0, 2987.0, -3410.49723305296, 0.0, 1991.8359663217275, -3947.0, -5873.084841149148, -5873.0, 7152.0, 8699.043398710783, -3547.0, 3947.0, -5873.084841149148, 0.0, 1994.3318484104516, -2738.0, -1933.0296025315897, -1933.0, 5699.0, 4695.499753815854, -1780.0, 2738.0, -1933.0296025315897, 0.0, 1997.6606387116947, -3071.0, -3702.379271945922, -3702.0, 7004.0, 6922.429369250986, -2603.0, 3071.0, -3702.379271945922, 0.0, 1997.748311696252, -3437.0, -6266.671243927977, -6266.0, 7563.0, 8349.045453256049, -3021.0, 3437.0, -6266.671243927977, 0.0, 2000.2462700353863, -3410.0, -5183.549981530668, -5183.0, 6771.0, 7641.435543148636, -2825.0, 3410.0, -5183.549981530668, 1.0, 2004.4949181317675, -2429.0, -4716.555334897996, -4716.0, 3254.0, 6769.619513010066, -1676.0, 2429.0, -4716.555334897996, 0.0, 2006.159304745751, -3566.0, -5835.148660582866, -5835.0, 6770.0, 8585.480144838095, -3134.0, 3566.0, -5835.148660582866, 1.0, 2006.2469771767821, -3154.0, -6134.665660876483, -6134.0, 6155.0, 8380.98447024229, -2730.0, 3154.0, -6134.665660876483, 1.0, 2009.7510839463152, -3319.0, -6243.122310475497, -6243.0, 6286.0, 8697.073638021075, -2945.0, 3319.0, -6243.122310475497, 1.0, 2002.414094325818, -3351.0, -5802.418486343448, -5802.0, 6433.0, 8706.787100518735, -2991.0, 3351.0, -5802.418486343448, 1.0, 2003.246971234811, -4142.0, -4585.500294053208, -4585.0, 6261.0, 8364.453103858854, -3598.0, 4142.0, -4585.500294053208, 1.0, 2004.1670623071304, -1670.0, -6332.030834009157, -6332.0, 5142.0, 6907.459867799811, -1159.0, 1670.0, -6332.030834009157, 1.0, 2010.079853664542, -2849.0, -5856.985306043012, -5856.0, 6988.0, 8191.089529213217, -2472.0, 2849.0, -5856.985306043012, 1.0, 2013.4962992573567, -3053.0, -6027.491123523182, -6027.0, 6027.0, 8440.237498887518, -2653.0, 3053.0, -6027.491123523182, 1.0, 2014.2415074718733, 1241.0, -4728.373186435326, -4728.0, 2138.0, 2429.50734947634, 1718.0, -1241.0, -4728.373186435326, 1.0, 2015.162059029839, 1612.0, -4833.927792074887, -4833.0, 1459.0, 1920.7438324451273, 2216.0, -1612.0, -4833.927792074887, 0.0, 2015.2497306782407, -126.0, -6467.083244665416, -6467.0, 4193.0, 6141.884187494221, 372.0, 126.0, -6467.083244665416, 1.0, 2016.0823868437878, -3132.0, -5023.0274973061405, -5023.0, 6669.0, 7400.47664857078, -2515.0, 3132.0, -5023.0274973061405, 1.0, 2017.0880923893328, -3721.0, -5686.733768015024, -5686.0, 6438.0, 8380.650160928122, -3203.0, 3721.0, -5686.733768015024, 1.0, 2020.2435405197898, -3321.0, -6230.939380015602, -6230.0, 6292.0, 8690.049533350195, -2946.0, 3321.0, -6230.939380015602, 1.0, 2002.4140950901826, -3361.0, -5920.268793931524, -5920.0, 6441.0, 8779.7482422866, -3005.0, 3361.0, -5920.268793931524, 1.0, 2003.246971992897, -4085.0, -4791.414926060188, -4791.0, 6501.0, 8531.922364422273, -3651.0, 4085.0, -4791.414926060188, 1.0, 2004.16706306324, -1672.0, -6308.84999182071, -6308.0, 5150.0, 6897.654648681004, -1164.0, 1672.0, -6308.84999182071, 1.0, 2010.0798544228185, -2843.0, -5841.900697019614, -5841.0, 6974.0, 8178.139542122548, -2466.0, 2843.0, -5841.900697019614, 1.0, 2013.4963000156013, -3159.0, -6040.283428765682, -6040.0, 5980.0, 8416.584778267348, -2726.0, 3159.0, -6040.283428765682, 1.0, 2014.241508230118, 1241.0, -4732.150064755701, -4732.0, 2135.0, 2429.597138654392, 1721.0, -1241.0, -4732.150064755701, 1.0, 2015.1620597880517, 1606.0, -4837.453971593898, -4837.0, 1442.0, 1900.5667611268543, 2222.0, -1606.0, -4837.453971593898, 0.0, 2015.2497314364537, -3644.0, -5506.722346710123, -5506.0, 7245.0, 8038.636677245758, -3064.0, 3644.0, -5506.722346710123, 1.0, 2015.994941264967, -123.0, -6460.316333576402, -6460.0, 4190.0, 6136.645579385898, 372.0, 123.0, -6460.316333576402, 1.0, 2016.0823875998976, -3087.0, -5122.308292510276, -5122.0, 6610.0, 7373.065846634451, -2473.0, 3087.0, -5122.308292510276, 1.0, 2017.0880931475774, -3723.0, -5704.632796993118, -5704.0, 6502.0, 8391.206256043155, -3224.0, 3723.0, -5704.632796993118, 1.0, 2020.2435412758678, -1469.0, -6285.41587542727, -6285.0, 4218.0, 6875.10060730226, -844.0, 1469.0, -6285.41587542727, 1.0, 2014.0880905921804, 80.0, -4650.312481747562, -4650.0, 2481.0, 3663.2567202515393, 978.0, -80.0, -4650.312481747562, 0.0, 2014.7456231185947, -4136.0, -5290.209373243157, -5290.0, 8287.0, 8738.580372528815, -3741.0, 4136.0, -5290.209373243157, 1.0, 2016.497683686406, -4079.0, -5252.919583475532, -5252.0, 8345.0, 8648.744645432409, -3674.0, 4079.0, -5252.919583475532, 1.0, 2017.4168549775177, -4337.0, -6468.483123220822, -6468.0, 6397.0, 9775.357940252585, -3575.0, 4337.0, -6468.483123220822, 0.0, 2020.746318446228, -1462.0, -6275.073263720944, -6275.0, 4169.0, 6822.191068664387, -812.0, 1462.0, -6275.073263720944, 1.0, 2014.0880913501712, 99.0, -4749.784423339765, -4749.0, 2413.0, 3689.618591114345, 1088.0, -99.0, -4749.784423339765, 0.0, 2014.745623876839, -4116.0, -5239.068902876937, -5239.0, 8233.0, 8653.102571286938, -3694.0, 4116.0, -5239.068902876937, 1.0, 2016.4976844424523, -4058.0, -5319.156047628996, -5319.0, 8089.0, 8613.891976090046, -3604.0, 4058.0, -5319.156047628996, 1.0, 2017.4168557357623, -3845.0, -6139.101612653549, -6139.0, 7410.0, 8724.697306995049, -3380.0, 3845.0, -6139.101612653549, 1.0, 2018.9949384269407, -4256.0, -4674.205967276228, -4674.0, 7682.0, 8443.255800730243, -3612.0, 4256.0, -4674.205967276228, 0.0, 2020.7463192024325, -4065.0, -4180.72821762429, -4180.0, 5410.0, 7543.387732132808, -3060.0, 4065.0, -4180.72821762429, 1.0, 2020.8337509091657'"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["df1['array'].iloc[0]"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["df2 = pd.DataFrame(df1['array'].str.split(\",\", expand=True).stack())"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["df2 = pd.DataFrame(df1['array'].str.split(\",\", expand=True).stack())\n", "# name = ['NDWI', 'MND<PERSON>', '<PERSON><PERSON>', 'NDVI', 'BSI', 'LSWI', 'BI', 'DBSI', 'NDSI', 'st', 'AWEIsh', 'water', 'time']\n", "name = ['NDBI', 'NDWI', 'MNDWI', 'E<PERSON>', 'NDVI', 'BSI', 'LSWI', 'NDSI', 'water', 'time']\n", "# name = ['water_zou', 'water_this', 'water_this_wt' 'water_refer', 'time']\n", "nameList = name * int(len(df2)/len(name))\n", "df2['name'] = nameList\n", "df2.index.names = ['ID', 'number']\n", "df2.columns = ['value', 'name']"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'Column not found: level_1'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "\u001b[1;32mc:\\Users\\<USER>\\Desktop\\Water\\Code\\提取算法探索.ipynb Cell 9\u001b[0m in \u001b[0;36m<cell line: 1>\u001b[1;34m()\u001b[0m\n\u001b[1;32m----> <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E6%8F%90%E5%8F%96%E7%AE%97%E6%B3%95%E6%8E%A2%E7%B4%A2.ipynb#X11sZmlsZQ%3D%3D?line=0'>1</a>\u001b[0m df2\u001b[39m.\u001b[39;49mgroupby(\u001b[39m'\u001b[39;49m\u001b[39mID\u001b[39;49m\u001b[39m'\u001b[39;49m)[\u001b[39m'\u001b[39;49m\u001b[39mlevel_1\u001b[39;49m\u001b[39m'\u001b[39;49m]\u001b[39m.\u001b[39mcount() \u001b[39m%\u001b[39m \u001b[39mlen\u001b[39m(name)\n", "File \u001b[1;32mc:\\Users\\<USER>\\.conda\\envs\\geospatial\\lib\\site-packages\\pandas\\core\\groupby\\generic.py:1338\u001b[0m, in \u001b[0;36mDataFrameGroupBy.__getitem__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   1329\u001b[0m \u001b[39mif\u001b[39;00m \u001b[39misinstance\u001b[39m(key, \u001b[39mtuple\u001b[39m) \u001b[39mand\u001b[39;00m \u001b[39mlen\u001b[39m(key) \u001b[39m>\u001b[39m \u001b[39m1\u001b[39m:\n\u001b[0;32m   1330\u001b[0m     \u001b[39m# if len == 1, then it becomes a SeriesGroupBy and this is actually\u001b[39;00m\n\u001b[0;32m   1331\u001b[0m     \u001b[39m# valid syntax, so don't raise warning\u001b[39;00m\n\u001b[0;32m   1332\u001b[0m     warnings\u001b[39m.\u001b[39mwarn(\n\u001b[0;32m   1333\u001b[0m         \u001b[39m\"\u001b[39m\u001b[39mIndexing with multiple keys (implicitly converted to a tuple \u001b[39m\u001b[39m\"\u001b[39m\n\u001b[0;32m   1334\u001b[0m         \u001b[39m\"\u001b[39m\u001b[39mof keys) will be deprecated, use a list instead.\u001b[39m\u001b[39m\"\u001b[39m,\n\u001b[0;32m   1335\u001b[0m         \u001b[39mFutureWarning\u001b[39;00m,\n\u001b[0;32m   1336\u001b[0m         stacklevel\u001b[39m=\u001b[39mfind_stack_level(),\n\u001b[0;32m   1337\u001b[0m     )\n\u001b[1;32m-> 1338\u001b[0m \u001b[39mreturn\u001b[39;00m \u001b[39msuper\u001b[39;49m()\u001b[39m.\u001b[39;49m\u001b[39m__getitem__\u001b[39;49m(key)\n", "File \u001b[1;32mc:\\Users\\<USER>\\.conda\\envs\\geospatial\\lib\\site-packages\\pandas\\core\\base.py:250\u001b[0m, in \u001b[0;36mSelectionMixin.__getitem__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m    248\u001b[0m \u001b[39melse\u001b[39;00m:\n\u001b[0;32m    249\u001b[0m     \u001b[39mif\u001b[39;00m key \u001b[39mnot\u001b[39;00m \u001b[39min\u001b[39;00m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mobj:\n\u001b[1;32m--> 250\u001b[0m         \u001b[39mraise\u001b[39;00m \u001b[39mKeyError\u001b[39;00m(\u001b[39mf\u001b[39m\u001b[39m\"\u001b[39m\u001b[39mColumn not found: \u001b[39m\u001b[39m{\u001b[39;00mkey\u001b[39m}\u001b[39;00m\u001b[39m\"\u001b[39m)\n\u001b[0;32m    251\u001b[0m     subset \u001b[39m=\u001b[39m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mobj[key]\n\u001b[0;32m    252\u001b[0m     ndim \u001b[39m=\u001b[39m subset\u001b[39m.\u001b[39mndim\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON>r\u001b[0m: 'Column not found: level_1'"]}], "source": ["df2.groupby('ID')['level_1'].count() % len(name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(df2)/5"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 1351/1351 [01:15<00:00, 17.90it/s]\n"]}], "source": ["import random\n", "from parfor import parfor\n", "from joblib import Parallel, delayed\n", "\n", "df2 = df2.reset_index()\n", "df2 = df2.set_index('ID')\n", "number_arr = np.array([])\n", "new_df = pd.DataFrame([])\n", "for point_id in tqdm(df1.index):\n", "    grouped = pd.DataFrame(df2.groupby(['ID', 'name'])['value'].count())\n", "    count = grouped.loc[(point_id), 'value'][0]\n", "    number_arr =  np.array(list([i]*len(name) for i in range(count))).flatten()\n", "    df2.loc[point_id, 'number'] = number_arr\n", "    "]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 1351/1351 [00:02<00:00, 459.92it/s]\n"]}], "source": ["df2 = df2.reset_index()\n", "for point_id in tqdm(df1.index):\n", "    # 随机选取一行数据\n", "    random_index = random.randint(0, count - 1)\n", "    sample_data = df2.loc[(df2['ID']==point_id) & (df2['number']==random_index)]\n", "    \n", "    # 将选取的数据加入新数据列表\n", "    new_df = pd.concat([new_df, sample_data], axis=0)\n", "    \n", "# res = Parallel(n_jobs=10)(delayed(fun)(i) for i in df1.index)\n", "# 将新数据转化为DataFrame格式并保存为CSV文件\n", "# new_df = pd.DataFrame(res)\n", "# new_df.to_csv('new_data.csv', index=False)"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["df3 = new_df.pivot(index='ID', columns='name', values='value').astype('float')"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'fractional_years'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "File \u001b[1;32mc:\\Users\\<USER>\\.conda\\envs\\geospatial\\lib\\site-packages\\pandas\\core\\indexes\\base.py:3629\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key, method, tolerance)\u001b[0m\n\u001b[0;32m   3628\u001b[0m \u001b[39mtry\u001b[39;00m:\n\u001b[1;32m-> 3629\u001b[0m     \u001b[39mreturn\u001b[39;00m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_engine\u001b[39m.\u001b[39;49mget_loc(casted_key)\n\u001b[0;32m   3630\u001b[0m \u001b[39mexcept\u001b[39;00m \u001b[39mKeyError\u001b[39;00m \u001b[39mas\u001b[39;00m err:\n", "File \u001b[1;32mc:\\Users\\<USER>\\.conda\\envs\\geospatial\\lib\\site-packages\\pandas\\_libs\\index.pyx:136\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mc:\\Users\\<USER>\\.conda\\envs\\geospatial\\lib\\site-packages\\pandas\\_libs\\index.pyx:163\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\_libs\\hashtable_class_helper.pxi:5198\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\_libs\\hashtable_class_helper.pxi:5206\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON>r\u001b[0m: 'fractional_years'", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "\u001b[1;32mc:\\Users\\<USER>\\Desktop\\Water\\Code\\提取算法探索.ipynb Cell 14\u001b[0m in \u001b[0;36m<cell line: 3>\u001b[1;34m()\u001b[0m\n\u001b[0;32m      <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E6%8F%90%E5%8F%96%E7%AE%97%E6%B3%95%E6%8E%A2%E7%B4%A2.ipynb#X16sZmlsZQ%3D%3D?line=0'>1</a>\u001b[0m \u001b[39m# 时间均匀分布展示\u001b[39;00m\n\u001b[0;32m      <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E6%8F%90%E5%8F%96%E7%AE%97%E6%B3%95%E6%8E%A2%E7%B4%A2.ipynb#X16sZmlsZQ%3D%3D?line=1'>2</a>\u001b[0m \u001b[39m# plt.scatter(df3.index, df3['time'])\u001b[39;00m\n\u001b[1;32m----> <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E6%8F%90%E5%8F%96%E7%AE%97%E6%B3%95%E6%8E%A2%E7%B4%A2.ipynb#X16sZmlsZQ%3D%3D?line=2'>3</a>\u001b[0m plt\u001b[39m.\u001b[39mscatter(df3\u001b[39m.\u001b[39mindex, df3[\u001b[39m'\u001b[39;49m\u001b[39mfractional_years\u001b[39;49m\u001b[39m'\u001b[39;49m])\n", "File \u001b[1;32mc:\\Users\\<USER>\\.conda\\envs\\geospatial\\lib\\site-packages\\pandas\\core\\frame.py:3505\u001b[0m, in \u001b[0;36mDataFrame.__getitem__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3503\u001b[0m \u001b[39mif\u001b[39;00m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mcolumns\u001b[39m.\u001b[39mnlevels \u001b[39m>\u001b[39m \u001b[39m1\u001b[39m:\n\u001b[0;32m   3504\u001b[0m     \u001b[39mreturn\u001b[39;00m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_getitem_multilevel(key)\n\u001b[1;32m-> 3505\u001b[0m indexer \u001b[39m=\u001b[39m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mcolumns\u001b[39m.\u001b[39;49mget_loc(key)\n\u001b[0;32m   3506\u001b[0m \u001b[39mif\u001b[39;00m is_integer(indexer):\n\u001b[0;32m   3507\u001b[0m     indexer \u001b[39m=\u001b[39m [indexer]\n", "File \u001b[1;32mc:\\Users\\<USER>\\.conda\\envs\\geospatial\\lib\\site-packages\\pandas\\core\\indexes\\base.py:3631\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key, method, tolerance)\u001b[0m\n\u001b[0;32m   3629\u001b[0m     \u001b[39mreturn\u001b[39;00m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_engine\u001b[39m.\u001b[39mget_loc(casted_key)\n\u001b[0;32m   3630\u001b[0m \u001b[39mexcept\u001b[39;00m \u001b[39mKeyError\u001b[39;00m \u001b[39mas\u001b[39;00m err:\n\u001b[1;32m-> 3631\u001b[0m     \u001b[39mraise\u001b[39;00m \u001b[39mKeyError\u001b[39;00m(key) \u001b[39mfrom\u001b[39;00m \u001b[39merr\u001b[39;00m\n\u001b[0;32m   3632\u001b[0m \u001b[39mexcept\u001b[39;00m \u001b[39mTypeError\u001b[39;00m:\n\u001b[0;32m   3633\u001b[0m     \u001b[39m# If we have a listlike key, _check_indexing_error will raise\u001b[39;00m\n\u001b[0;32m   3634\u001b[0m     \u001b[39m#  InvalidIndexError. Otherwise we fall through and re-raise\u001b[39;00m\n\u001b[0;32m   3635\u001b[0m     \u001b[39m#  the TypeError.\u001b[39;00m\n\u001b[0;32m   3636\u001b[0m     \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_check_indexing_error(key)\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON>r\u001b[0m: 'fractional_years'"]}], "source": ["# 时间均匀分布展示\n", "# plt.scatter(df3.index, df3['time'])\n", "plt.scatter(df3.index, df3['fractional_years'])"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["df3 = df3.loc[:, ['water_this', 'water_this_wt', 'water_zou', 'refer', 'fractional_years']]"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [], "source": ["df3 = df3[df3<10000]\n", "df3 = df3[df3>-10000]"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["# 2: water, 1: nonWater\n", "water = df3[df3['water']==2]\n", "nonWater = df3[df3['water']==1]"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["# MNDWI, EVI, NDVI, BSI, LSWI, BI, DBSI\n", "def displot(data1, data2):\n", "    sns.distplot(data1, bins=1000, kde=True, label='water')\n", "    sns.distplot(data2, bins=1000, kde=True, label='nonWater')\n", "    plt.legend()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\geospatial\\lib\\site-packages\\seaborn\\distributions.py:2619: FutureWarning: `distplot` is a deprecated function and will be removed in a future version. Please adapt your code to use either `displot` (a figure-level function with similar flexibility) or `histplot` (an axes-level function for histograms).\n", "  warnings.warn(msg, FutureWarning)\n", "c:\\Users\\<USER>\\.conda\\envs\\geospatial\\lib\\site-packages\\seaborn\\distributions.py:2619: FutureWarning: `distplot` is a deprecated function and will be removed in a future version. Please adapt your code to use either `displot` (a figure-level function with similar flexibility) or `histplot` (an axes-level function for histograms).\n", "  warnings.warn(msg, FutureWarning)\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAlIAAAGwCAYAAABiu4tnAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjUuMywgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/NK7nSAAAACXBIWXMAAA9hAAAPYQGoP6dpAABcIUlEQVR4nO3deVyVZf7/8dfhcM5hXwQFURHcxSUVc0tFWzSdyrbJaoZWm/w1TanVlDUtU99GbcqxZlKbyaymdcq0vbQSckFzwR2XFMEQRFxYZOfcvz+OnERQ4QgcwPfz8TiPc859rvu6PvcNHt/cq8kwDAMRERERqTMPdxcgIiIi0lwpSImIiIi4SEFKRERExEUKUiIiIiIuUpASERERcZGClIiIiIiLFKREREREXOTp7gJaMrvdzsGDB/H398dkMrm7HBEREakFwzDIz88nIiICD4+zb3NSkGpABw8epEOHDu4uQ0RERFxw4MAB2rdvf9Y2ClINyN/fH3D8IAICAtxcjYiIiNRGXl4eHTp0cP4/fjYKUg2ocndeQECAgpSIiEgzU5vDcnSwuYiIiIiLFKREREREXKQgJSIiIuIiHSMlIiJSBxUVFZSVlbm7DDkPFosFs9lcL30pSImIiNSCYRhkZWVx/Phxd5ci9SAoKIjw8PDzvs6jgpSIiEgtVIaoNm3a4OPjowstN1OGYVBYWEh2djYAbdu2Pa/+FKRERETOoaKiwhmiQkJC3F2OnCdvb28AsrOzadOmzXnt5tPB5iIiIudQeUyUj4+PmyuR+lL5szzf490UpERERGpJu/Najvr6WSpIiYiIiLhIQUpERETERTrYXERExEXvrU1v1PFuHRzZqOPJuWmLlIiIiNTKqFGjmDJlirvLaFIUpERERKRRlZaWuruEeqMgJSIi0kJ9/vnnBAUFYbfbAdi0aRMmk4lHHnnE2ebee+/llltu4ciRI9xyyy20b98eHx8f+vTpw/vvv+9sd8cdd5CYmMjLL7+MyWTCZDKxf/9+AHbs2MH48ePx8/MjLCyM+Ph4cnJynPOOGjWK+++/n2nTphEaGsoVV1zROCugEShIiYiItFAjR44kPz+f5ORkABITEwkNDSUxMdHZJiEhgbi4OIqLi4mNjeWLL75g27Zt/OEPfyA+Pp61a9cC8PLLLzN06FDuueceMjMzyczMpEOHDmRmZhIXF0e/fv1Yv34933zzDYcOHeKmm26qUstbb72Fp6cnq1at4rXXXmu8ldDAdLC5iIhICxUYGEi/fv1ISEggNjaWhIQEpk6dyl//+lfy8/M5ceIEu3fvZtSoUbRr146HH37YOe+f/vQnvvnmGz766CMGDx5MYGAgVqsVHx8fwsPDne3mzZvHgAED+Nvf/uac9sYbb9ChQwd2795Nt27dAOjSpQsvvPBC4y18I9EWKRERkRZs1KhRJCQkYBgGK1asYMKECfTu3ZuVK1eyfPlywsLC6NGjBxUVFTz//PP07duXkJAQ/Pz8WLp0KenpZz8zccOGDSxfvhw/Pz/no0ePHgDs3bvX2W7gwIENupzuoi1SIiIiLdioUaNYsGABmzdvxsPDg5iYGOLi4khMTOTYsWPExcUB8NJLL/GPf/yDOXPm0KdPH3x9fZkyZco5Dwy32+1cffXVzJo1q9pnp94Q2NfXt34XrIlQkBIREWnBKo+TmjNnDnFxcZhMJuLi4pgxYwbHjh3jwQcfBHBurfr9738POALSnj176Nmzp7Mvq9VKRUVFlf4HDBjAokWLiIqKwtPzwosV2rUnIiLSglUeJ/XOO+8watQowBGuNm7c6Dw+ChzHMC1btozVq1eTkpLCvffeS1ZWVpW+oqKiWLt2Lfv37ycnJwe73c4f//hHjh49yi233MJPP/3Evn37WLp0KXfddVe10NUSXXjRUUREpJ40lyuNjx49mo0bNzpDU3BwMDExMRw8eNC5xenJJ58kNTWVsWPH4uPjwx/+8AeuvfZacnNznf08/PDD3H777cTExFBUVERqaipRUVGsWrWKRx99lLFjx1JSUkLHjh258sor8fBo+dtrTIZhGO4uoqXKy8sjMDCQ3NxcAgIC3F2OiIi4qLi4mNTUVKKjo/Hy8nJ3OVIPzvYzrcv/3y0/KoqIiIg0EAUpERERERcpSImIiIi4SEFKRERExEUKUiIiIiIuUpASERERcZGClIiIiIiLFKREREREXKQgJSIiIuIi3SJGRETEVesXNu54A+9s3PFqYf78+TzyyCMcO3bMedPigoICgoODGTJkCCtWrHC2XbFiBSNHjmTXrl1069btrP0mJCQwevRojh07RlBQUEMuwnnRFikRERFx2ejRoykoKGD9+vXOaStWrCA8PJx169ZRWFjonJ6QkEBERMQ5Q1R9MgyD8vLyButfQUpERKQFGzVqFA888AB//vOfadWqFeHh4TzzzDPOz9PT05kwYQJ+fn4EBARw0003cejQIefnzzzzDP369eO///0vUVFRBAYGcvPNN5Ofnw9A9+7diYiIICEhwTlPQkICEyZMoHPnzqxevbrK9NGjRwPwzjvvMHDgQPz9/QkPD+fWW28lOzsbgP379zvbBQcHYzKZuOOOOwBHMHrhhRfo1KkT3t7eXHTRRXz88cdVxjCZTHz77bcMHDgQm81WZatYfVOQEhERaeHeeustfH19Wbt2LS+88ALPPvssy5YtwzAMrr32Wo4ePUpiYiLLli1j7969TJw4scr8e/fuZcmSJXzxxRd88cUXJCYmMnPmTOfno0aNYvny5c73y5cvZ9SoUcTFxTmnl5aWkpSU5AxIpaWlPPfcc2zevJklS5aQmprqDEsdOnRg0aJFAOzatYvMzExefvllAP7yl7+wcOFC5s2bx/bt25k6dSq///3vSUxMrFLzn//8Z2bMmEFKSgp9+/at3xV6Ch0jJSIi0sL17duXp59+GoCuXbvyr3/9i++//x6ALVu2kJqaSocOHQD473//S69evVi3bh0XX3wxAHa7nTfffBN/f38A4uPj+f7773n++ecBR5CaOnUq5eXlFBUVkZyczMiRI6moqOCVV14BYM2aNRQVFTmD1F133eWsr1OnTrzyyisMGjSIgoIC/Pz8aNWqFQBt2rRxHiN14sQJZs+ezQ8//MDQoUOd865cuZLXXnuNuLg4Z5/PPvssV1xxRf2vzNMoSImIiLRwp2+Radu2LdnZ2aSkpNChQwdniAKIiYkhKCiIlJQUZ5CKiopyhqhT5680evRoTpw4wbp16zh27BjdunWjTZs2xMXFER8fz4kTJ0hISCAyMpJOnToBkJyczDPPPMOmTZs4evQodrsdcOxqjImJqXE5duzYQXFxcbWAVFpaSv/+/atMGzhwYF1Xk0sUpERERFo4i8VS5b3JZMJut2MYBiaTqVr706efaf5KXbp0oX379ixfvpxjx445twyFh4cTHR3NqlWrWL58OZdeeing2LI0ZswYxowZwzvvvEPr1q1JT09n7NixlJaWnnE5Ksf88ssvadeuXZXPbDZblfe+vr5n7Kc+KUiJiIhcoGJiYkhPT+fAgQPOrVI7duwgNzeXnj171qmv0aNHk5CQwLFjx3jkkUec0+Pi4vj2229Zs2YNd97puHzDzp07ycnJYebMmc5xTz3rD8BqtQJQUVFRpV6bzUZ6enqV3XjupCAlIiJygbr88svp27cvv/vd75gzZw7l5eXcd999xMXF1XnX2OjRo/njH/9IWVlZlZATFxfH//t//4/i4mLn8VGRkZFYrVb++c9/MnnyZLZt28Zzzz1Xpb+OHTtiMpn44osvGD9+PN7e3vj7+/Pwww8zdepU7HY7w4cPJy8vj9WrV+Pn58ftt99+/iuljnTWnoiIyAXKZDKxZMkSgoODGTlyJJdffjmdOnXiww8/rHNfo0ePpqioiC5duhAWFuacHhcXR35+Pp07d3ZufWrdujVvvvkmH330ETExMcycOZMXX3yxSn/t2rXjr3/9K4899hhhYWHcf//9ADz33HM89dRTzJgxg549ezJ27Fg+//xzoqOjz2NNuM5kGIbhlpEvAHl5eQQGBpKbm0tAQIC7yxERERcVFxeTmppKdHQ0Xl5e7i5H6sHZfqZ1+f9bW6REREREXKQgJSIiIuIiBSkRERERFylIiYiIiLhIQUpERKSWdH5Wy1FfP0sFKRERkXOovLJ3YWGhmyuR+lL5szz9qu11pQtyioiInIPZbCYoKMh5fzkfH58ab60iTZ9hGBQWFpKdnU1QUBBms/m8+lOQEhERqYXw8HCAKjfrleYrKCjI+TM9HwpSIiIitWAymWjbti1t2rShrKzM3eXIebBYLOe9JaqSgpSIiEgdmM3mevtPWJo/HWwuIiIi4iIFKREREREXKUiJiIiIuMjtQWru3LnOOy/HxsayYsWKs7ZPTEwkNjYWLy8vOnXqxPz586u1WbRoETExMdhsNmJiYli8eHGdxy0oKOD++++nffv2eHt707NnT+bNm3d+CysiIiItiluD1IcffsiUKVN44oknSE5OZsSIEYwbN4709PQa26empjJ+/HhGjBhBcnIyjz/+OA888ACLFi1ytklKSmLixInEx8ezefNm4uPjuemmm1i7dm2dxp06dSrffPMN77zzDikpKUydOpU//elPfPrppw23QkRERKRZMRluvN794MGDGTBgQJUtPT179uTaa69lxowZ1do/+uijfPbZZ6SkpDinTZ48mc2bN5OUlATAxIkTycvL4+uvv3a2ufLKKwkODub999+v9bi9e/dm4sSJPPnkk842sbGxjB8/nueee65Wy5eXl0dgYCC5ubkEBATUah4RERFxr7r8/+22LVKlpaVs2LCBMWPGVJk+ZswYVq9eXeM8SUlJ1dqPHTuW9evXO6/pcaY2lX3Wdtzhw4fz2WefkZGRgWEYLF++nN27dzN27NgzLlNJSQl5eXlVHiIiItJyuS1I5eTkUFFRQVhYWJXpYWFhZGVl1ThPVlZWje3Ly8vJyck5a5vKPms77iuvvEJMTAzt27fHarVy5ZVXMnfuXIYPH37GZZoxYwaBgYHOR4cOHc6xFkRERKQ5c/vB5qffq8gwjLPev6im9qdPr02f52rzyiuvsGbNGj777DM2bNjASy+9xH333cd33313xtqmT59Obm6u83HgwIEzthUREZHmz21XNg8NDcVsNlfb+pSdnV1ta1Gl8PDwGtt7enoSEhJy1jaVfdZm3KKiIh5//HEWL17Mb37zGwD69u3Lpk2bePHFF7n88strrM9ms2Gz2Wqz+CIiItICuG2LlNVqJTY2lmXLllWZvmzZMoYNG1bjPEOHDq3WfunSpQwcOBCLxXLWNpV91mbcsrIyysrK8PCounrMZjN2u72OSyoiIiItluFGH3zwgWGxWIwFCxYYO3bsMKZMmWL4+voa+/fvNwzDMB577DEjPj7e2X7fvn2Gj4+PMXXqVGPHjh3GggULDIvFYnz88cfONqtWrTLMZrMxc+ZMIyUlxZg5c6bh6elprFmzptbjGoZhxMXFGb169TKWL19u7Nu3z1i4cKHh5eVlzJ07t9bLl5ubawBGbm7u+awmERERaUR1+f/brUHKMAzj1VdfNTp27GhYrVZjwIABRmJiovOz22+/3YiLi6vSPiEhwejfv79htVqNqKgoY968edX6/Oijj4zu3bsbFovF6NGjh7Fo0aI6jWsYhpGZmWnccccdRkREhOHl5WV0797deOmllwy73V7rZVOQEmlk695wdwUi0gLU5f9vt15HqqXTdaREGtn6hTDwTndXISLNXLO4jpSIiIhIc6cgJSIiIuIiBSkRERERFylIiYiIiLhIQUpERETERQpSIiIiIi5SkBIRERFxkYKUiIiIiIsUpERERERcpCAlIiIi4iIFKREREREXKUiJiIiIuEhBSkRERMRFClIiIiIiLlKQEhEREXGRgpSIiIiIixSkRERERFykICUiIiLiIgUpERERERcpSImIiIi4SEFKRERExEUKUiIiIiIuUpASERERcZGClIiIiIiLFKREREREXKQgJSIiIuIiBSkRERERFylIiYiIiLhIQUpERETERQpSIiIiIi5SkBIRERFxkYKUiIiIiIsUpERERERcpCAlIiIi4iIFKREREREXKUiJiIiIuEhBSkRERMRFClIiIiIiLlKQEhEREXGRgpSIiIiIixSkRERERFykICUiIiLiIgUpERERERcpSImIiIi4SEFKRERExEUKUiIiIiIuUpASERERcZGClIiIiIiLFKRE5MKwfqG7KxCRFkhBSkRERMRFClIiIiIiLlKQEhEREXGRgpSIiIiIixSkRERERFykICUiIiLiIgUpERERERcpSImIiIi4SEFKRERExEUKUiIiIiIuUpASERERcZGClIiIiIiLFKREREREXKQgJSIiIuIiBSkRERERFylIiYiIiLhIQUpERETERW4PUnPnziU6OhovLy9iY2NZsWLFWdsnJiYSGxuLl5cXnTp1Yv78+dXaLFq0iJiYGGw2GzExMSxevNilcVNSUrjmmmsIDAzE39+fIUOGkJ6e7vrCioiISIvi1iD14YcfMmXKFJ544gmSk5MZMWIE48aNO2NYSU1NZfz48YwYMYLk5GQef/xxHnjgARYtWuRsk5SUxMSJE4mPj2fz5s3Ex8dz0003sXbt2jqNu3fvXoYPH06PHj1ISEhg8+bNPPnkk3h5eTXcChERaUTvrdUfhiLny2QYhuGuwQcPHsyAAQOYN2+ec1rPnj259tprmTFjRrX2jz76KJ999hkpKSnOaZMnT2bz5s0kJSUBMHHiRPLy8vj666+dba688kqCg4N5//33az3uzTffjMVi4b///a/Ly5eXl0dgYCC5ubkEBAS43I+I1NL6hTDwzrp/doF6b206tw6OdHcZIk1OXf7/dtsWqdLSUjZs2MCYMWOqTB8zZgyrV6+ucZ6kpKRq7ceOHcv69espKys7a5vKPmszrt1u58svv6Rbt26MHTuWNm3aMHjwYJYsWXLWZSopKSEvL6/KQ0RERFoutwWpnJwcKioqCAsLqzI9LCyMrKysGufJysqqsX15eTk5OTlnbVPZZ23Gzc7OpqCggJkzZ3LllVeydOlSrrvuOq6//noSExPPuEwzZswgMDDQ+ejQoUMt1oSIiIg0V24/2NxkMlV5bxhGtWnnan/69Nr0ebY2drsdgAkTJjB16lT69evHY489xlVXXVXjwe2Vpk+fTm5urvNx4MCBM7YVERGR5s/TXQOHhoZiNpurbX3Kzs6utrWoUnh4eI3tPT09CQkJOWubyj5rM25oaCienp7ExMRUadOzZ09Wrlx5xmWy2WzYbLYzfi4iIiIti9u2SFmtVmJjY1m2bFmV6cuWLWPYsGE1zjN06NBq7ZcuXcrAgQOxWCxnbVPZZ23GtVqtXHzxxezatatKm927d9OxY8c6LqmIiIi0VG7bIgUwbdo04uPjGThwIEOHDuXf//436enpTJ48GXDsKsvIyODtt98GHGfo/etf/2LatGncc889JCUlsWDBAufZeAAPPvggI0eOZNasWUyYMIFPP/2U7777rsqWpHONC/DII48wceJERo4cyejRo/nmm2/4/PPPSUhIaJyVIyIiIk2f4Wavvvqq0bFjR8NqtRoDBgwwEhMTnZ/dfvvtRlxcXJX2CQkJRv/+/Q2r1WpERUUZ8+bNq9bnRx99ZHTv3t2wWCxGjx49jEWLFtVp3EoLFiwwunTpYnh5eRkXXXSRsWTJkjotW25urgEYubm5dZpPRFy07g3XPrtAvbsmzd0liDRJdfn/263XkWrpdB0pkUam60jVia4jJVKzZnEdKREREZHmTkFKRERExEUKUiIiIiIuUpASERERcZGClIiIiIiLFKREREREXKQgJSIiIuIiBSkRERERFylIiYiIiLhIQUpERETERQpSIiIiIi5SkBIRERFxkYKUiIiIiIsUpERERERcpCAlIiIi4iKXglRqamp91yEiIiLS7LgUpLp06cLo0aN55513KC4uru+aRERERJoFl4LU5s2b6d+/Pw899BDh4eHce++9/PTTT/Vdm4iIiEiT5lKQ6t27N7NnzyYjI4OFCxeSlZXF8OHD6dWrF7Nnz+bw4cP1XaeIiIhIk3NeB5t7enpy3XXX8b///Y9Zs2axd+9eHn74Ydq3b89tt91GZmZmfdUpIiIi0uScV5Bav3499913H23btmX27Nk8/PDD7N27lx9++IGMjAwmTJhQX3WKiIiINDmersw0e/ZsFi5cyK5duxg/fjxvv/0248ePx8PDkcuio6N57bXX6NGjR70WKyIiItKUuBSk5s2bx1133cWdd95JeHh4jW0iIyNZsGDBeRUnIlJn6xc6ngfeWfX9ueapbN8cNLd6RVowl4LUsmXLiIyMdG6BqmQYBgcOHCAyMhKr1crtt99eL0WKiIiINEUuHSPVuXNncnJyqk0/evQo0dHR512UiIiISHPgUpAyDKPG6QUFBXh5eZ1XQSIiIiLNRZ127U2bNg0Ak8nEU089hY+Pj/OziooK1q5dS79+/eq1QBEREZGmqk5BKjk5GXBskdq6dStWq9X5mdVq5aKLLuLhhx+u3wpFREREmqg6Banly5cDcOedd/Lyyy8TEBDQIEWJiIiINAcunbW3cGEtTicWERERaeFqHaSuv/563nzzTQICArj++uvP2vaTTz4578JEREREmrpaB6nAwEBMJpPztYiIiMiFrtZB6tTdedq1JyIiIuLidaSKioooLCx0vk9LS2POnDksXbq03goTERERaepcClITJkzg7bffBuD48eMMGjSIl156iQkTJjBv3rx6LVBERESkqXIpSG3cuJERI0YA8PHHHxMeHk5aWhpvv/02r7zySr0WKCIiItJUuRSkCgsL8ff3B2Dp0qVcf/31eHh4MGTIENLS0uq1QBEREZGmyqUg1aVLF5YsWcKBAwf49ttvGTNmDADZ2dm6SKeIiIhcMFwKUk899RQPP/wwUVFRDB48mKFDhwKOrVP9+/ev1wJFREREmiqXrmx+4403Mnz4cDIzM7nooouc0y+77DKuu+66eitOREREpClzKUgBhIeHEx4eXmXaoEGDzrsgERERkebCpSB14sQJZs6cyffff092djZ2u73K5/v27auX4kRERESaMpeC1KRJk0hMTCQ+Pp62bds6bx0jIiIiciFxKUh9/fXXfPnll1xyySX1XY+IiIhIs+HSWXvBwcG0atWqvmsRERERaVZcClLPPfccTz31VJX77YmIiIhcaFzatffSSy+xd+9ewsLCiIqKwmKxVPl848aN9VKciIiISFPmUpC69tpr67kMERERkebHpSD19NNP13cdIiIiIs2OS8dIARw/fpzXX3+d6dOnc/ToUcCxSy8jI6PeihMRERFpylzaIrVlyxYuv/xyAgMD2b9/P/fccw+tWrVi8eLFpKWl8fbbb9d3nSIiIiJNjktbpKZNm8Ydd9zBnj178PLyck4fN24cP/74Y70VJyIiItKUuRSk1q1bx7333lttert27cjKyjrvokRERESaA5eClJeXF3l5edWm79q1i9atW593USIiIiLNgUtBasKECTz77LOUlZUBYDKZSE9P57HHHuOGG26o1wJFREREmiqXgtSLL77I4cOHadOmDUVFRcTFxdGlSxf8/f15/vnn67tGERERkSbJpbP2AgICWLlyJcuXL2fDhg3Y7XYGDBjA5ZdfXt/1iYiIiDRZdQ5SdrudN998k08++YT9+/djMpmIjo4mPDwcwzAwmUwNUaeIiIhIk1OnXXuGYXDNNdcwadIkMjIy6NOnD7169SItLY077riD6667rqHqFBEREWly6rRF6s033+THH3/k+++/Z/To0VU+++GHH7j22mt5++23ue222+q1SBEREZGmqE5bpN5//30ef/zxaiEK4NJLL+Wxxx7j3XffrbfiRERERJqyOgWpLVu2cOWVV57x83HjxrF58+bzLkpERESkOahTkDp69ChhYWFn/DwsLIxjx46dd1EiIiIizUGdglRFRQWenmc+rMpsNlNeXn7eRYmIiIg0B3U62NwwDO644w5sNluNn5eUlNRLUSIiIiLNQZ2C1O23337ONjpjT0RERC4Uddq1t3Dhwlo96mLu3LlER0fj5eVFbGwsK1asOGv7xMREYmNj8fLyolOnTsyfP79am0WLFhETE4PNZiMmJobFixef17j33nsvJpOJOXPm1GnZREREpGVz6V579eXDDz9kypQpPPHEEyQnJzNixAjGjRtHenp6je1TU1MZP348I0aMIDk5mccff5wHHniARYsWOdskJSUxceJE4uPj2bx5M/Hx8dx0002sXbvWpXGXLFnC2rVriYiIqP8VICIiIs2aW4PU7Nmzufvuu5k0aRI9e/Zkzpw5dOjQgXnz5tXYfv78+URGRjJnzhx69uzJpEmTuOuuu3jxxRedbebMmcMVV1zB9OnT6dGjB9OnT+eyyy6rsjWptuNmZGRw//338+6772KxWM65PCUlJeTl5VV5iEjz997amv+4q+95zsd7a9OrjHn6+4Ye+3zaNmatIvXNbUGqtLSUDRs2MGbMmCrTx4wZw+rVq2ucJykpqVr7sWPHsn79esrKys7aprLP2o5rt9uJj4/nkUceoVevXrVaphkzZhAYGOh8dOjQoVbziYiISPPktiCVk5NDRUVFtetShYWFkZWVVeM8WVlZNbYvLy8nJyfnrG0q+6ztuLNmzcLT05MHHnig1ss0ffp0cnNznY8DBw7Uel4RERFpfup01l5DMJlMVd4bhlFt2rnanz69Nn2erc2GDRt4+eWX2bhx41lrOZ3NZjvjpSFERESk5XHbFqnQ0FDMZnO1rU/Z2dlnvHp6eHh4je09PT0JCQk5a5vKPmsz7ooVK8jOziYyMhJPT088PT1JS0vjoYceIioqyuVlFpF6sr5uZwfXat6z9Nk5/SPXx2tpKteTC+tRpCVyW5CyWq3ExsaybNmyKtOXLVvGsGHDapxn6NCh1dovXbqUgQMHOg8GP1Obyj5rM258fDxbtmxh06ZNzkdERASPPPII3377resLLSIiIi2KW3ftTZs2jfj4eAYOHMjQoUP597//TXp6OpMnTwYcxxxlZGTw9ttvAzB58mT+9a9/MW3aNO655x6SkpJYsGAB77//vrPPBx98kJEjRzJr1iwmTJjAp59+ynfffcfKlStrPW5ISIhzC1cli8VCeHg43bt3b+jVIiIiIs2EW4PUxIkTOXLkCM8++yyZmZn07t2br776io4dOwKQmZlZ5dpO0dHRfPXVV0ydOpVXX32ViIgIXnnlFW644QZnm2HDhvHBBx/wl7/8hSeffJLOnTvz4YcfMnjw4FqPKyIiIlIbbj/Y/L777uO+++6r8bM333yz2rS4uDg2btx41j5vvPFGbrzxRpfHrcn+/ftr3VZEREQuDG69IKeIiIhIc6YgJSIiIuIiBSkRERERFylIiYiIiLhIQUpERETERQpSIiIiIi5SkBIRERFxkYKUiIiIiIsUpERERERcpCAlIiIi4iIFKREREREXKUiJiIiIuEhBSkRERMRFClIiIiIiLlKQEhEREXGRgpSIiIiIixSkRERERFykICUiIiLiIgUpERERERcpSImIiIi4SEFKRERExEUKUiIiIiIuUpASERERcZGClIiIiIiLFKREREREXKQgJSIiIuIiBSkRERERFylIiYiIiLhIQUpERETERQpSIiIiIi5SkBIRERFxkYKUiIiIiIsUpERERERcpCAlIiIi4iIFKREREREXKUiJiIiIuEhBSkRERMRFClIiIiIiLlKQEhEREXGRgpSIiIiIixSkRERERFykICUiIiLiIgUpERERERcpSImIiIi4SEFKRERExEUKUiLSeNYvrN00V9rUZb71C13vs5Z9dU7/yLVxz7eu0+evS39na1ubn119rVORZkRBSkRERMRFClIiIiIiLlKQEhEREXGRgpSIiIiIixSkRERERFykICUiIiLiIgUpERERERcpSImIiIi4yNPdBYiINKgTObD7aziyFxJmQKtO0Gk0+LVxd2Ui0gIoSIlIy5WdAuvfAHvZr9MKDsHBjTDgdvfVJSIthnbtiUjLlL721xDVqjMM+SPc9qlji1R5Cax7HfavdHeVItLMKUiJSMtTXgKfTHKEqDYxMOT/QWhX6DQKhtwHbS8Cww7/ux1yM9xdrYg0YwpSItLy7PwSjqeDdzAMuA08TjmKwcMT+v0OAtpBYQ58OQ0Mw321ikizpiAlIi1LwWFIO7nLru9E8PSq3sZshf7x4GGB3d9A1tbGrVFEWgwFKRFpWfZ869ht13UstO5x5nb+4XDJg47XO5aAvbxRyhORlkVBSkRajoLDkLHB8XrUY+duP+Ih8AuDoqOQvqZhaxORFklBSkRajrQVgOE4wLzdgHO3t/rAyEccr/cshbKiBi1PRFoeBSkRaRlKCuDAT47XUSNqP9+A2x0HpZfkwZb/NUxtItJiKUiJSMuw9X9QXgw+odC6e+3n87RC9EjH6zVzdQafiNSJ24PU3LlziY6OxsvLi9jYWFasWHHW9omJicTGxuLl5UWnTp2YP39+tTaLFi0iJiYGm81GTEwMixcvrtO4ZWVlPProo/Tp0wdfX18iIiK47bbbOHjw4PkvsIg0jOR3Hc8dLwFTHb/aOgwBTxsc3gl7v6//2kSkxXJrkPrwww+ZMmUKTzzxBMnJyYwYMYJx48aRnp5eY/vU1FTGjx/PiBEjSE5O5vHHH+eBBx5g0aJFzjZJSUlMnDiR+Ph4Nm/eTHx8PDfddBNr166t9biFhYVs3LiRJ598ko0bN/LJJ5+we/durrnmmoZdISLimpw9kLHeEaDaD6z7/BZvR5gCSHq1fmsTkRbNrUFq9uzZ3H333UyaNImePXsyZ84cOnTowLx582psP3/+fCIjI5kzZw49e/Zk0qRJ3HXXXbz44ovONnPmzOGKK65g+vTp9OjRg+nTp3PZZZcxZ86cWo8bGBjIsmXLuOmmm+jevTtDhgzhn//8Jxs2bDhjyBMRN9ryoeM5tDvY/F3rI3qkI4jt/QEO7ai/2kSkRXNbkCotLWXDhg2MGTOmyvQxY8awevXqGudJSkqq1n7s2LGsX7+esrKys7ap7NOVcQFyc3MxmUwEBQWdsU1JSQl5eXlVHiKne29t9TBe07T67N8dfTQaw/j1IPH2FwOwNvXoWWep8XOfEOhxleP1mrnOya6si8p5Tn+uqc/TPzu1tvfWplfp49T3Z1LTstVUx+mva6rx1GmV/Z7+XFObc413rjbnWsYzrYdzjXHqOjzTGGfrty7z1MUF92+2hXFbkMrJyaGiooKwsLAq08PCwsjKyqpxnqysrBrbl5eXk5OTc9Y2lX26Mm5xcTGPPfYYt956KwEBAWdcphkzZhAYGOh8dOjQ4YxtRaSeZG2B42ng6Q3hvc+vryH3OZ63fgxFx86/NhFp8dx+sLnJZKry3jCMatPO1f706bXps7bjlpWVcfPNN2O325k7d261z081ffp0cnNznY8DBw6ctb2I1IOULxzPXS5z3PrlfEQO4Zh/Vygvgs0fnn9tItLiuS1IhYaGYjabq20Fys7Orra1qFJ4eHiN7T09PQkJCTlrm8o+6zJuWVkZN910E6mpqSxbtuysW6MAbDYbAQEBVR4i0sB2ngxSPa8+/75MJn7ucJPj9fo3dCkEETkntwUpq9VKbGwsy5YtqzJ92bJlDBs2rMZ5hg4dWq390qVLGThwIBaL5axtKvus7biVIWrPnj189913zqAmIk3Ikb2QvQNMZug65tztayE14iqw+ELOLkg783GTIiLg5l1706ZN4/XXX+eNN94gJSWFqVOnkp6ezuTJkwHHrrLbbrvN2X7y5MmkpaUxbdo0UlJSeOONN1iwYAEPP/yws82DDz7I0qVLmTVrFjt37mTWrFl89913TJkypdbjlpeXc+ONN7J+/XreffddKioqyMrKIisri9LS0sZZOSJybju/dDxHDQefVvXSZbnFD/rc6Hiz/o166VNEWi5Pdw4+ceJEjhw5wrPPPktmZia9e/fmq6++omPHjgBkZmZWudxAdHQ0X331FVOnTuXVV18lIiKCV155hRtuuMHZZtiwYXzwwQf85S9/4cknn6Rz5858+OGHDB48uNbj/vLLL3z22WcA9OvXr0rNy5cvZ9SoUQ20RkSkTupzt96pBt4JG9+CHZ9ia3N//fYtIi2KW4MUwH333cd9991X42dvvvlmtWlxcXFs3LjxrH3eeOON3HjjjS6PGxUV5TyIXUSaqPxDv95br8dv6rfviP4QMQAObqRTxqeUerp4bSoRafHcftaeiIhLdn0JGNAuFgIi6r//gXcB0CX9Ix10LiJnpCAlIs3T7qWO5+7jG6b/3teDLRD/ol8IPLGvYcYQkWZPQUpEmp+Kckj90fG6ns7Wq8bqC/1uAaDN0fUNM4aINHsKUiLS/BzbB2UnwC8Mwvs03DixdwIQnL8bcjMabhwRabYUpESk+clOcTx3vgzOcieE89amB4eCYzFhQPJ/G24cEWm2FKREpPk5vNPx3OWyBh/q58iTVzrf8JZjl6KIyCkUpESkecnNgPxMwASdL23w4Q6EXU6Z2QfyD8Kebxt8PBFpXhSkRKR52fu947ldbL1dzfxs7GYrh4P6Od7oSucichoFKRFpXn7+zvHc9YpGGzK71YCTY38PR1MbbVwRafoUpESk+agoh70JjtddLm+0YUusrU7uRjQct44RETlJQUpEmo+M9VCSCxYfx21cGtPJK52z8b9QrpuXi4iDgpSINB+Vu/VadwcPc+OO3W0c+LeFwhzY+Xnjji0iTZaClIg0H84g1bPxxzZ7woDbHa/X/rvxxxeRJklBSkSah4LDcDDZ8bp1d/fUEHsHmK1wYA3sX+WeGkSkSVGQEpHmYd9yx3N4H/AKdE8NAW2h3+8cr1e86J4aRKRJUZASkeahcrdeI56tV6PhU8Bkhr0/QMYG99YiIm6nICUiTZ9hd1zDCdwfpIKjoO/J28asmO3WUkTE/RSkRKTpy81wnC1n9Yf2g9xdDQyfBphg5xeQl+nuakTEjRSkRKTpO5zieO4UB55W99YC0LobxExwvNalEEQuaJ7uLkBEmrD1C2HgnXX/7HzGqanfwzsdz10uqzrPqU6f5/TPa1lH5/SjMPihqu9rqvXSJyHlM8jeAdkpVZp0Tv+oxr5r0jn9IzC3qnkZzsG+biH7C8xsa3U536Ucojx7D0fKOsArK8jMLcYwDOYSQol5N0uSMygoKWdrxnFa5e2koigPo+MlHFv1JniedvPnU5d78EPVa69pWU6ZVrlMndOPOpatvn5PRJogBSkRadqKjsGx/Y7XnS87a9NGFdoFokZAaiLs+BTG/s1xrakGVlBSzvtZXqzYtYFVO0PJLfMATl4WgpNnM+bnOdsfwwyUcDi/BIAdmXmAr+ORto/XaI2PdQdhAV7sb+VLH8tBLirwwDDAZGrwxRFp9hSkRKRp27vccbB56x4Q3NHd1VTVdSz8sh4KsmDDQhh0T4MMU1ZhZ2tGLuv3H2Xv4QLsRgCQBXhg8zDo2S4Yi9lEl7JdhFjKiR09gTV7j+BhMnGFxzq+LIulf2QQS7cfoksbPw7v28SmbIP08iByi8ooLK0gNecE/87xhd3JQCh+5mC6+xUy9se9DCr2pMJokEUTafYUpESkaduz1PHc9Qr31lETqw90HwfbPoblf4Ne14FvaL11X1xWwZp9R3j5+90cyitxTu8TXMblsb0YXrGGvsHlWAZdxXtr0+mcngTA4O5tyDxeDMAAczk7K/y4qm8EeUXl3Do4EgJXsjb1KHsjh3Ct8T2v5g4jK7cI7+N72FLalpSM4xRUmNma60Xu19/xOaUUmwJom7eatoUhePh7Uq5gJQIoSIlIU2a3w55ljtddx7q3ljOJHAppqyA/E77+M9z4xnl3WVRawRurUvnXDz9TVFYBgK/Nk0FRwfTvEMwDQSthYFdYv/K8x/LxhHZB3rQL8ubWTgXQpx9lnz7I0b0bCC1Jw4zd2fbEARs/2PszK3MM201dGZ5VjnfrI3Rt4+fcqyhyoVGQEpGmKzPZcdkDTxtEDnF3NTXzMMNFt8Cql2HbIuh5tctdGYbBiiMBTHkpgcxcxxalUD8b08f14ERJOZ7mhjvR2mQvg7QE+O5pLMW5hFV+YAvAMHlQUVKALyVcbV7D1eY1LK2I5fGDk8g5eBCAj31b0d3HQr7NhM2ug6vkwqEgJSJN1+6Tu/VCe4DZ4t5aziYoEoZPddw25rMHsUXeRok1uE5dHDjhwaOvr2X1/gigmHZB3lzSJYS+7YO4IbY9761Nb5jagbCcJAbumAkn9jkm+LYmza8fxwK60+/W/8O04U027DvC4GGjyV70MK2Pb2KMeQPDrbt5xPoXvj7egbQTnqSdCGbpYbB5BBB3qIwu1nK6RZio25oQaV4UpESk6ao8Piosxr111MaoxyD1R/jlJ7oe+IgdUXfUaja7Af/d682MLb4UVhzBarLz4Jie3D08mk82ZjRoyeaKYkhZxGX7VzgmWP3gyhlQUUbW/uOOaZWn7plM0D6W1IiraNPncgrXvolPSTYvlzzNhMEv4eFh4qM9BttOBJBRaGbpQRtLiWD+foOLtyUR6mdjZLf6O35MpKlQkBKRpqkgGw5udLxu3cO9tdSG2QI3LoB/j8a3MIsuv3zMzx0nnnWW9COF/N+eDmzP9wVgUFQrfue7jgmjXd89WFvexYcYu/oWKPgZgN2RN9OtZ18YcNu5r8EVEMH2Tndx8ZHP8MzewaVbpuE5+A/4RwYzKKqM/+04wUHa8Ol+M/uLvFib6rgm1ZdbM4ls5UNZhZ3ik8d+iTR3urK5iDRNlTcpbnsReDWTI5mDIuHWD6kweRJc8DMDd8wAo+bT277cksn4V1awPd8Xb7PBM/3y+eAPQwj3KmvYGg0D0lbRe9/rBBX8DDZ/lg+cx/peT4DFp9bd2D2sMPBufmkzCk97Caz7D7aSI5hMEOVTwtReJ5gVs58V43J4+uoYokN9MZkg/WghT3+2nZlf7yR+wVo2pB2lqFShSpovbZESkaZp19eO565j3FtHXbUfyN7219P1wP/oeuB/sLQNBP7B+XGpHb7YcpDVe48A0MOvkNeGF9HRrwI8GvYgbc+yAvj4Ltj+CR5ARusRtOs3lkzP4a516GFmVb+/c+lPk2h9fDPdDvwPujxUpUkHXzt3DozG5mnmsp5tePbzHfxyvIjNB46zYk8OAEs8DtIj3J/YjsGUV9hrGkmkydIWKRFpekoLf90i1eMq99bigmMBPdjfdpzjTdK/GLL1L2Cv4GChBxMTgp0hanJcZ57qlu4IUQ3tYDLjVt8E2z8BkwdpYZeTGPsvsPmdV7cVZi9W9P8H2ALxKTnsuMr7GYQFeHFJl1A+/eMlPHRFNx4e0402/jYq7AbbD+bxdlIaQ2f+wNfbMtlzKP+86hJpLNoiJSJNz94foKzQsaus7UVwMPnc8zQx2a0uJiv0EoZue5pOGZ9xtGAvNx97gPRSC14WD/55ywCuiAlj7UdfNmwhhp3u+9+Bpf/Av6IUAiOh9/VkHQ8AU/38LV3s1Rr6/x7WvArpSQRFdgTOfrmKED8btw6OpJWvjczcIjamHWPTgeMcznfczuaKf/zIRR2CuDG2Pdf0jSDQpwmftSkXNG2REpGmJ+Uzx3PPa5r1Dd9S20+Am9+l1GSlVe523jM9wW8DtnP/6K5cERN27g7Ok9+JdEh6ldiUWVBRyoGwS2HyjxAcVf+DhXYls9VgAKIPfgGlJ2o9a9tAb37TN4LHxvXk3/GxxLQNwNPDxOYDx3lyyTYu/tt33P/eRn7cfRi7XZdUl6ZFW6REpGmxl8Oubxyvz+Pilk1Bhd3gLynt2VT8FP+0/JNoj0O8UDaDHb8cgpK/nvdutTMqKyZm7+v0/vk1sBdTZvbGMvY5VtjHcKt3w13V6UDYpbQtTcVakO24bU5w3X5+Zg8TY3qFk1NQypheYXy66SAfrT/Azqx8vtiSyRdbMmkX5E3Ptv7EdmzVQEshUjfaIiUiTUvOHijJBb8waD/I3dW4rKDcg4WrU3lnTTrbjU583+UJjHYDMRl2eu17Hf41ELb874xn9bnEsBNyfCv8ayD9dr+Mp70YQrry1fBPHDdUbuCte4aHBfr9HgMTHEwm8OSlFVwR6mfj7uHRfP3gCL7403BuH9qRAC9PMo4X8V1KNi98s5M7F/7EN9uyKNMB6uJG2iIlIk1L1hbHc4/fgEfz/FvvYLGVWT+3J6vkBL5WM9cPaM+k9tnA76FtXwq2f4tffgZ8cg99ra3A41LocB6hsSSf7vvfodfPr+FVdhyAE15hbO72IMM6eHPC3r5elqtWgiLJChlE2yNr6Zj5DcQMOK/uTCYTvdsF0rtdINPH9+SbbVm8/P0eUnNOsHzXYZbvOkyon40bY9vjYzXX00KI1J6ClIg0HfYKyNrqeN1Md+ut/jmHv+zsyIkKM0E+Fj74wxA2ph3/tUF4X75odR83V3wGq17Gu/ioYzfYzs/hwFpa5Zqg4DD4tT7zIIYBxw/AL+sgczN88xix5Y5785WZvbHEPcQXntdQYfZmmOn7hl3gGmS0HkXbghS8S47CvuUw+J566dfLYuba/u0oLK0gp6CEwtIKPt7wCzkFJcxP3AvAqp9zuGVQJFf2Dq+XMUXORUFKRJqOtNVQWuC4AGfUCHdXU2cf/JTOX5Zso9xupptvITfGxdIjPKBqkALsZhsMewgG3cv+hXcTlbcOCo/AtkV0BXjxY/Bvy2hbNCWWIDjcloHZBdjKcrGVHIWC7fDl1Cp95vpGcTQghpygi7h45CNUNOC9+c6lwmxznCiw6R3HbX6OH6j3MUJPnvX30Jhu/LAzmw9+Sidh12HWph5lbepRAj71JCYikH4dgup9bJFTKUiJSNOx+X3Hc8yEpn2T4tNUGDDrqxT+/aPjpr+XBOcyOSqLA7bBZ5/R5sehkEFExY6B4+mAQdG6d/EuzYH8TNrmZzraZUK30+f18AS/cAjrBZc9yZf7A+h84OP6XjTXtYslb/ePBBSmw9InoOP/NcgwFrMHY3uFM7ZXOHOX/0xZhcH/1h8g43gRa/YdYfwrK4hs5YOvzUy53Y5nM91dLE2XgpSINA2lJ369mONFt7q3ljo4UW7iwbUBfJfpCFFTL+/G4KOf1e24bpOH45IEA+9ky/FABl/zBzi0nTU/rcVanseAMDNbM3IpsQRRaglgWEAOxD36a/AM7wNp7tsCVSOTif1tx9F3779hx6e09r8WiGzQIYN8rNw6OJL7L+3Cqp9zeOGbnezMyif9aCEPfrAJX5snF0cFMyiqFUE+1gatRS4cClIi0jSkfO7YrecTCpFnv5hjU5FZ6MHdqwPZcdyC1dODF397EddcFMHaj86zY5s/RA5hX2YEAAMGR7L1lF11w8zfg8XrPAdpeEVeYdBxKKStJjblBRg7oVHGNXuYGNmtNb8cK+Lynm2Yvngr2zJyOZRXQsKuw/y4+zA92wYQFerD0E4hmJrxtcrE/bSNU0Sahk3vOZ7bX9wsLsK59Zdcrv0hmB3HLYTY7Lx/zxCuuSjC3WU1Pd3Ggy2QVnkpv/6MG1GbAC8u6xHGykcv5ZZBkUSH+mI3YPvBPG79z1p+88pKliRn6BIK4jIFKRFxv6JjkPqj43X7ge6tpRa+2ZbJb19bzaFiM90Cylly6VFiOzbchS6bNZsfxP3Z8fr7Zx03TnYDi9mDPu0CuWdEJx64rCuDo1vhbTGzIzOPKR9uYuQLy/nPj/soLmuE+x5Ki6IgJSLu98s6wHCcqecT4u5qzshuN5i93ZfJ72ykuMxOXFgJi0Yfo4Ovtmac1aA/kOfTEU5kOy5G6mbhAV5M6NeO1Y9dysNjuhHqZyMzt5jnv0ph1jc7ef7LHRw8XuTuMqWZUJASEfcy7HDgJ8fri25xby1nkVtYxt1vreOVFF8A7hgWxYJLcvG36N5v5+RpJbnHwwD0SH0bjqa6uSCHYF8r91/alZWPjmbWDX3o0saPknI7/1mRysgXljPtf5vYfSjf3WVKE6cgJSLudXgXFOaALcBx2YMmKL3IxjWvrmT5rsPYPAxe+u1FPHNNLzz1DVprGW3ioNMozEYZLHvK3eVU4WUxM/HiSJZOGcntQzsypFMryu0Gn2zMYMw/fuTtpP2s23/U3WVKE6WvARFxr/0rHM/9f99wN/E9DyuOBPCXnR1JO1JIuyBvFo0+xg2xjXjLlZbCZIKxM7DjASmf0ebIOndXVI2Hh4nu4QF88IehLPnjJVzZKxyTCXZm5fPb+Um8lriX73Ycwm7XVkj5lYKUiLjPkb2QnQKY4OJJ7q6mivziMqb8FMC/9kdQYvdgRNdQvvjTcHoHl7u7tOYrLIafI38LQGzKLMctgZqofh2CmB8fy3fT4hjYMRir2YO0o4VMens9Y+f8yMcbfqG0XMfGiYKUiLjTqjmAAW1iIKSzu6tx2ph+jPGvrGBJuhcmDG5se5g37xxEsK8u4ni+tnb9I3gFEpy/C5LfcXc559S5tR/XD2jPikdHM7JrKH42T/ZkF/DwR5uJ+/tyXl+xj5LyphsIpeEpSImIe+RmwKaTV+buerl7azmpzA6LMkP47fwkDhwtor1PBX/tns5vI45g9mj617ZqDkqswRD3mOPND89hKWseB3OHBXhxZe+2rHrsUv58ZXfnmX7/92UKL3yzi9lLd3GkoMTdZYobKEiJiHusmgP2MmjVGYKj3V0Nm496cs33wfzvYGsq7AYT+kXw1RVH6e6n0+Dr3cWTyPONghOHuWj3y+6upk4CvS3cN6oLKx8dzd+u60NUiA9FZRW88sPPXDLrB576dBsHjha6u0xpRApSItL4jqbC+oWO193GurWUgpJy/rrJj2t/CCYl14KfuYI5E/vx8s39CdClDRqGp5V1vf4CQLf0D2l9dKObC6o7L4uZWwdH8v1Do7hlUCR92wdSXGbn7aQ0Rr2YwB/f20jS3iMYhn6HWjoFKRFpfMv/5tga1flSCO3mlhIMA77emsmY2Yks/NkHAxPXRRbzj177uLZ/O7fUdCE5FDIY+scDMGjb03hUNM/dYmYPE33aBfLpHy/hvUmDGdE1lAq7wZdbMrnlP2u44h8/snBVKrlFZe4uVRqIgpSINK60JNj6P8fry9xzPaGdBd7csDyY//fuRg7mFtPBt4K3hx/nH4PyCLDowOFGM+Y5imyhBJ7YT++9r7m7mvNiMpkY1iWU/949mK8eGMGtgyPxsZr5ObuAv36+g8F/+44pHySzYs9hKnT5hBbF090FiMgFxF4BXzmucM2A2yCiPxzc1GjD7z1cwAurA/j2YBsAvC1mJo2I5v8FrMZH34aNzzuY9TGPMyJ5GjH73iAzdDgQ6e6qzltMRAB/u64P08f1YHFyBu+sSWP3oQKWbDrIkk0HCQ/w4tr+7ZjQL4Ie4f7uLlfOk746RKTx7P0eDm0DryC47JlGG3bbMU/m7/Lhq0WJ2A3HJQ1uji5myi1XERbgBetXN1otUtWB8CtIjbiK6INfcMmmR2DkCHeXVG/8vSzcNjSK+CEd2XTgOJ9szOCzzQfJyitmfuJe5ifuJSrEh8hWvsREBHBR+0BMJp0d2twoSIlI4ziYDLu/cbwe9wL4NuzNiQ0DVmZbeG2XLyuzf73+0+VtSxgXnMENMX4Q4NWgNUjtrOv1JK1ytxF4Yj8svhe6vOjukuqVyWSif2Qw/SOD+ctVPVm+M5tFGzNI3H2Y/UcK2X+kkB/3HKZtoBdje4VzZe9wBnYMxtOso2+aAwUpEWl4hUfhf7c5blAcMwH63tRgQx0tMfHloWCe3NOK3XmOrzizyeDqDiXce90V9MxYxNrU0gYbX+qu3NOHlf1fYuzqW/H8eRm9eQ2GzHB3WQ3C5mnmyt5tubJ3W06UlJOw6zDzE/ey73ABmbnFvLl6P2+u3k+AlycjurYmrntr4rq1dmw5lSZJQUpEGlZ5CXx0BxxPB58QuGqO475r9TmEHdbsyeGDNQEsPWij1O7o39tsMDG6iEldC2nva4e2AZBRr0NLPcn178b6mCcYsu0p+v48F9Z3hYF3ubusBuVr8+Q3fduSW1TG9QPasernHL7ZlsWylEMcLyzjy62ZfLk1E4CebQOI69aaUd1bE9sxGIu2VjUZClIi0nAqyh27alITweIDA+8Gn1b10nW5HdYctvDlL14sPWjjSMlawPFXe7RPMXd1L+OaDsUEWnWGVHOxr8N1DGmVDz/+Hb6YBt6toNe17i6rUXhZzFzWM4zLeoZRYTfY/MtxEnYdJnH3Ybb8cpyUzDxSMvOYn7gXP5snl3QJYUTX1ozoGkrHEF93l39BU5ASkYZRXgKL7oaUz8HDAhPfcWyVOg9HCkpY+XMOiev8WZ5p41jpr3+VB/lYuCo8l5ujizlxPJvB0fUT2KSRjX6CPan76XrgI1g0CTzM0PNqd1fVqMweJgZEBjMgMphpV3Rz/t4n7DrMj7sPc+REKd9uP8S32w8B0KGVN+EB3gT5WBjWOYQgH90TsjEpSIlI/cvNcBwTlbEezFa4cSF0uezXq5nXUl5xGRvTjvHTVl9WJK1k28FcHBeK9gagldXO2HYljG9fzJArb8WS/BYAa4/X7+JIIzKZWN/rCboGlMP2xY7foyueg6F/rPddws1FiJ+NCf3aMaFfO+x2g20Hc0ncdZgVP+ewMe0YB44WceBoEev2H8Vkgr7tAhneNZRLuoQS2zEYm6fZ3YvQoilIiUj9MQzY/D588xgU54JXINz0X+gUd85Zyyrs7D1cwLb9Xmz6ZSvr9x9j16H8k8HJF8gFHMeKjPTPJC68lEGhZXhWbpTSMSMthmEyw/Wvgy0ANr4FS5+AtFUw4dV62zXcXHl4mOjbPoi+7YP402VdOVFSztrUI7y+IpXD+SXsyS5g8y+5bP4ll1eX78XbYmZQdCtGdA1leNdQuof56xIL9UxBSkTOn90Oe5ZC4iw4ePK+aW37wW/fhFZVb0hcYTc4eLyInw8XsDe7gJ93+LPjuCc7l3xLabkdCAB+3QXYMcSHWL8jXDJoCCO6htImwKvOW7akGTJ7wtUvQ3gf+PZx2PUVzB8OVzwLxkAwKTiD44D1S3uEkZVbwq2DI8nKLWblzzms3HOYlT8fIaeghMTdjmOtAFr72xjeJdTx6BqqswHrgYKUiLimohzS18COzyDlM8g9AIBh9eV47AOkRN/BwdRyDibvITO3iIzjxRzMbMWBJd9QUm4/pSPvk892/G2e9PQvpE+P7gzsGExsVDBt/E8Gp9j2jb6I4mYmEwy6BzoMgo/uhKN7YdHdjA3sxZau94P9ZvBQoDpVeKAXN8a258bY9hiGwa5D+azck8OKPTmsTT3C4fwSFidnsDjZcfpq1zZ+DOscwoCOjmOy2gd7a4tVHSlIibQUhUfh0Ha6pa2C3ALIPwQFWVCQjVF6wnF7Fns52MsxzFYMqx+G1R+71Q+7xY8Kiy92iy8VFj865lTwy9Ewig/toTQzn7LyMuzFBZiLDmMtyiHoxF5CC/fiya/3pSvAhw8qLmV+3m/IWR4IyzfUUKQnYMfq6UGnUF86tfalc9leegaV02vEBDoE++Cx8U0YGNNYa02ag7YXweSVsOZVWDmHkNztjF7//+DnmTAgHmKuhZAu7q6yyTGZTPQID6BHeACTRnSipLyCDWnHWLknh5U/57A1I5c92QXsyS7graQ0wLHFKjYymNiOwQzoGERM20C8rTrG6mzcHqTmzp3L3//+dzIzM+nVqxdz5sxhxIgz3yIgMTGRadOmsX37diIiIvjzn//M5MmTq7RZtGgRTz75JHv37qVz5848//zzXHfddXUa1zAM/vrXv/Lvf/+bY8eOMXjwYF599VV69epVvytAmhTDMKiwG5TbDcoq7JRXGJTZHc+nvi6rsFNuNyivsFNWYVBut1NWYae0/NfXZeWO9mXljjalJ/tLPnCMtCMnnO/LKuzsysonad+Rk/3ZKa0wKCu3n+zLwG44xrcbBh4VJbQvP0BURSrR9jSiK9LobKTRmmMADKxhuU7/+/Jcf29eAr/uXdt/5nbHDD+W2/vxTcXFJNovogTH2ULeFjMRQV5EBHkTEehNRJA3bYO8aHcokfZDrqN9sA9mj5NVrN/ieNYp3HI2Vh8Y+QgMuIOdHz9Dp18+xXo8DX74P8cjqCMX+w8C6yjHbuXW3d1dcZNj8zQzrHMowzqH8mfg2IlSkvYdYd3+o2xMP872jFwO55fwzfYsvtmeBYCHCaJCfekZHkDPtv50C/OnU2tfOrTy0UHsJ7k1SH344YdMmTKFuXPncskll/Daa68xbtw4duzYQWRk9RtXpqamMn78eO655x7eeecdVq1axX333Ufr1q254YYbAEhKSmLixIk899xzXHfddSxevJibbrqJlStXMnjw4FqP+8ILLzB79mzefPNNunXrxv/93/9xxRVXsGvXLvz9dZPJ82EYjlBRWu54lFUYjtcVFZSc+r7cESpKyu3O9mWnzOfso8IRVkrP9Nkp00sq+6j4dezKwFQZWhpDwq7D1aYlHzjufO2BnRByaWc6QmfTQbp6ZNDZlEFXUwYdTNmYTTXXmW5vzS4jkjSjDdlGENlGMNkEUWB4U4EHFZipwAMrZfhRjJ+pEF+KCfQowt9UjJ9HCf4U4W0UEeRZhq9RgMnihcnDE8Nso9AWQrE1lALfSHJLwNJjLMF+Nn7vY+UBXyvBvlZa+VjP/Bfs+jIFJjk/fq3Z2PNRNnd7gIk+Gx0nN6SthuNpdD2eBgc+crQzmbnGKxx2duHi8tZQ3J0u2R4UW1tRYg2mxBoMJ3zA6geetgvyjMBgXyvj+7RlfJ+2ABSXVbDll1w2ph9jQ9oxktOPk1NQwr7DJ9h3+ITz4qDgWF0Rgd5Eh/rSLsibsEAvwgO8CA+00cbfiyAfC0E+Vnyt5ha/q9CtQWr27NncfffdTJo0CYA5c+bw7bffMm/ePGbMqH57gPnz5xMZGcmcOXMA6NmzJ+vXr+fFF190Bqk5c+ZwxRVXMH36dACmT59OYmIic+bM4f3336/VuIZhMGfOHJ544gmuv/56AN566y3CwsJ47733uPfeext0vZzL3sMF7MpynM1kNwwMHOHEMMDAwG6n2jRH219fG875TvZx8hkcz6cGjF+3yNgps1duKakeQEpPCylVw9Kpwab5XSDRYjZhMXvg6XHy2WzC08MDi9mE58npVk8PLGbHtH5lmwgy8rF6VGAx2bGa7FhOPo4VFNEuwBMrZXjbT+BtL6Qo/xhhXmV4lefhU3IY75IcPIzyM9ZTZg2iOLg7xa16UBLSg5JWPagI7YHJ5s+OrVlc2z8CD5MJs4cJTw8THqc8m09ON5987eFR/UvuvbXpXD440nFs0sA7ay5i/UIY2Km+VrFInVSYvaHfLY5HSQGk/kjK2m/oaeyDg5ugNB+/ogxIzaArwAEYdHonK04+e3iC1Res/oy322BLkCNgWX3BbGXo8TLICmbgkTI4FuK4pIenzfnc7UABeLR2HABv8gBMv742mU6bbqo6Parp3KTZ6+QZfoNOuQZbdn4xOzPz2ZmVR0pmPnuy89mfU0hBSTkZx4vIOF501j49PUwEelscDx8LfjZPvCxmx8PT4+Rrx7PF7IHZw4SH6dTvK8d1tU7/7vIwmRyr0GSiaxs/erYNaOjVc+ZldNfApaWlbNiwgccee6zK9DFjxrB6dc13Yk9KSmLMmDFVpo0dO5YFCxZQVlaGxWIhKSmJqVOnVmtTGb5qM25qaipZWVlVxrLZbMTFxbF69eozBqmSkhJKSkqc73NzHadr5+XlnWk1uGTx2r288v3P9dqnO3l6mLB4mrCaK4OIBzbPX187AooJi6cHVrMH1pOhxhlcPB2vbR4eeJo9sHqeGmpq6MvDA4unCZvnyUBkdkzz9DA5A5LnKcHJ7GGq+19UC/8MWZvP/Hn1DVJOFUABAB7g18Zx1ltoNwjp6jgOJLQb+LUGkwkbYKsyt4GvRynBloqqndpPPk72f9qn1RSeyHf83hYUwZl+f8/22ZnUNE/ByS/iyulnanPqtIIavrwr6wVOFBaTd5Y2zs9PHfOkE4XFv/6bPdnWMWsN9Z06xin1Vc5TuR4LT+ST5/Fr20J7vrO/KrXm5VUZv/BEvnPsyteAo69TlpeTn59aq3NejyLneKf+XE8UFjvrKrTnV+u3ynJXrrPK+k5Zh6fOe/pnp67nKuvitOeqP6JT6jyDU9eLU8RwVnXoSLuBHRx/IeYf4oe167k0rJDtO7bSK6CYXzJ+wavsOLbSY9hKj2OtKDg5cxkUHQeO4wHkHak6XgiQlwrhQN6e6vV0A/I2nbHcs4v/lMITbc77/4lzrTNXeQH9wm30C28N/VoDjj/EjxSUkn70BGlHCjmUV8Kh/GKy84rJyivhSEEJuUXljj+ggcNFZ/3KO2+Thkcz5Ypu9dpn5bo0jFr84W+4SUZGhgEYq1atqjL9+eefN7p161bjPF27djWef/75KtNWrVplAMbBgwcNwzAMi8VivPvuu1XavPvuu4bVaq31uJV9ZmRkVGlzzz33GGPGjDnjMj399NMGJzcG6aGHHnrooYcezftx4MCBM/6fX8ntB5uf/pe+YRhn/eu/pvanT69Nn/XV5lTTp09n2rRpzvd2u52jR48SEhLS4vcR16e8vDw6dOjAgQMHCAhw3+balkTrtGFovdY/rdOGofVaN4ZhkJ+fT0RExDnbui1IhYaGYjabycrKqjI9OzubsLCwGucJDw+vsb2npychISFnbVPZZ23GDQ8PByArK4u2bdvWqjZw7P6z2aruaAkKCjpjezm7gIAA/YOvZ1qnDUPrtf5pnTYMrdfaCwwMrFU7t13JzGq1Ehsby7Jly6pMX7ZsGcOGDatxnqFDh1Zrv3TpUgYOHIjFYjlrm8o+azNudHQ04eHhVdqUlpaSmJh4xtpERETkAnTOnX8N6IMPPjAsFouxYMECY8eOHcaUKVMMX19fY//+/YZhGMZjjz1mxMfHO9vv27fP8PHxMaZOnWrs2LHDWLBggWGxWIyPP/7Y2WbVqlWG2Ww2Zs6caaSkpBgzZ840PD09jTVr1tR6XMMwjJkzZxqBgYHGJ598YmzdutW45ZZbjLZt2xp5eXmNsGYubLm5uQZg5ObmuruUFkPrtGFovdY/rdOGofXacNwapAzDMF599VWjY8eOhtVqNQYMGGAkJiY6P7v99tuNuLi4Ku0TEhKM/v37G1ar1YiKijLmzZtXrc+PPvrI6N69u2GxWIwePXoYixYtqtO4hmEYdrvdePrpp43w8HDDZrMZI0eONLZu3Vo/Cy1nVVxcbDz99NNGcXGxu0tpMbROG4bWa/3TOm0YWq8Nx2QYtTm3T0REREROp7s9ioiIiLhIQUpERETERQpSIiIiIi5SkBIRERFxkYKUNIjnn3+eYcOG4ePjc8aLkqanp3P11Vfj6+tLaGgoDzzwAKWlpVXabN26lbi4OLy9vWnXrh3PPvtstXsfJSYmEhsbi5eXF506dWL+/PnVxlq0aBExMTHYbDZiYmJYvHhxvS2rO0VFRWEymao8Tr+PZGOu5wvJ3LlziY6OxsvLi9jYWFasWHHumS4QzzzzTLXfy8oLHYPjqtHPPPMMEREReHt7M2rUKLZv316lj5KSEv70pz8RGhqKr68v11xzDb/88kuVNseOHSM+Pp7AwEACAwOJj4/n+PHjjbGIDe7HH3/k6quvJiIiApPJxJIlS6p83pjrsDbfIRc0d54yKC3XU089ZcyePduYNm2aERgYWO3z8vJyo3fv3sbo0aONjRs3GsuWLTMiIiKM+++/39kmNzfXCAsLM26++WZj69atxqJFiwx/f3/jxRdfdLapvLbYgw8+aOzYscP4z3/+U+3aYqtXrzbMZrPxt7/9zUhJSTH+9re/Vbu2WHPVsWNH49lnnzUyMzOdj/z8fOfnjbmeLySV16L7z3/+Y+zYscN48MEHDV9fXyMtLc3dpTUJTz/9tNGrV68qv5fZ2dnOz2fOnGn4+/sbixYtMrZu3WpMnDix2nX6Jk+ebLRr185YtmyZsXHjRmP06NHGRRddZJSXlzvbXHnllUbv3r2N1atXG6tXrzZ69+5tXHXVVY26rA3lq6++Mp544glj0aJFBmAsXry4yueNtQ5r8x1yoVOQkga1cOHCGoPUV199ZXh4eFS5MfT7779v2Gw25wXj5s6dawQGBla57smMGTOMiIgIw263G4ZhGH/+85+NHj16VOn73nvvNYYMGeJ8f9NNNxlXXnlllTZjx441br755vNePnfr2LGj8Y9//OOMnzfmer6QDBo0yJg8eXKVaT169DAee+wxN1XUtDz99NPGRRddVONndrvdCA8PN2bOnOmcVlxcbAQGBhrz5883DMMwjh8/blgsFuODDz5wtsnIyDA8PDyMb775xjAMw9ixY4cBVPmDKCkpyQCMnTt3NsBSuc/pQaox12FtvkMudNq1J26RlJRE7969q9wQcuzYsZSUlLBhwwZnm7i4uCr3Lxw7diwHDx5k//79zjZjxoyp0vfYsWNZv349ZWVlZ22zevXqhli0Rjdr1ixCQkLo168fzz//fJVN7o25ni8UpaWlbNiwodr6GDNmTIv5naoPe/bsISIigujoaG6++Wb27dsHQGpqKllZWVXWn81mIy4uzrn+NmzYQFlZWZU2ERER9O7d29kmKSmJwMBABg8e7GwzZMgQAgMDW/zPoTHXYW2+Qy50ClLiFllZWdVuAB0cHIzVanXeULqmNpXvz9WmvLycnJycs7Y5/cbVzdGDDz7IBx98wPLly7n//vuZM2cO9913n/PzxlzPF4qcnBwqKipa7O9UfRg8eDBvv/023377Lf/5z3/Iyspi2LBhHDlyxLmOzrb+srKysFqtBAcHn7VNmzZtqo3dpk2bFv9zaMx1WJvvkAudgpTUWk0HkJ7+WL9+fa37M5lM1aYZhlFl+ultjJMHQNdHm5rGbwrqsp6nTp1KXFwcffv2ZdKkScyfP58FCxZw5MgRZ3+NuZ4vJM3pd6qxjRs3jhtuuIE+ffpw+eWX8+WXXwLw1ltvOdu4sv7O9Xtb235aisZahxf6ej4XT3cXIM3H/fffz80333zWNlFRUbXqKzw8nLVr11aZduzYMcrKypx//YSHh1f7iyc7OxvgnG08PT0JCQk5a5vT/8pqKs5nPQ8ZMgSAn3/+mZCQkEZdzxeK0NBQzGZzs/qdcjdfX1/69OnDnj17uPbaawHHlo62bds625y6/sLDwyktLeXYsWNVtqhkZ2czbNgwZ5tDhw5VG+vw4cMt/udQeQZkY6zD2nyHXOi0RUpqLTQ0lB49epz14eXlVau+hg4dyrZt28jMzHROW7p0KTabjdjYWGebH3/8scoxP0uXLiUiIsIZJIYOHcqyZcuq9L106VIGDhyIxWI5a5vKL5Om5nzWc3JyMoDzy7Ux1/OFwmq1EhsbW219LFu2rMn+TrlbSUkJKSkptG3blujoaMLDw6usv9LSUhITE53rLzY2FovFUqVNZmYm27Ztc7YZOnQoubm5/PTTT842a9euJTc3t8X/HBpzHdbmO+SC54YD3OUCkJaWZiQnJxt//etfDT8/PyM5OdlITk52nppfeUrtZZddZmzcuNH47rvvjPbt21c5pfb48eNGWFiYccsttxhbt241PvnkEyMgIKDG0/KnTp1q7Nixw1iwYEG10/JXrVplmM1mY+bMmUZKSooxc+bMFnH5g9WrVxuzZ882kpOTjX379hkffvihERERYVxzzTXONo25ni8klZc/WLBggbFjxw5jypQphq+vr7F//353l9YkPPTQQ0ZCQoKxb98+Y82aNcZVV11l+Pv7O9fPzJkzjcDAQOOTTz4xtm7datxyyy01nrrfvn1747vvvjM2btxoXHrppTWeut+3b18jKSnJSEpKMvr06dNiLn+Qn5/v/N4EnP/WKy+x0VjrsDbfIRc6BSlpELfffrsBVHssX77c2SYtLc34zW9+Y3h7exutWrUy7r///iqn4BuGYWzZssUYMWKEYbPZjPDwcOOZZ55xnpJfKSEhwejfv79htVqNqKgoY968edXq+eijj4zu3bsbFovF6NGjh7Fo0aIGWe7GtGHDBmPw4MFGYGCg4eXlZXTv3t14+umnjRMnTlRp15jr+ULy6quvGh07djSsVqsxYMAAIzEx0d0lNRmV1zSyWCxGRESEcf311xvbt293fm63242nn37aCA8PN2w2mzFy5Ehj69atVfooKioy7r//fqNVq1aGt7e3cdVVVxnp6elV2hw5csT43e9+Z/j7+xv+/v7G7373O+PYsWONsYgNbvny5TV+h95+++2GYTTuOqzNd8iFzGQYp12+WERERERqRcdIiYiIiLhIQUpERETERQpSIiIiIi5SkBIRERFxkYKUiIiIiIsUpERERERcpCAlIiIi4iIFKREREREXKUiJiIiIuEhBSkQuWHfccQfXXnttjZ8lJydz1VVX0aZNG7y8vIiKimLixInk5OQAjhtDz5o1q8o8jz76KCaTie+//77K9Msuu4xbb70VgDfffJOgoKB6XxYRcQ8FKRGR02RnZ3P55ZcTGhrKt99+S0pKCm+88QZt27alsLAQgFGjRrF8+fIq8yUkJNChQ4cq00tLS0lKSmL06NGNugwi0jg83V2AiEhTs3r1avLy8nj99dfx9HR8TUZHR3PppZc624wePZqHHnqI8vJyPD09yc/PJzk5mTlz5vDee+85261du5aioiIFKZEWSlukREROEx4eTnl5OYsXL+ZM93UfPXo0BQUFrFu3DoAVK1bQrVs3brzxRtatW+fccrV8+XLat29Ply5dGq1+EWk8ClIiIqcZMmQIjz/+OLfeeiuhoaGMGzeOv//97xw6dMjZpmvXrrRr146EhATAsVsvLi6ONm3a0KlTJ1atWuWcrq1RIi2XgpSISA2ef/55srKymD9/PjExMcyfP58ePXqwdetWZ5tRo0ZVCVKjRo0CIC4ujoSEBEpKSlizZk2VXYIi0rIoSImInEFISAi//e1veemll0hJSSEiIoIXX3zR+fno0aNZtWoVR44cITk5mZEjRwKOILV8+XLWrFmj46NEWjgFKRGRWrBarXTu3JkTJ044p40ePZoTJ04we/ZsunbtSlhYGOAIUuvXr+fLL78kOjqajh07uqtsEWlgOmtPRC5oubm5bNq0qcq0LVu2sHTpUm6++Wa6deuGYRh8/vnnfPXVVyxcuNDZrlOnTkRGRvLPf/6T3/3ud87pERERdOzYkfnz5/Pb3/62sRZFRNxAQUpELmgJCQn079+/yrT4+Hh8fHx46KGHOHDgADabja5du/L6668THx9fpe3o0aN56623nMdHVYqLi2PBggXarSfSwpmMM53bKyIiIiJnpWOkRERERFykICUiIiLiIgUpERERERcpSImIiIi4SEFKRERExEUKUiIiIiIuUpASERERcZGClIiIiIiLFKREREREXKQgJSIiIuIiBSkRERERF/1/AQtvTbRt+68AAAAASUVORK5CYII=", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sub1 = water['LSWI']\n", "sub2 = nonWater['LSWI']\n", "displot(sub1, sub2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plt.plot(np.sort(sub1))\n", "plt.plot(np.sort(sub2))\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\geospatial\\lib\\site-packages\\seaborn\\distributions.py:2619: FutureWarning: `distplot` is a deprecated function and will be removed in a future version. Please adapt your code to use either `displot` (a figure-level function with similar flexibility) or `histplot` (an axes-level function for histograms).\n", "  warnings.warn(msg, FutureWarning)\n", "c:\\Users\\<USER>\\.conda\\envs\\geospatial\\lib\\site-packages\\seaborn\\distributions.py:2619: FutureWarning: `distplot` is a deprecated function and will be removed in a future version. Please adapt your code to use either `displot` (a figure-level function with similar flexibility) or `histplot` (an axes-level function for histograms).\n", "  warnings.warn(msg, FutureWarning)\n"]}, {"data": {"image/png": "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*******************************/*******************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sub1 = water['MNDWI']-water['NDVI']\n", "sub2 = nonWater['MNDWI']-nonWater['NDVI']\n", "plt.xlabel('MNDWI - NDVI')\n", "displot(sub1, sub2)"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\geospatial\\lib\\site-packages\\seaborn\\distributions.py:2619: FutureWarning: `distplot` is a deprecated function and will be removed in a future version. Please adapt your code to use either `displot` (a figure-level function with similar flexibility) or `histplot` (an axes-level function for histograms).\n", "  warnings.warn(msg, FutureWarning)\n", "c:\\Users\\<USER>\\.conda\\envs\\geospatial\\lib\\site-packages\\seaborn\\distributions.py:2619: FutureWarning: `distplot` is a deprecated function and will be removed in a future version. Please adapt your code to use either `displot` (a figure-level function with similar flexibility) or `histplot` (an axes-level function for histograms).\n", "  warnings.warn(msg, FutureWarning)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sub1 = water['MNDWI']-water['EVI']\n", "sub2 = nonWater['MNDWI']-nonWater['EVI']\n", "# plt.xlabel('MNDWI - EVI')\n", "displot(sub1, sub2)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\geospatial\\lib\\site-packages\\seaborn\\distributions.py:2619: FutureWarning: `distplot` is a deprecated function and will be removed in a future version. Please adapt your code to use either `displot` (a figure-level function with similar flexibility) or `histplot` (an axes-level function for histograms).\n", "  warnings.warn(msg, FutureWarning)\n", "c:\\Users\\<USER>\\.conda\\envs\\geospatial\\lib\\site-packages\\seaborn\\distributions.py:2619: FutureWarning: `distplot` is a deprecated function and will be removed in a future version. Please adapt your code to use either `displot` (a figure-level function with similar flexibility) or `histplot` (an axes-level function for histograms).\n", "  warnings.warn(msg, FutureWarning)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sub1 = water['LSWI']-water['NDVI']\n", "sub2 = nonWater['LSWI']-nonWater['NDVI']\n", "plt.xlabel('LSWI - NDVI')\n", "displot(sub1, sub2)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## 水体提取算法验证"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [], "source": ["# This study\n", "def predict1(row):\n", "    # relation = (row['LSWI'] > row['NDVI'])# | (row['LSWI'] > row['EVI'])\n", "    # relation = ((row['MNDWI'] > row['EVI']) | (row['MNDWI'] > row['BSI'])) # | (row['LSWI'] > row['NDVI'])) & (row['EVI']<1000)\n", "    # relation = (row['MNDWI'] > row['NDVI']) | (row['MNDWI'] > row['EVI'])\n", "    relation = (row['MNDWI'] > row['EVI']) | (row['MNDWI']>row['NDVI']) | (row['LSWI'] > row['NDVI']) # & (row['EVI']<1000)\n", "    relation = (row['MNDWI'] > row['EVI']) | (row['MNDWI']>row['NDVI']) | ((row['LSWI'] > row['NDVI']) & (row['LSWI'] > row['EVI']))\n", "    return pd.Series(relation + 1)"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [], "source": ["# Zou.等\n", "def predict2(row):\n", "    # relation = (row['LSWI'] > row['NDVI'])# | (row['LSWI'] > row['EVI'])\n", "    # relation = (row['MNDWI'] > row['NDVI']) | (row['MNDWI'] > row['BSI'])\n", "    \n", "    relation = ((row['MNDWI'] > row['NDVI']) | (row['MNDWI'] > row['EVI'])) & (np.array(row['EVI']) < 1000)\n", "    return pd.Series(relation + 1)"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_14940\\3115704269.py:3: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  data['predict1'] = data.apply(predict1, axis=1)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_14940\\3115704269.py:5: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  data['predict2'] = data.apply(predict2, axis=1)\n"]}], "source": ["data = df3[df3['water']!=0]\n", "# This study.\n", "data['predict1'] = data.apply(predict1, axis=1)\n", "# Zou.等\n", "data['predict2'] = data.apply(predict2, axis=1)\n", "# AWEI\n", "# data['predict3'] = (data['AWEIsh']>0) + 1"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"data": {"text/plain": ["predict1\n", "1    653\n", "2    271\n", "dtype: int64"]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["data.value_counts('predict1')"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\geospatial\\lib\\site-packages\\seaborn\\distributions.py:2619: FutureWarning: `distplot` is a deprecated function and will be removed in a future version. Please adapt your code to use either `displot` (a figure-level function with similar flexibility) or `histplot` (an axes-level function for histograms).\n", "  warnings.warn(msg, FutureWarning)\n", "c:\\Users\\<USER>\\.conda\\envs\\geospatial\\lib\\site-packages\\seaborn\\distributions.py:2619: FutureWarning: `distplot` is a deprecated function and will be removed in a future version. Please adapt your code to use either `displot` (a figure-level function with similar flexibility) or `histplot` (an axes-level function for histograms).\n", "  warnings.warn(msg, FutureWarning)\n"]}, {"data": {"text/plain": ["<matplotlib.legend.Legend at 0x255a505b220>"]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["water_pre = data[data['predict1']==2]\n", "water_pre2 = data[data['predict2']==2]\n", "water_ref = data[data['water']==2]\n", "nonWater_pre = data[data['predict1']==1]\n", "\n", "sns.distplot(water_pre['EVI'], bins=1000, kde=True, label='predict1')\n", "sns.distplot(water_pre2['EVI'], bins=1000, kde=True, label='predict2')\n", "# sns.distplot(water_ref['BI'], bins=1000, kde=True)\n", "\n", "plt.legend()"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-------本方法与Pekel.等对比-------\n", "0.9437229437229437\n", "0.8651163051839043\n", "[[624  29]\n", " [ 23 248]]\n", "-------本方法与Zou.等对比-------\n", "0.9924242424242424\n", "0.9815858699736941\n", "[[653   0]\n", " [  7 264]]\n", "-------<PERSON><PERSON>.等与Pekel.等对比-------\n", "0.9512987012987013\n", "0.8824188129899216\n", "[[631  29]\n", " [ 16 248]]\n"]}], "source": ["\n", "print('-------本方法与Pekel.等对比-------')\n", "print(accuracy_score(data['predict1'], data['water']))\n", "print(cohen_kappa_score(data['predict1'], data['water']))\n", "print(confusion_matrix(data['predict1'], data['water']))\n", "print('-------本方法与Zou.等对比-------')\n", "print(accuracy_score(data['predict1'], data['predict2']))\n", "print(cohen_kappa_score(data['predict1'], data['predict2']))\n", "print(confusion_matrix(data['predict1'], data['predict2']))\n", "print('-------<PERSON><PERSON>.等与Pekel.等对比-------')\n", "print(accuracy_score(data['predict2'], data['water']))\n", "print(cohen_kappa_score(data['predict2'], data['water']))\n", "print(confusion_matrix(data['predict2'], data['water']))\n", "# print('-------本方法与AWEI方法对比-------')\n", "# print(accuracy_score(data['predict1'], data['predict3']))\n", "# print(cohen_kappa_score(data['predict1'], data['predict3']))\n", "# print(confusion_matrix(data['predict1'], data['predict3']))\n", "# print('-------Z<PERSON>.等与AWEI方法对比------')\n", "# print(accuracy_score(data['predict2'], data['predict3']))\n", "# print(cohen_kappa_score(data['predict2'], data['predict3']))\n", "# print(confusion_matrix(data['predict2'], data['predict3']))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[data['predict1']!=data['predict2']]"]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_14940\\230581087.py:3: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  data['refer'] = data['refer']-1\n"]}], "source": ["# 将JRC数据的缺失数据过滤\n", "data = df3[df3['refer']!=0]\n", "data['refer'] = data['refer']-1"]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-------本方法与Pekel.等对比-------\n", "0.9242089771891097\n", "0.8052068417031616\n", "[[950  87]\n", " [ 16 306]]\n", "-------本方法与Zou.等对比-------\n", "0.9889624724061811\n", "0.969111904758297\n", "[[1035    2]\n", " [  13  309]]\n", "-------本方法2与Pekel.等对比-------\n", "0.9212656364974245\n", "0.7982854408123348\n", "[[946  87]\n", " [ 20 306]]\n", "-------本方法与Zou.等对比-------\n", "0.9889624724061811\n", "0.9692492657385381\n", "[[1033    0]\n", " [  15  311]]\n", "-------本方法与本方法2对比-------\n", "0.9896983075791023\n", "0.9716321150818265\n", "[[1028    9]\n", " [   5  317]]\n"]}], "source": ["\n", "print('-------本方法与Pekel.等对比-------')\n", "print(accuracy_score(data['water_this'], data['refer']))\n", "print(cohen_kappa_score(data['water_this'], data['refer']))\n", "print(confusion_matrix(data['water_this'], data['refer']))\n", "print('-------本方法与Zou.等对比-------')\n", "print(accuracy_score(data['water_this'], data['water_zou']))\n", "print(cohen_kappa_score(data['water_this'], data['water_zou']))\n", "print(confusion_matrix(data['water_this'], data['water_zou']))\n", "# print('-------<PERSON><PERSON>.等与Pekel.等对比-------')\n", "# print(accuracy_score(data['water_zou'], data['refer']))\n", "# print(cohen_kappa_score(data['water_zou'], data['refer']))\n", "# print(confusion_matrix(data['water_zou'], data['refer']))\n", "\n", "print('-------本方法2与Pekel.等对比-------')\n", "print(accuracy_score(data['water_this_wt'], data['refer']))\n", "print(cohen_kappa_score(data['water_this_wt'], data['refer']))\n", "print(confusion_matrix(data['water_this_wt'], data['refer']))\n", "print('-------本方法与Zou.等对比-------')\n", "print(accuracy_score(data['water_this_wt'], data['water_zou']))\n", "print(cohen_kappa_score(data['water_this_wt'], data['water_zou']))\n", "print(confusion_matrix(data['water_this_wt'], data['water_zou']))\n", "# print('-------<PERSON><PERSON>.等与Pekel.等对比-------')\n", "# print(accuracy_score(data['water_zou'], data['refer']))\n", "# print(cohen_kappa_score(data['water_zou'], data['refer']))\n", "# print(confusion_matrix(data['water_zou'], data['refer']))\n", "\n", "print('-------本方法与本方法2对比-------')\n", "print(accuracy_score(data['water_this'], data['water_this_wt']))\n", "print(cohen_kappa_score(data['water_this'], data['water_this_wt']))\n", "print(confusion_matrix(data['water_this'], data['water_this_wt']))"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import datetime\n", "\n", "def fractional_year_to_date(fractional_year):\n", "    year = int(fractional_year)\n", "    remainder = fractional_year - year\n", "    days_in_year = 365 + int(datetime.date(year, 1, 1).strftime('%j') == '366')\n", "    date = datetime.date.fromordinal(datetime.date(year, 1, 1).toordinal() + int(days_in_year * remainder))\n", "    return date.strftime('%Y-%m-%d')"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["'2004-12-29'"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["fractional_year_to_date(2004.996)"]}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>system:index</th>\n", "      <th>Classified</th>\n", "      <th>GrndTruth</th>\n", "      <th>fractional_years</th>\n", "      <th>random</th>\n", "      <th>refer</th>\n", "      <th>water_this</th>\n", "      <th>water_this_wt</th>\n", "      <th>water_zou</th>\n", "      <th>.geo</th>\n", "    </tr>\n", "    <tr>\n", "      <th>ID</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>97124</th>\n", "      <td>00000000000000003403_0</td>\n", "      <td>0</td>\n", "      <td>-1</td>\n", "      <td>2021.835702</td>\n", "      <td>0.864649</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>96838</th>\n", "      <td>000000000000000033e5_0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>2021.912462</td>\n", "      <td>0.868414</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21749</th>\n", "      <td>00000000000000001355_0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>2016.502534</td>\n", "      <td>0.868865</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>57160</th>\n", "      <td>000000000000000022cb_0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>2021.666529</td>\n", "      <td>0.869277</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>65882</th>\n", "      <td>00000000000000002699_0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>2021.668979</td>\n", "      <td>0.870548</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81343</th>\n", "      <td>00000000000000002d0e_0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>2021.250205</td>\n", "      <td>0.872591</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>58867</th>\n", "      <td>00000000000000002371_0</td>\n", "      <td>0</td>\n", "      <td>-1</td>\n", "      <td>2019.249846</td>\n", "      <td>0.877199</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>94858</th>\n", "      <td>000000000000000032fb_0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>2017.089334</td>\n", "      <td>0.877333</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>57042</th>\n", "      <td>000000000000000022c2_0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>2020.416083</td>\n", "      <td>0.879561</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>86015</th>\n", "      <td>00000000000000002f21_0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>2021.663920</td>\n", "      <td>0.881016</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>94298</th>\n", "      <td>000000000000000032c4_0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>2021.158974</td>\n", "      <td>0.882103</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55222</th>\n", "      <td>0000000000000000220a_0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>2021.499652</td>\n", "      <td>0.882458</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93838</th>\n", "      <td>0000000000000000328d_0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>2021.750848</td>\n", "      <td>0.884745</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>72949</th>\n", "      <td>00000000000000002990_0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>2021.836128</td>\n", "      <td>0.889225</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>65058</th>\n", "      <td>00000000000000002640_0</td>\n", "      <td>0</td>\n", "      <td>-1</td>\n", "      <td>2021.329393</td>\n", "      <td>0.889495</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22233</th>\n", "      <td>0000000000000000137f_0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>2015.498327</td>\n", "      <td>0.890050</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 system:index  Classified  GrndTruth  fractional_years  \\\n", "ID                                                                       \n", "97124  00000000000000003403_0           0         -1       2021.835702   \n", "96838  000000000000000033e5_0           1         -1       2021.912462   \n", "21749  00000000000000001355_0           1         -1       2016.502534   \n", "57160  000000000000000022cb_0           1         -1       2021.666529   \n", "65882  00000000000000002699_0           1         -1       2021.668979   \n", "81343  00000000000000002d0e_0           1         -1       2021.250205   \n", "58867  00000000000000002371_0           0         -1       2019.249846   \n", "94858  000000000000000032fb_0           1         -1       2017.089334   \n", "57042  000000000000000022c2_0           1         -1       2020.416083   \n", "86015  00000000000000002f21_0           1         -1       2021.663920   \n", "94298  000000000000000032c4_0           1         -1       2021.158974   \n", "55222  0000000000000000220a_0           1         -1       2021.499652   \n", "93838  0000000000000000328d_0           1         -1       2021.750848   \n", "72949  00000000000000002990_0           1         -1       2021.836128   \n", "65058  00000000000000002640_0           0         -1       2021.329393   \n", "22233  0000000000000000137f_0           1         -1       2015.498327   \n", "\n", "         random  refer  water_this  water_this_wt  water_zou  \\\n", "ID                                                             \n", "97124  0.864649      0           1              1          0   \n", "96838  0.868414      0           1              1          0   \n", "21749  0.868865      0           1              1          1   \n", "57160  0.869277      0           1              1          1   \n", "65882  0.870548      0           1              1          1   \n", "81343  0.872591      0           1              1          1   \n", "58867  0.877199      0           1              1          0   \n", "94858  0.877333      0           1              1          1   \n", "57042  0.879561      0           1              1          1   \n", "86015  0.881016      0           1              1          1   \n", "94298  0.882103      0           1              1          1   \n", "55222  0.882458      0           1              1          1   \n", "93838  0.884745      0           1              1          1   \n", "72949  0.889225      0           1              1          1   \n", "65058  0.889495      0           1              1          1   \n", "22233  0.890050      0           1              1          1   \n", "\n", "                                         .geo  \n", "ID                                             \n", "97124  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "96838  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "21749  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "57160  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "65882  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "81343  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "58867  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "94858  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "57042  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "86015  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "94298  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "55222  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "93838  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "72949  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "65058  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "22233  {\"type\":\"MultiPoint\",\"coordinates\":[]}  "]}, "execution_count": 74, "metadata": {}, "output_type": "execute_result"}], "source": ["judge = data[data['water_this']!=data['refer']]\n", "judge[judge['refer']==0]"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_14940\\3090733178.py:3: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  judge['fractional_years'] = judge['fractional_years'].map(fractional_year_to_date)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>system:index</th>\n", "      <th>Classified</th>\n", "      <th>GrndTruth</th>\n", "      <th>fractional_years</th>\n", "      <th>random</th>\n", "      <th>refer</th>\n", "      <th>water_this</th>\n", "      <th>water_this_wt</th>\n", "      <th>water_zou</th>\n", "      <th>.geo</th>\n", "    </tr>\n", "    <tr>\n", "      <th>ID</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>41775</th>\n", "      <td>00000000000000001c21_0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>2015-08-01</td>\n", "      <td>0.863228</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>68719</th>\n", "      <td>000000000000000027b7_0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>2021-09-02</td>\n", "      <td>0.864201</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>77495</th>\n", "      <td>00000000000000002b87_0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>2021-10-31</td>\n", "      <td>0.869354</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>59899</th>\n", "      <td>000000000000000023f8_0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>2016-07-01</td>\n", "      <td>0.869928</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63418</th>\n", "      <td>0000000000000000257e_0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>2021-10-01</td>\n", "      <td>0.876805</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71544</th>\n", "      <td>000000000000000028f6_0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>2021-09-02</td>\n", "      <td>0.879767</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>85845</th>\n", "      <td>00000000000000002f0c_0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>2021-05-02</td>\n", "      <td>0.879976</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48402</th>\n", "      <td>00000000000000001f28_0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>2021-06-02</td>\n", "      <td>0.881563</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>60192</th>\n", "      <td>00000000000000002420_0</td>\n", "      <td>0</td>\n", "      <td>-1</td>\n", "      <td>2021-03-30</td>\n", "      <td>0.883991</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48149</th>\n", "      <td>00000000000000001f03_0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>2016-07-01</td>\n", "      <td>0.884540</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71126</th>\n", "      <td>000000000000000028c6_0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>2021-09-01</td>\n", "      <td>0.886203</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48122</th>\n", "      <td>00000000000000001efe_0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>2021-05-31</td>\n", "      <td>0.886284</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43823</th>\n", "      <td>00000000000000001d03_0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>2020-06-30</td>\n", "      <td>0.887885</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>82128</th>\n", "      <td>00000000000000002d6a_0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>2021-10-31</td>\n", "      <td>0.889829</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 system:index  Classified  GrndTruth fractional_years  \\\n", "ID                                                                      \n", "41775  00000000000000001c21_0           1         -1       2015-08-01   \n", "68719  000000000000000027b7_0           1         -1       2021-09-02   \n", "77495  00000000000000002b87_0           1         -1       2021-10-31   \n", "59899  000000000000000023f8_0           1         -1       2016-07-01   \n", "63418  0000000000000000257e_0           1         -1       2021-10-01   \n", "71544  000000000000000028f6_0           1         -1       2021-09-02   \n", "85845  00000000000000002f0c_0           1         -1       2021-05-02   \n", "48402  00000000000000001f28_0           1         -1       2021-06-02   \n", "60192  00000000000000002420_0           0         -1       2021-03-30   \n", "48149  00000000000000001f03_0           1         -1       2016-07-01   \n", "71126  000000000000000028c6_0           1         -1       2021-09-01   \n", "48122  00000000000000001efe_0           1         -1       2021-05-31   \n", "43823  00000000000000001d03_0           1         -1       2020-06-30   \n", "82128  00000000000000002d6a_0           1         -1       2021-10-31   \n", "\n", "         random  refer  water_this  water_this_wt  water_zou  \\\n", "ID                                                             \n", "41775  0.863228      1           1              0          0   \n", "68719  0.864201      0           0              1          0   \n", "77495  0.869354      0           0              1          0   \n", "59899  0.869928      1           0              1          0   \n", "63418  0.876805      1           1              0          0   \n", "71544  0.879767      0           0              1          0   \n", "85845  0.879976      1           1              0          0   \n", "48402  0.881563      1           0              1          1   \n", "60192  0.883991      0           0              1          0   \n", "48149  0.884540      1           1              0          0   \n", "71126  0.886203      1           0              1          0   \n", "48122  0.886284      1           0              1          1   \n", "43823  0.887885      1           1              0          0   \n", "82128  0.889829      1           0              1          0   \n", "\n", "                                         .geo  \n", "ID                                             \n", "41775  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "68719  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "77495  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "59899  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "63418  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "71544  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "85845  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "48402  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "60192  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "48149  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "71126  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "48122  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "43823  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "82128  {\"type\":\"MultiPoint\",\"coordinates\":[]}  "]}, "execution_count": 78, "metadata": {}, "output_type": "execute_result"}], "source": ["# 比对是否解译错误\n", "judge = data[data['water_this']!=data['water_this_wt']]\n", "judge['fractional_years'] = judge['fractional_years'].map(fractional_year_to_date)\n", "judge"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# NDVI\n", "# 0.947939511665309\n", "# 0.8720139830596473\n", "# [[24690  1574]\n", "#  [ 3411 66079]]\n", "# This study\n", "# 0.9291935584936399\n", "# 0.8295382793036692\n", "# [[24706  1558]\n", "#  [ 5222 64268]]\n", "#Zou\n", "# 0.9438665747645008\n", "# 0.8745650627777415\n", "# [[24851  1413]\n", "#  [ 3486 66004]]"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## 去除冰雪影响探索"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### 2.1 数据预处理"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pd.read_excel(r\"C:\\Users\\<USER>\\Desktop\\TRHR\\algorithm\\snow_join2020.xlsx\").set_index('ID')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df2 = pd.DataFrame(df['array'].str.split(\",\", expand=True).stack())\n", "name = ['NDWI', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'NDVI', 'BSI', 'LSWI', 'B<PERSON>', 'DBSI', 'NDSI', 'st', 'snow', 'time']\n", "nameList = name * int(len(df2.reset_index())/12)\n", "df2['name'] = nameList"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["index1 = df2.index.get_level_values(0)\n", "bins = pd.interval_range(start=0, freq=11, end =1000, closed='left')\n", "index2 = pd.cut(df2.index.get_level_values(1), bins)\n", "tuples = list(zip(*[index1, index2]))\n", "index = pd.MultiIndex.from_tuples(tuples, names=['ID', 'num'])\n", "df3 = pd.DataFrame(df2.values, index = index, columns = ['value', 'name'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df4 = pd.pivot(df3.reset_index(), index=['ID', 'num'], columns='name', values='value').astype(float)\n", "df4 = df4[df4.abs()<10000]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# MNDWI, EVI, NDVI, BSI, LSWI, BI, DBSI\n", "def displot(data1, data2):\n", "    sns.distplot(data1, bins=100, kde=True, label='snow')\n", "    sns.distplot(data2, bins=100, kde=True, label='nonSnow')\n", "    plt.legend()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["snow = df4[df4['snow']>0]\n", "nonSnow = df4[df4['snow']==0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df5 = df4.copy()\n", "df5['snow'] = df5['snow']>20\n", "df5['nonSnow'] = df5['snow']<20"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# NDSI>BSI or NDSI >NDVI\n", "\n", "# 错分雪的像元数\n", "def apply(relation, cover):\n", "    error = (df4[relation]['snow']>=cover).sum()\n", "    prediction = df4[relation]['snow'].count()\n", "    reality = (df4['snow']<=cover).sum()\n", "    print('错误率: ', error/prediction)\n", "    print('漏分率: ', (reality-prediction)/reality)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def predict(row):\n", "    # 去除冰雪\n", "    relation = ((row['NDSI'] < (row['NDWI']+2500)) | (row['NDSI'] < row['st'])) # & (row['EVI'] > -1000)\n", "    # 提取冰雪\n", "    # relation = ((row['NDSI']>row['NDWI']+2500) | (row['NDSI']>row['st'])) & (row['NDVI']<3000)\n", "    \n", "    # ----------Trash----------\n", "    # relation = (row['MNDWI'] > row['NDVI']) | (row['MNDWI'] > row['BSI']) | (row['LSWI'] > row['NDVI'])\n", "    # relation = (df4['NDSI'] - df4['BSI']>0) & (df4['NDSI'] - df4['NDVI']>0) & (df4['EVI']>-1000)\n", "    # relation = (row['MNDWI'] > row['NDWI']+7000) & (row['st'] < 274.65)\n", "    # relation = (row['MNDWI'] > row['BSI']) & (row['MNDWI'] > row['BSI'])\n", "    # relation = (df4['NDSI'] - df4['BSI']>0) & (df4['NDSI'] - df4['NDVI']>0) & (df4['EVI']<1000)\n", "    # relation = (row['MNDWI'] > row['NDVI']) | (row['MNDWI'] > row['EVI']) & (row['EVI'] < 1000)\n", "    return pd.Series(relation)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df5['predict'] = df5.apply(predict, axis=1)\n", "print('----------提取冰雪----------')\n", "print('总体精度:', accuracy_score(df5['snow'], df5['predict']))\n", "print('Kappa:', cohen_kappa_score(df5['snow'], df5['predict']))\n", "print('混淆矩阵:', confusion_matrix(df5['snow'], df5['predict']))\n", "print('----------去除冰雪----------')\n", "print('总体精度:', accuracy_score(df5['nonSnow'], df5['predict']))\n", "print('Kappa:', cohen_kappa_score(df5['nonSnow'], df5['predict']))\n", "print('混淆矩阵:', confusion_matrix(df5['nonSnow'], df5['predict']))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df5.index"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plt.scatter(np.arange(0, len(df5)), df5['nonSnow'])\n", "plt.scatter(np.arange(0, len(df5)), df5['predict'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# This study\n", "总体精度: 0.8663852035395392\n", "Kappa: 0.5293516621279665\n", "混淆矩阵: [[190440  24895]\n", " [  8430  25646]]\n", "# Zou\n", "0.8756430149432062\n", "0.5034829982176892\n", "[[197332  18003]\n", " [ 13013  21063]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sub1 = snow['NDSI']-snow['NDWI'] - 2500\n", "sub2 = nonSnow['NDSI']-nonSnow['NDWI'] - 2500\n", "displot(sub1, sub2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sub1 = snow['NDSI']-snow['st']\n", "sub2 = nonSnow['NDSI']-nonSnow['st']\n", "displot(sub1, sub2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sub1 = snow['NDSI']-snow['BSI']\n", "sub2 = nonSnow['NDSI']-nonSnow['BSI']\n", "displot(sub1, sub2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sub1 = snow['EVI']+1000\n", "sub2 = nonSnow['EVI']+1000\n", "displot(sub1, sub2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sub1 = snow['BI']\n", "sub2 = nonSnow['BI']\n", "displot(sub1, sub2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3.9.13 ('geospatial')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "b1b39a4a244caf17510908998bb6239044600d7237f663de43d23978e1895c1a"}}}, "nbformat": 4, "nbformat_minor": 2}