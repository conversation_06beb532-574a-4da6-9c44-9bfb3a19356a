{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import cv2"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "error", "evalue": "OpenCV(4.6.0) D:\\a\\opencv-python\\opencv-python\\opencv\\modules\\imgproc\\src\\resize.cpp:4052: error: (-215:<PERSON><PERSON><PERSON> failed) !ssize.empty() in function 'cv::resize'\n", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31<PERSON><PERSON>r\u001b[0m                                     <PERSON><PERSON> (most recent call last)", "\u001b[1;32mUntitled-1.ipynb Cell 1\u001b[0m line \u001b[0;36m<cell line: 16>\u001b[1;34m()\u001b[0m\n\u001b[0;32m     <a href='vscode-notebook-cell:Untitled-1.ipynb?jupyter-notebook#W0sdW50aXRsZWQ%3D?line=12'>13</a>\u001b[0m image \u001b[39m=\u001b[39m cv2\u001b[39m.\u001b[39mimread(\u001b[39m'\u001b[39m\u001b[39mpath_to_image.jpg\u001b[39m\u001b[39m'\u001b[39m)\n\u001b[0;32m     <a href='vscode-notebook-cell:Untitled-1.ipynb?jupyter-notebook#W0sdW50aXRsZWQ%3D?line=14'>15</a>\u001b[0m \u001b[39m# 调整图像大小和颜色通道\u001b[39;00m\n\u001b[1;32m---> <a href='vscode-notebook-cell:Untitled-1.ipynb?jupyter-notebook#W0sdW50aXRsZWQ%3D?line=15'>16</a>\u001b[0m image \u001b[39m=\u001b[39m cv2\u001b[39m.\u001b[39;49mresize(image, (\u001b[39m800\u001b[39;49m, \u001b[39m600\u001b[39;49m))\n\u001b[0;32m     <a href='vscode-notebook-cell:Untitled-1.ipynb?jupyter-notebook#W0sdW50aXRsZWQ%3D?line=16'>17</a>\u001b[0m image \u001b[39m=\u001b[39m cv2\u001b[39m.\u001b[39mcvtColor(image, cv2\u001b[39m.\u001b[39mCOLOR_BGR2RGB)\n\u001b[0;32m     <a href='vscode-notebook-cell:Untitled-1.ipynb?jupyter-notebook#W0sdW50aXRsZWQ%3D?line=18'>19</a>\u001b[0m \u001b[39m# 模拟云覆盖率为0.3的影响\u001b[39;00m\n", "\u001b[1;31merror\u001b[0m: OpenCV(4.6.0) D:\\a\\opencv-python\\opencv-python\\opencv\\modules\\imgproc\\src\\resize.cpp:4052: error: (-215:<PERSON><PERSON><PERSON> failed) !ssize.empty() in function 'cv::resize'\n"]}], "source": ["def simulate_clouds(image, cloud_coverage):\n", "    # 创建与原始图像相同大小的随机遮罩\n", "    mask = np.random.choice([0, 1], size=image.shape[:2], p=[cloud_coverage, 1-cloud_coverage])\n", "    # 将遮罩应用于图像\n", "    clouded_image = image.copy()\n", "    clouded_image[mask == 1] = 0  # 设置遮罩中为1的像素为空值\n", "    return clouded_image\n", "\n", "# def simulate_clouds(image, cloud_coverage, cluster_size):\n", "#     # 创建与原始图像相同大小的随机遮罩\n", "#     mask = np.random.choice([0, 1], size=image.shape[:2], p=[cloud_coverage, 1-cloud_coverage])\n", "\n", "#     # 对遮罩进行膨胀操作以增加云的集聚性\n", "#     kernel = np.ones((cluster_size, cluster_size), np.uint8)\n", "#     dilated_mask = cv2.dilate(mask.astype(np.uint8), kernel, iterations=1)\n", "\n", "#     # 将遮罩应用于图像\n", "#     clouded_image = image.copy()\n", "#     clouded_image[dilated_mask == 1] = 0  # 设置遮罩中为1的像素为空值\n", "#     return clouded_image\n", "\n", "# 读取原始图像\n", "image = cv2.imread('path_to_image.jpg')\n", "\n", "# 调整图像大小和颜色通道\n", "image = cv2.resize(image, (800, 600))\n", "image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)\n", "\n", "# 模拟云覆盖率为0.3的影响\n", "cloud_coverage = 0.3\n", "clouded_image = simulate_clouds(image, cloud_coverage)\n", "\n", "# 显示原始图像和有云影响的图像\n", "cv2.imshow('Original Image', image)\n", "cv2.imshow('Clouded Image', clouded_image)\n", "cv2.<PERSON><PERSON><PERSON>(0)\n", "cv2.destroyAllWindows()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}