{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import geopandas as gpd\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["inws = r'C:\\Users\\<USER>\\Desktop\\STNS\\Training\\随机森林模型\\feaCol.shp'\n", "gdf = gpd.read_file(inws)\n", "\n", "remain_cols = []"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>red</th>\n", "      <th>st</th>\n", "      <th>green</th>\n", "      <th>blue</th>\n", "      <th>nir</th>\n", "      <th>swir1</th>\n", "      <th>swir2</th>\n", "      <th>water</th>\n", "      <th>geometry</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.003390</td>\n", "      <td>286.742788</td>\n", "      <td>0.012699</td>\n", "      <td>0.010925</td>\n", "      <td>0.000571</td>\n", "      <td>0.001905</td>\n", "      <td>0.000351</td>\n", "      <td>0</td>\n", "      <td>POINT (-99.58081 60.26397)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.011516</td>\n", "      <td>277.394503</td>\n", "      <td>0.020550</td>\n", "      <td>0.036981</td>\n", "      <td>0.010031</td>\n", "      <td>0.001162</td>\n", "      <td>0.001396</td>\n", "      <td>0</td>\n", "      <td>POINT (52.89599 73.07763)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0.000392</td>\n", "      <td>286.305281</td>\n", "      <td>0.003638</td>\n", "      <td>0.003830</td>\n", "      <td>0.001272</td>\n", "      <td>0.005865</td>\n", "      <td>0.001520</td>\n", "      <td>0</td>\n", "      <td>POINT (-78.25732 54.75470)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0.000447</td>\n", "      <td>291.350279</td>\n", "      <td>0.007130</td>\n", "      <td>0.012465</td>\n", "      <td>-0.007830</td>\n", "      <td>-0.000102</td>\n", "      <td>0.001657</td>\n", "      <td>0</td>\n", "      <td>POINT (12.90084 57.33834)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0.105718</td>\n", "      <td>301.347987</td>\n", "      <td>0.101015</td>\n", "      <td>0.062350</td>\n", "      <td>0.133960</td>\n", "      <td>0.004325</td>\n", "      <td>0.001767</td>\n", "      <td>0</td>\n", "      <td>POINT (-101.05764 19.93842)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>84818</th>\n", "      <td>0.030670</td>\n", "      <td>293.630098</td>\n", "      <td>0.073158</td>\n", "      <td>0.069803</td>\n", "      <td>0.008835</td>\n", "      <td>0.009165</td>\n", "      <td>-0.008462</td>\n", "      <td>1</td>\n", "      <td>POINT (150.78856 -23.00096)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>84819</th>\n", "      <td>0.003527</td>\n", "      <td>289.357573</td>\n", "      <td>0.015187</td>\n", "      <td>0.028525</td>\n", "      <td>0.003197</td>\n", "      <td>-0.004063</td>\n", "      <td>-0.008985</td>\n", "      <td>1</td>\n", "      <td>POINT (-86.04059 30.23312)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>84820</th>\n", "      <td>0.141990</td>\n", "      <td>272.640037</td>\n", "      <td>0.160910</td>\n", "      <td>0.147572</td>\n", "      <td>0.088173</td>\n", "      <td>-0.000790</td>\n", "      <td>-0.009040</td>\n", "      <td>1</td>\n", "      <td>POINT (-66.43352 61.88364)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>84821</th>\n", "      <td>-0.061428</td>\n", "      <td>283.769111</td>\n", "      <td>-0.066983</td>\n", "      <td>-0.073995</td>\n", "      <td>-0.042342</td>\n", "      <td>-0.008325</td>\n", "      <td>-0.009453</td>\n", "      <td>1</td>\n", "      <td>POINT (-65.25259 -44.70848)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>84822</th>\n", "      <td>0.078878</td>\n", "      <td>284.063060</td>\n", "      <td>0.077393</td>\n", "      <td>0.061800</td>\n", "      <td>0.055970</td>\n", "      <td>-0.011102</td>\n", "      <td>-0.011680</td>\n", "      <td>1</td>\n", "      <td>POINT (84.25357 70.43308)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>84823 rows × 9 columns</p>\n", "</div>"], "text/plain": ["            red          st     green      blue       nir     swir1     swir2  \\\n", "0      0.003390  286.742788  0.012699  0.010925  0.000571  0.001905  0.000351   \n", "1      0.011516  277.394503  0.020550  0.036981  0.010031  0.001162  0.001396   \n", "2      0.000392  286.305281  0.003638  0.003830  0.001272  0.005865  0.001520   \n", "3      0.000447  291.350279  0.007130  0.012465 -0.007830 -0.000102  0.001657   \n", "4      0.105718  301.347987  0.101015  0.062350  0.133960  0.004325  0.001767   \n", "...         ...         ...       ...       ...       ...       ...       ...   \n", "84818  0.030670  293.630098  0.073158  0.069803  0.008835  0.009165 -0.008462   \n", "84819  0.003527  289.357573  0.015187  0.028525  0.003197 -0.004063 -0.008985   \n", "84820  0.141990  272.640037  0.160910  0.147572  0.088173 -0.000790 -0.009040   \n", "84821 -0.061428  283.769111 -0.066983 -0.073995 -0.042342 -0.008325 -0.009453   \n", "84822  0.078878  284.063060  0.077393  0.061800  0.055970 -0.011102 -0.011680   \n", "\n", "       water                     geometry  \n", "0          0   POINT (-99.58081 60.26397)  \n", "1          0    POINT (52.89599 73.07763)  \n", "2          0   POINT (-78.25732 54.75470)  \n", "3          0    POINT (12.90084 57.33834)  \n", "4          0  POINT (-101.05764 19.93842)  \n", "...      ...                          ...  \n", "84818      1  POINT (150.78856 -23.00096)  \n", "84819      1   POINT (-86.04059 30.23312)  \n", "84820      1   POINT (-66.43352 61.88364)  \n", "84821      1  POINT (-65.25259 -44.70848)  \n", "84822      1    POINT (84.25357 70.43308)  \n", "\n", "[84823 rows x 9 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["gdf"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["84823\n", "74497\n"]}], "source": ["print(len(gdf))\n", "\n", "# 指定要排除的行索引\n", "exclude_cols = ['water', 'geometry']\n", "\n", "# 对其他行应用标准差过滤\n", "other_cols = [col for col in gdf.columns if col not in exclude_cols]\n", "\n", "filtered_df = gdf[exclude_cols]  # 首先保留指定排除的列\n", "remain_df = gdf[other_cols]\n", "\n", "mean = remain_df.mean(axis=0)\n", "std = remain_df.std(axis=0)\n", "\n", "del_df = remain_df.mask(np.abs(remain_df - mean[other_cols]) > 2 * std[other_cols], other=np.nan)\n", "\n", "join_df = filtered_df.join(del_df).dropna()\n", "\n", "print(len(join_df))"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["gpd.GeoDataFrame(join_df).to_file(r'C:\\Users\\<USER>\\Desktop\\STNS\\Training\\随机森林模型\\feaCol_filter.shp')"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["inws = r'C:\\Users\\<USER>\\Desktop\\Poyang_Water\\Training\\join_shp_r15_2.shp'\n", "join_df = gpd.read_file(inws)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# join_df = gpd.read_file('join_shp_r15.shp')\n", "# 加载数据\n", "# data = join_df\n", "data = join_df"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["252394"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["len(data)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["(array([2.0000e+00, 1.0000e+00, 0.0000e+00, 1.0000e+00, 0.0000e+00,\n", "        0.0000e+00, 0.0000e+00, 1.0000e+00, 0.0000e+00, 1.0000e+00,\n", "        3.0000e+00, 4.0000e+00, 3.0000e+00, 4.0000e+00, 1.1000e+01,\n", "        1.3000e+01, 1.3000e+01, 9.0000e+00, 1.5000e+01, 1.2000e+01,\n", "        2.5000e+01, 1.4000e+01, 2.4000e+01, 3.1000e+01, 3.5000e+01,\n", "        4.6000e+01, 4.0000e+01, 7.1000e+01, 1.0500e+02, 1.5300e+02,\n", "        2.9000e+02, 4.8700e+02, 8.5900e+02, 1.7900e+03, 3.1550e+03,\n", "        4.4570e+03, 5.7970e+03, 6.9640e+03, 8.0970e+03, 9.4290e+03,\n", "        1.0352e+04, 1.1545e+04, 1.2138e+04, 1.2640e+04, 1.2649e+04,\n", "        1.2710e+04, 1.3056e+04, 1.2629e+04, 1.1985e+04, 1.1382e+04,\n", "        1.0567e+04, 9.4540e+03, 8.2660e+03, 7.4440e+03, 6.4730e+03,\n", "        5.6930e+03, 4.9330e+03, 4.2480e+03, 3.6670e+03, 3.2040e+03,\n", "        2.8580e+03, 2.7100e+03, 2.4740e+03, 2.1730e+03, 1.9330e+03,\n", "        1.6360e+03, 1.3610e+03, 1.3030e+03, 1.2110e+03, 1.0510e+03,\n", "        9.2000e+02, 7.8400e+02, 7.4700e+02, 6.1400e+02, 5.0500e+02,\n", "        4.9200e+02, 4.0500e+02, 3.3100e+02, 2.9300e+02, 2.0200e+02,\n", "        2.1700e+02, 1.8400e+02, 1.4800e+02, 1.4100e+02, 1.0500e+02,\n", "        9.8000e+01, 7.7000e+01, 6.8000e+01, 6.5000e+01, 6.5000e+01,\n", "        3.8000e+01, 4.8000e+01, 3.5000e+01, 1.6000e+01, 2.3000e+01,\n", "        2.8000e+01, 1.5000e+01, 1.1000e+01, 6.0000e+00, 6.0000e+00]),\n", " array([-0.10221   , -0.09854439, -0.09487877, -0.09121316, -0.08754755,\n", "        -0.08388194, -0.08021633, -0.07655071, -0.0728851 , -0.06921949,\n", "        -0.06555387, -0.06188826, -0.05822265, -0.05455704, -0.05089142,\n", "        -0.04722581, -0.0435602 , -0.03989459, -0.03622897, -0.03256336,\n", "        -0.02889775, -0.02523214, -0.02156653, -0.01790091, -0.0142353 ,\n", "        -0.01056969, -0.00690408, -0.00323846,  0.00042715,  0.00409276,\n", "         0.00775837,  0.01142399,  0.0150896 ,  0.01875521,  0.02242082,\n", "         0.02608644,  0.02975205,  0.03341766,  0.03708327,  0.04074889,\n", "         0.0444145 ,  0.04808011,  0.05174572,  0.05541134,  0.05907695,\n", "         0.06274256,  0.06640817,  0.07007379,  0.0737394 ,  0.07740501,\n", "         0.08107062,  0.08473624,  0.08840185,  0.09206746,  0.09573308,\n", "         0.09939869,  0.1030643 ,  0.10672991,  0.11039552,  0.11406114,\n", "         0.11772675,  0.12139236,  0.12505797,  0.12872359,  0.1323892 ,\n", "         0.13605481,  0.13972042,  0.14338604,  0.14705165,  0.15071726,\n", "         0.15438288,  0.15804849,  0.1617141 ,  0.16537971,  0.16904532,\n", "         0.17271094,  0.17637655,  0.18004216,  0.18370777,  0.18737339,\n", "         0.191039  ,  0.19470461,  0.19837022,  0.20203584,  0.20570145,\n", "         0.20936706,  0.21303267,  0.21669829,  0.2203639 ,  0.22402951,\n", "         0.22769512,  0.23136074,  0.23502635,  0.23869196,  0.24235757,\n", "         0.24602319,  0.2496888 ,  0.25335441,  0.25702002,  0.26068564,\n", "         0.26435125]),\n", " <BarContainer object of 100 artists>)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.hist(data['blue'], bins=100, edgecolor='black')"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\geospatial\\lib\\site-packages\\scipy\\__init__.py:155: UserWarning: A NumPy version >=1.18.5 and <1.25.0 is required for this version of SciPy (detected version 1.26.4\n", "  warnings.warn(f\"A NumPy version >={np_minversion} and <{np_maxversion}\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["28\n", "Best Parameters:  {'min_samples_leaf': 20, 'n_estimators': 82}\n", "Best Score:  0.9821364998850527\n", "Accuracy on test set: 0.9811\n"]}], "source": ["import pandas as pd\n", "import geopandas as gpd\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.model_selection import train_test_split, GridSearchCV\n", "from sklearn.metrics import cohen_kappa_score, fbeta_score\n", "\n", "# 指定特征列和目标列\n", "exclude_cols = ['water', 'geometry']\n", "feature_cols = [col for col in data.columns if col not in exclude_cols]\n", "\n", "remain_cols = [x for x in feature_cols]\n", "# remain_cols = ['MNDWI', 'NDWI', 'EVI', 'NDVI', 'BSI', 'LSWI', 'NDBI']\n", "\n", "target_col = 'water'  # 目标列\n", "\n", "# 将数据分为特征矩阵X和目标向量y\n", "X = data[remain_cols]\n", "print(len(remain_cols))\n", "y = data[target_col]\n", "\n", "# 分割数据为训练集和测试集\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "\n", "# 设置要调整的超参数及其值范围\n", "param_grid = {\n", "    'n_estimators': np.a<PERSON><PERSON>(1, 101),\n", "    'min_samples_leaf': [20],\n", "}\n", "# 创建随机森林分类器\n", "rf = RandomForestClassifier(random_state=42)\n", "\n", "grid_search = GridSearchCV(estimator=rf, param_grid=param_grid, cv=10, scoring='f1', n_jobs=-1, return_train_score=True)\n", "\n", "grid_search.fit(X_train, y_train)\n", "\n", "# 打印最佳参数组合和对应的分数\n", "print('Best Parameters: ', grid_search.best_params_)\n", "print('Best Score: ', grid_search.best_score_)\n", "\n", "# 使用最佳参数在测试集上评估模型\n", "rf_best = grid_search.best_estimator_\n", "score = rf_best.score(X_test, y_test)\n", "print(f'Accuracy on test set: {score:.4f}')\n", "\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0, 1, 2, 3, 4, 5, 6, 7, 8, 9])"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["np.<PERSON><PERSON><PERSON>(10)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'min_samples_leaf': 20, 'n_estimators': 8}"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["grid_search.best_params_"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# 获取每个参数组合的分数\n", "cv_results = grid_search.cv_results_\n", "params = cv_results['params']\n", "mean_train_scores = cv_results['mean_train_score']\n", "mean_test_scores = cv_results['mean_test_score']\n", "\n", "# 将结果存储在数据框中\n", "scores_df = pd.DataFrame({'params': params,\n", "                          'mean_train_score': mean_train_scores,\n", "                          'mean_test_score': mean_test_scores})"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["scores_df.to_csv('../Training/accuracy_changes.csv')"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>params</th>\n", "      <th>mean_train_score</th>\n", "      <th>mean_test_score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>{'min_samples_leaf': 20, 'n_estimators': 1}</td>\n", "      <td>0.980393</td>\n", "      <td>0.977790</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>{'min_samples_leaf': 20, 'n_estimators': 2}</td>\n", "      <td>0.982591</td>\n", "      <td>0.980061</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>{'min_samples_leaf': 20, 'n_estimators': 3}</td>\n", "      <td>0.983212</td>\n", "      <td>0.980604</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>{'min_samples_leaf': 20, 'n_estimators': 4}</td>\n", "      <td>0.983568</td>\n", "      <td>0.980800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>{'min_samples_leaf': 20, 'n_estimators': 5}</td>\n", "      <td>0.983749</td>\n", "      <td>0.981217</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>95</th>\n", "      <td>{'min_samples_leaf': 20, 'n_estimators': 96}</td>\n", "      <td>0.984587</td>\n", "      <td>0.982069</td>\n", "    </tr>\n", "    <tr>\n", "      <th>96</th>\n", "      <td>{'min_samples_leaf': 20, 'n_estimators': 97}</td>\n", "      <td>0.984572</td>\n", "      <td>0.982093</td>\n", "    </tr>\n", "    <tr>\n", "      <th>97</th>\n", "      <td>{'min_samples_leaf': 20, 'n_estimators': 98}</td>\n", "      <td>0.984574</td>\n", "      <td>0.982083</td>\n", "    </tr>\n", "    <tr>\n", "      <th>98</th>\n", "      <td>{'min_samples_leaf': 20, 'n_estimators': 99}</td>\n", "      <td>0.984577</td>\n", "      <td>0.982048</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99</th>\n", "      <td>{'min_samples_leaf': 20, 'n_estimators': 100}</td>\n", "      <td>0.984580</td>\n", "      <td>0.982059</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>100 rows × 3 columns</p>\n", "</div>"], "text/plain": ["                                           params  mean_train_score  \\\n", "0     {'min_samples_leaf': 20, 'n_estimators': 1}          0.980393   \n", "1     {'min_samples_leaf': 20, 'n_estimators': 2}          0.982591   \n", "2     {'min_samples_leaf': 20, 'n_estimators': 3}          0.983212   \n", "3     {'min_samples_leaf': 20, 'n_estimators': 4}          0.983568   \n", "4     {'min_samples_leaf': 20, 'n_estimators': 5}          0.983749   \n", "..                                            ...               ...   \n", "95   {'min_samples_leaf': 20, 'n_estimators': 96}          0.984587   \n", "96   {'min_samples_leaf': 20, 'n_estimators': 97}          0.984572   \n", "97   {'min_samples_leaf': 20, 'n_estimators': 98}          0.984574   \n", "98   {'min_samples_leaf': 20, 'n_estimators': 99}          0.984577   \n", "99  {'min_samples_leaf': 20, 'n_estimators': 100}          0.984580   \n", "\n", "    mean_test_score  \n", "0          0.977790  \n", "1          0.980061  \n", "2          0.980604  \n", "3          0.980800  \n", "4          0.981217  \n", "..              ...  \n", "95         0.982069  \n", "96         0.982093  \n", "97         0.982083  \n", "98         0.982048  \n", "99         0.982059  \n", "\n", "[100 rows x 3 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["scores_df"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "colors = sns.color_palette(\"colorblind\")\n", "plt.rcParams['font.family'] = 'Times New Roman'\n", "plt.rcParams['font.size'] = 18"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["scores_df = pd.read_csv('../Training/accuracy_changes.csv')"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(15.972222222222216, 0.5, 'F1 score')"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 700x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(figsize=(7, 4))\n", "plt.plot(scores_df['mean_test_score'], color=colors[0])\n", "plt.axvline(10, color=colors[1], linestyle='--')  # 绘制线条\n", "plt.savefig('../Plot/精度随随机森林棵树的变化.tif', dpi=600)\n", "plt.xlabel('Number of trees')\n", "plt.y<PERSON><PERSON>('F1 score')"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [], "source": ["# 训练模型\n", "rf_best.fit(X_train, y_train)\n", "y_pred = rf_best.predict(X_test)"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Accuracy on test set: 0.981\n", "Kappa score: 0.963\n", "F2 score: 0.981\n", "Feature Importance:        feature  importance\n", "10    swir1_1    0.166595\n", "7        NDVI    0.130827\n", "37      MNDWI    0.122202\n", "4       swir1    0.120070\n", "33       NDWI    0.105098\n", "28    EVI_1_1    0.076419\n", "41   NDVI_1_1    0.074670\n", "25  elevation    0.071795\n", "6       swir2    0.023825\n", "43        EVI    0.022164\n", "3     MNDWI_1    0.011226\n", "2   MNDWI_1_1    0.009647\n", "44     NDWI_1    0.009413\n", "32      slope    0.005328\n", "16        nir    0.005072\n", "39      nir_1    0.003963\n", "24      EVI_1    0.003907\n", "18     NDBI_1    0.002359\n", "40    nir_1_1    0.002070\n", "27         st    0.001934\n", "22     NDVI_1    0.001905\n", "17     aspect    0.001644\n", "9      st_1_1    0.001625\n", "36       blue    0.001524\n", "45       LSWI    0.001479\n", "31       st_1    0.001446\n", "0       BSI_1    0.001434\n", "12        red    0.001419\n", "29      green    0.001417\n", "34     blue_1    0.001382\n", "1    NDWI_1_1    0.001346\n", "20       NDBI    0.001318\n", "21        BSI    0.001284\n", "26     LSWI_1    0.001202\n", "30  swir2_1_1    0.001077\n", "38      red_1    0.001043\n", "13    BSI_1_1    0.001028\n", "8     swir2_1    0.001007\n", "15  swir1_1_1    0.000948\n", "5    blue_1_1    0.000941\n", "42    red_1_1    0.000941\n", "23    green_1    0.000915\n", "35   NDBI_1_1    0.000833\n", "11  green_1_1    0.000823\n", "14  hillshade    0.000769\n", "19   LSWI_1_1    0.000665\n"]}], "source": ["rf = rf_best\n", "# 在测试集上评估模型\n", "score = rf.score(X_test, y_test)    \n", "print(f'Accuracy on test set: {score:.3f}')\n", "\n", "# 计算Kappa系数\n", "kappa = cohen_kappa_score(y_test, y_pred)\n", "print(f'Kappa score: {kappa:.3f}')\n", "\n", "# 计算F2分数\n", "f2 = fbeta_score(y_test, y_pred, beta=2, average='macro')\n", "print(f'F2 score: {f2:.3f}')\n", "\n", "feature_importances = rf.feature_importances_\n", "importances_df = pd.DataFrame({'feature': X_train.columns, 'importance': feature_importances})\n", "importances_df = importances_df.sort_values('importance', ascending=False)\n", "importances_df.to_csv('importances_df.csv')\n", "print('Feature Importance: ', importances_df)"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "geospatial", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}