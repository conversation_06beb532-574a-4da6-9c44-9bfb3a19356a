import os
import math
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import List, Dict

# 数据路径（可被命令行参数覆盖）
DEFAULT_PATHS = {
    "points_jrc_gap": r"D:\STNS\STNS\Global_validation\参数15\points_jrc_gap.csv",   # 提供 count_clean
    "points_jrc": r"D:\STNS\STNS\Global_validation\参数15\points_jrc.csv",            # 提供 gaps_rate
    "terrain": r"D:\STNS\STNS\Global_validation\参数15\terrain.csv",                 # 提供 slope
    "contribution": r"D:\STNS\STNS\Global_validation\参数15\contribution_analysis.csv", # 提供 contrib_*
    "validation": r"D:\STNS\STNS\Global_validation\参数15\validation.csv",            # 提供 F1_spatial/F1_temporal 等绝对指标
}
OUTPUT_FIG = r"D:\STNS\STNS\Code\contrib_spatial_temporal_vs_factors.png"

# 兼容可能的列名别名
ALIASES: Dict[str, List[str]] = {
    "id": ["id", "ID", "point_id", "pid"],
    "type": ["type", "Type", "TYPE", "point_type"],
    "gaps_rate": ["gaps_rate", "gap_rate", "gaps", "gapsrate"],
    "count_clean": ["count_clean", "clean_count", "n_clean", "clean_samples"],
    "slope": ["slope", "Slope", "slope_deg", "slope_degree", "slope_degrees"],
    "contrib_spatial_ratio": ["contrib_spatial_ratio", "spatial_ratio", "contrib_spatial", "spatial_contrib"],
    "contrib_temporal_ratio": ["contrib_temporal_ratio", "temporal_ratio", "contrib_temporal", "temporal_contrib"],
    # 绝对指标（来自 validation.csv）
    "f1_spatial": ["f1_spatial", "F1_spatial"],
    "f1_temporal": ["f1_temporal", "F1_temporal"],
}


def _try_read_csv(path: str) -> pd.DataFrame:
    encs = ["utf-8-sig", "utf-8", "gbk", "cp936", None]
    engines = [None, "python"]
    last = None
    for enc in encs:
        for eng in engines:
            try:
                return pd.read_csv(path, encoding=enc, engine=eng)
            except Exception as e:
                last = e
    raise RuntimeError(f"读取CSV失败: {path}; 最后错误: {last}")


def _norm_cols(df: pd.DataFrame) -> pd.DataFrame:
    out = df.copy()
    out.columns = [str(c).strip().lower().replace(" ", "_") for c in out.columns]
    return out


def _find_col(df: pd.DataFrame, alias: List[str]) -> str:
    cols = set(df.columns)
    for a in alias:
        k = str(a).strip().lower().replace(" ", "_")
        if k in cols:
            return k
    raise KeyError(f"找不到列，尝试别名: {alias}; 现有列: {list(df.columns)}")


def _select_rename(df: pd.DataFrame, needs: Dict[str, List[str]]) -> pd.DataFrame:
    d = _norm_cols(df)
    mapping = {k: _find_col(d, v) for k, v in needs.items()}
    sub = d[list(mapping.values())].copy().rename(columns={v: k for k, v in mapping.items()})
    return sub


def _to_num(df: pd.DataFrame, cols: List[str]) -> pd.DataFrame:
    x = df.copy()
    for c in cols:
        x[c] = pd.to_numeric(x[c], errors="coerce")
    return x


def _dedup(df: pd.DataFrame, keys: List[str]) -> pd.DataFrame:
    if not df.duplicated(keys).any():
        return df
    num_cols = [c for c in df.columns if c not in keys and pd.api.types.is_numeric_dtype(df[c])]
    oth_cols = [c for c in df.columns if c not in keys + num_cols]
    agg = {**{c: "mean" for c in num_cols}, **{c: "first" for c in oth_cols}}
    return df.groupby(keys, as_index=False).agg(agg)


def load_and_merge(paths: Dict[str, str], mode: str = "relative") -> pd.DataFrame:
    jrc = _try_read_csv(paths["points_jrc"])             # gaps_rate
    gap = _try_read_csv(paths["points_jrc_gap"])         # count_clean
    ter = _try_read_csv(paths["terrain"])                # slope

    jrc = _select_rename(jrc, {"id": ALIASES["id"], "type": ALIASES["type"], "gaps_rate": ALIASES["gaps_rate"]})
    gap = _select_rename(gap, {"id": ALIASES["id"], "type": ALIASES["type"], "count_clean": ALIASES["count_clean"]})
    ter = _select_rename(ter, {"id": ALIASES["id"], "type": ALIASES["type"], "slope": ALIASES["slope"]})

    if mode == "relative":
        con = _try_read_csv(paths["contribution"])           # contrib ratios
        con = _select_rename(con, {"id": ALIASES["id"], "type": ALIASES["type"],
                                   "contrib_spatial_ratio": ALIASES["contrib_spatial_ratio"],
                                   "contrib_temporal_ratio": ALIASES["contrib_temporal_ratio"]})
        con = _to_num(con, ["contrib_spatial_ratio", "contrib_temporal_ratio"])
    elif mode == "absolute":
        val = _try_read_csv(paths["validation"])             # absolute metrics from validation
        val = _select_rename(val, {"id": ALIASES["id"], "type": ALIASES["type"],
                                   "f1_spatial": ALIASES["f1_spatial"],
                                   "f1_temporal": ALIASES["f1_temporal"]})
        val = _to_num(val, ["f1_spatial", "f1_temporal"])
        # 为了复用下游绘图逻辑，将列名统一映射为 contrib_*
        con = val.rename(columns={"f1_spatial": "contrib_spatial_ratio", "f1_temporal": "contrib_temporal_ratio"})
    else:
        raise ValueError("mode 仅支持 'relative' 或 'absolute'")

    jrc = _to_num(jrc, ["gaps_rate"]) ; gap = _to_num(gap, ["count_clean"]) ; ter = _to_num(ter, ["slope"])

    keys = ["id", "type"]
    jrc = _dedup(jrc, keys); gap = _dedup(gap, keys); ter = _dedup(ter, keys); con = _dedup(con, keys)

    df = con.merge(jrc, on=keys, how="inner").merge(gap, on=keys, how="inner").merge(ter, on=keys, how="inner")
    df = df.dropna(subset=["contrib_spatial_ratio", "contrib_temporal_ratio", "gaps_rate", "count_clean", "slope"]).reset_index(drop=True)
    return df


def _bins_by_range(vmin: float, vmax: float, step: float) -> np.ndarray:
    if not np.isfinite(vmin) or not np.isfinite(vmax):
        return np.array([0, 1])
    if vmax <= vmin:
        vmax = vmin + step
    start = math.floor(vmin / step) * step
    end = math.ceil(vmax / step) * step
    return np.arange(start, end + step, step)


def _label(cat) -> str:
    if pd.isna(cat):
        return "NA"
    try:
        left, right = cat.left, cat.right
        return f"[{left:.0f},{right:.0f})"
    except Exception:
        return str(cat)


def analyze_and_plot(df: pd.DataFrame, mode: str = "relative", save_path: str = OUTPUT_FIG):
    # 字体
    try:
        plt.rcParams["font.sans-serif"] = ["SimHei", "Microsoft YaHei", "Arial Unicode MS", "DejaVu Sans"]
        plt.rcParams["axes.unicode_minus"] = False
    except Exception:
        pass

    # 分箱：gaps_rate按范围1000步长；count_clean、slope按分位数（退化到等宽）
    gr_bins = _bins_by_range(min(0, df.gaps_rate.min()), max(10000, df.gaps_rate.max()), 1000)
    df["gaps_rate_bin"] = pd.cut(df["gaps_rate"], bins=gr_bins, include_lowest=True, duplicates="drop")

    try:
        df["count_clean_bin"] = pd.qcut(df["count_clean"], q=10, duplicates="drop")
    except Exception:
        cc_bins = _bins_by_range(df["count_clean"].min(), df["count_clean"].max(), max(1.0, (df["count_clean"].max()-df["count_clean"].min())/10))
        df["count_clean_bin"] = pd.cut(df["count_clean"], bins=cc_bins, include_lowest=True, duplicates="drop")

    try:
        df["slope_bin"] = pd.qcut(df["slope"], q=10, duplicates="drop")
    except Exception:
        sl_bins = _bins_by_range(df["slope"].min(), df["slope"].max(), max(1.0, (df["slope"].max()-df["slope"].min())/10))
        df["slope_bin"] = pd.cut(df["slope"], bins=sl_bins, include_lowest=True, duplicates="drop")

    def summarize(by):
        g = df.groupby(by)[["contrib_spatial_ratio", "contrib_temporal_ratio"]].mean().reset_index()
        return g

    g_type = summarize("type").sort_values("type")
    # 使用分箱类别的天然顺序，避免字符串排序导致的区间错位
    g_gap = df.groupby("gaps_rate_bin")[ ["contrib_spatial_ratio", "contrib_temporal_ratio"] ].mean().reset_index()
    g_gap = g_gap.sort_values("gaps_rate_bin")
    g_gap["label"] = g_gap["gaps_rate_bin"].apply(_label)

    g_cc = df.groupby("count_clean_bin")[ ["contrib_spatial_ratio", "contrib_temporal_ratio"] ].mean().reset_index()
    g_cc = g_cc.sort_values("count_clean_bin")
    g_cc["label"] = g_cc["count_clean_bin"].apply(_label)

    g_sl = df.groupby("slope_bin")[ ["contrib_spatial_ratio", "contrib_temporal_ratio"] ].mean().reset_index()
    g_sl = g_sl.sort_values("slope_bin")
    g_sl["label"] = g_sl["slope_bin"].apply(_label)

    # 绘图：2x2子图，每图两条曲线（空间/时间）
    fig, axes = plt.subplots(2, 2, figsize=(16, 10))
    if mode == "relative":
        fig.suptitle("空间与时间贡献比随类型与因子变化", fontsize=16)
        series1_label, series2_label, y_label = "空间贡献比", "时间贡献比", "贡献比"
    else:
        fig.suptitle("空间与时间绝对指标(F1)随类型与因子变化", fontsize=16)
        series1_label, series2_label, y_label = "F1_spatial", "F1_temporal", "F1"

    def draw(ax, x, y1, y2, title, xlabel):
        ax.plot(x, y1, marker="o", label=series1_label)
        ax.plot(x, y2, marker="s", label=series2_label)
        ax.set_title(title)
        ax.set_xlabel(xlabel)
        ax.set_ylabel(y_label)
        ax.grid(True, alpha=0.3)
        ax.tick_params(axis='x', rotation=45 if len(x) > 6 else 0)
        ax.legend()

    draw(axes[0, 0], g_type["type"].astype(str), g_type["contrib_spatial_ratio"], g_type["contrib_temporal_ratio"], "随类型(type)变化", "type")
    draw(axes[0, 1], g_gap["label"].astype(str), g_gap["contrib_spatial_ratio"], g_gap["contrib_temporal_ratio"], "随缺口率(gaps_rate)分箱变化", "gaps_rate区间")
    draw(axes[1, 0], g_cc["label"].astype(str), g_cc["contrib_spatial_ratio"], g_cc["contrib_temporal_ratio"], "随清洗样本数(count_clean)分箱变化", "count_clean区间")
    draw(axes[1, 1], g_sl["label"].astype(str), g_sl["contrib_spatial_ratio"], g_sl["contrib_temporal_ratio"], "随坡度(slope)分箱变化", "slope区间")

    plt.tight_layout(rect=[0, 0, 1, 0.96])
    out_dir = os.path.dirname(save_path)
    if out_dir and not os.path.exists(out_dir):
        os.makedirs(out_dir, exist_ok=True)
    plt.savefig(save_path, dpi=200)
    print(f"图像已保存: {save_path}")
    plt.show()



def compute_partial_correlations(df: pd.DataFrame, mode: str = "relative") -> pd.DataFrame:
    """
    计算在控制其余变量后，type/gaps_rate/count_clean/slope 对 目标(空间/时间)的偏相关，并进行显著性检验。
    目标列根据 mode：
      - relative: contrib_spatial_ratio, contrib_temporal_ratio
      - absolute: F1_spatial, F1_temporal (内部已映射为上述两列名)
    返回：DataFrame（系数），并在控制台打印系数与 p 值（两侧检验）。
    """
    predictors = ["type", "gaps_rate", "count_clean", "slope"]
    # 目标列在 df 中统一为 contrib_spatial_ratio / contrib_temporal_ratio
    target_cols = ["contrib_spatial_ratio", "contrib_temporal_ratio"]

    def _to_num_or_codes(s: pd.Series) -> np.ndarray:
        sn = pd.to_numeric(s, errors="coerce")
        if sn.isna().sum() > 0 and s.notna().sum() > sn.notna().sum():
            # 存在非数值：转为分类编码
            codes = pd.Categorical(s.astype(str)).codes.astype(float)
            return codes
        return sn.to_numpy(dtype=float)

    # 构造 Xfull (n x p)
    Xcols = []
    for c in predictors:
        arr = _to_num_or_codes(df[c])
        # 标准化（避免尺度影响）
        mu, sd = np.nanmean(arr), np.nanstd(arr)
        if not np.isfinite(sd) or sd == 0:
            Xcols.append(np.zeros_like(arr))
        else:
            Xcols.append((arr - mu) / sd)
    Xfull = np.vstack(Xcols).T  # shape (n,p)

    def _pval_from_r(r: float, dof: int) -> float:
        if not np.isfinite(r) or not np.isfinite(dof) or dof <= 0:
            return np.nan
        r = max(min(r, 0.999999), -0.999999)  # 避免 1/-1 导致数值问题
        # 优先使用 SciPy 的 t 分布
        try:
            from scipy import stats  # type: ignore
            t = r * math.sqrt(dof / max(1e-12, 1 - r*r))
            p = 2 * stats.t.sf(abs(t), df=dof)
            return float(p)
        except Exception:
            # 后备：Fisher 变换 + 正态近似（大样本）
            # z = atanh(r) * sqrt(n - m - 3)，其中 dof = n - m - 2 => 近似使用 sqrt(dof - 1)
            try:
                z = 0.5 * math.log((1 + r) / (1 - r)) * math.sqrt(max(dof - 1, 1))
                # 正态分布两侧 p 值：2 * (1 - Phi(|z|))；Phi 可用 erf 实现
                p = 2 * (1 - 0.5 * (1 + math.erf(abs(z) / math.sqrt(2))))
                return float(p)
            except Exception:
                return np.nan

    def _partial_corr_for_target(y: np.ndarray) -> tuple[np.ndarray, np.ndarray, int]:
        y = y.astype(float)
        # 有效样本掩码（去除 NaN/Inf）
        mask_valid = np.isfinite(y)
        mask_valid &= np.all(np.isfinite(Xfull), axis=1)
        Y = y[mask_valid]
        X = Xfull[mask_valid, :]
        n, p = X.shape
        res_r = np.full(p, np.nan, dtype=float)
        res_p = np.full(p, np.nan, dtype=float)
        for k in range(p):
            # 控制其余变量
            other_idx = [i for i in range(p) if i != k]
            Xo = X[:, other_idx]
            ones = np.ones((n, 1), dtype=float)
            Xo_i = np.hstack([ones, Xo])
            # y residual
            try:
                beta_y, *_ = np.linalg.lstsq(Xo_i, Y, rcond=None)
                ry = Y - Xo_i @ beta_y
            except Exception:
                continue
            # xk residual
            xk = X[:, k]
            try:
                beta_x, *_ = np.linalg.lstsq(Xo_i, xk, rcond=None)
                rx = xk - Xo_i @ beta_x
            except Exception:
                continue
            sy, sx = np.std(ry), np.std(rx)
            if sy == 0 or sx == 0:
                res_r[k] = np.nan
                res_p[k] = np.nan
            else:
                r = float(np.corrcoef(ry, rx)[0, 1])
                res_r[k] = r
                # 偏相关的自由度：dof = n - m - 2，其中 m = 控制变量数 = p-1
                dof = n - (p - 1) - 2
                res_p[k] = _pval_from_r(r, dof)
        return res_r, res_p, n

    results_r = {}
    results_p = {}
    ns_used = {}
    for tcol in target_cols:
        rvec, pvec, n_used = _partial_corr_for_target(df[tcol].to_numpy())
        results_r[tcol] = rvec
        results_p[tcol] = pvec
        ns_used[tcol] = n_used

    coef_df = pd.DataFrame(results_r, index=predictors)
    pval_df = pd.DataFrame(results_p, index=predictors)

    # 友好列名
    if mode == "relative":
        coef_df = coef_df.rename(columns={
            "contrib_spatial_ratio": "偏相关(空间贡献比)",
            "contrib_temporal_ratio": "偏相关(时间贡献比)",
        })
        pval_df = pval_df.rename(columns={
            "contrib_spatial_ratio": "p值(空间贡献比)",
            "contrib_temporal_ratio": "p值(时间贡献比)",
        })
    else:
        coef_df = coef_df.rename(columns={
            "contrib_spatial_ratio": "偏相关(F1_spatial)",
            "contrib_temporal_ratio": "偏相关(F1_temporal)",
        })
        pval_df = pval_df.rename(columns={
            "contrib_spatial_ratio": "p值(F1_spatial)",
            "contrib_temporal_ratio": "p值(F1_temporal)",
        })

    def _star(p: float) -> str:
        if not np.isfinite(p):
            return ""
        if p < 0.001:
            return "***"
        if p < 0.01:
            return "**"
        if p < 0.05:
            return "*"
        if p < 0.1:
            return "."
        return ""

    print("\n偏相关系数（控制其余变量）：")
    try:
        with pd.option_context('display.max_rows', None, 'display.max_columns', None, 'display.width', 160):
            print(coef_df.round(4))
    except Exception:
        print(coef_df)

    print("\n显著性检验 p 值（两侧）：")
    try:
        with pd.option_context('display.max_rows', None, 'display.max_columns', None, 'display.width', 160):
            print(pval_df.applymap(lambda x: np.nan if not np.isfinite(x) else round(float(x), 4)))
    except Exception:
        print(pval_df)

    # 同时打印显著性星标版本，便于快速查看
    try:
        star_df = pval_df.applymap(_star)
        print("\n显著性标记(*** <0.001, ** <0.01, * <0.05, . <0.1)：")
        print(star_df)
    except Exception:
        pass

    return coef_df


def main(points_jrc_gap_path=DEFAULT_PATHS["points_jrc_gap"],
         points_jrc_path=DEFAULT_PATHS["points_jrc"],
         terrain_path=DEFAULT_PATHS["terrain"],
         contribution_path=DEFAULT_PATHS["contribution"],
         validation_path=DEFAULT_PATHS["validation"],
         mode: str = "relative",
         output_fig=OUTPUT_FIG):
    # 校验存在
    must_exist = [points_jrc_gap_path, points_jrc_path, terrain_path]
    if mode == "relative":
        must_exist.append(contribution_path)
    elif mode == "absolute":
        must_exist.append(validation_path)
    else:
        raise ValueError("mode 仅支持 'relative' 或 'absolute'")
    for p in must_exist:
        if not os.path.exists(p):
            raise FileNotFoundError(f"文件不存在: {p}")

    df = load_and_merge({
        "points_jrc_gap": points_jrc_gap_path,
        "points_jrc": points_jrc_path,
        "terrain": terrain_path,
        "contribution": contribution_path,
        "validation": validation_path,
    }, mode=mode)
    print("合并后数据示例:\n", df.head())
    analyze_and_plot(df, mode=mode, save_path=output_fig)
    # 计算并输出偏相关
    _ = compute_partial_correlations(df, mode=mode)


if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description="分析相对贡献(contrib_*)或绝对指标(F1_spatial/F1_temporal)随type/gaps_rate/count_clean/slope变化并绘图")
    parser.add_argument("--jrc_gap", default=DEFAULT_PATHS["points_jrc_gap"], help="points_jrc_gap.csv 路径(count_clean)")
    parser.add_argument("--jrc", default=DEFAULT_PATHS["points_jrc"], help="points_jrc.csv 路径(gaps_rate)")
    parser.add_argument("--terrain", default=DEFAULT_PATHS["terrain"], help="terrain.csv 路径(slope)")
    parser.add_argument("--contrib", default=DEFAULT_PATHS["contribution"], help="contribution_analysis.csv 路径(相对贡献)")
    parser.add_argument("--validation", default=DEFAULT_PATHS["validation"], help="validation.csv 路径(绝对指标)")
    parser.add_argument("--mode", choices=["relative", "absolute"], default="relative", help="选择分析相对贡献(relative)或绝对指标(absolute)")
    parser.add_argument("--out", default=OUTPUT_FIG, help="输出图像路径")
    a = parser.parse_args()
    main(a.jrc_gap, a.jrc, a.terrain, a.contrib, a.validation, a.mode, a.out)

