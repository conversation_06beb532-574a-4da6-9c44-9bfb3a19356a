import arcpy
import glob
import os
from arcpy.sa import *

arcpy.CheckOutExtension("Spatial")

inws = r"C:\Users\<USER>\Desktop\ChinaWater"
base = r"C:\Users\<USER>\Desktop\ChinaWater\1990.tif"
out_coor_system = arcpy.Describe(base).spatialReference
arcpy.env.parallelProcessingFactor = "90%"
arcpy.env.workspace = r"C:\Users\<USER>\Desktop"

rasters = glob.glob(os.path.join(inws, "*.tif"))

print(rasters)
for raster in rasters:
    raster = Divide(raster,100)
    name = os.path.basename(raster)[:4]
    print(name)

    arcpy.CopyRaster_management(rastrer, name+'.tif', pixel_type = '8_BIT_SIGNED', format = 'TIFF')
