from __future__ import annotations
from pathlib import Path
import pandas as pd


def analyze_contributions(
    csv_path: Path = Path("Global_validation") / "参数15" / "validation.csv",
    output_path: Path | None = Path("Global_validation") / "参数15" / "contribution_analysis.csv",
    metric_prefix: str = "F1",  # 可选："F1" 或 "acc"
) -> dict:
    """
    分析空间与时间信息对联合重建精度（F1_rec）的相对贡献。

    方法说明（相对贡献占比）:
    - 仅在满足 F1_rec > F1_spatial 且 F1_rec > F1_temporal 的样本中评估贡献。
    - 以各自的边际增益归一化作为相对贡献：
        delta_spatial = F1_rec - F1_temporal  # 在“时间”基线下引入“空间”的增益
        delta_temporal = F1_rec - F1_spatial  # 在“空间”基线下引入“时间”的增益
        contrib_spatial_ratio = delta_spatial / (delta_spatial + delta_temporal)
        contrib_temporal_ratio = delta_temporal / (delta_spatial + delta_temporal)
      该方法对两个信息源是对称的，数值位于 [0,1] 且二者相加为 1。

    返回值: 包含关键统计量的字典。
    """

    csv_path = Path(csv_path)
    if output_path is not None:
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)

    # 读取数据
    df = pd.read_csv(csv_path, encoding="utf-8-sig")

    # 构造列名（支持 F1 或 acc 前缀）
    if metric_prefix not in ("F1", "acc"):
        raise ValueError(f"metric_prefix 仅支持 'F1' 或 'acc'，当前: {metric_prefix}")

    col_rec = f"{metric_prefix}_rec"
    col_spatial = f"{metric_prefix}_spatial"
    col_temporal = f"{metric_prefix}_temporal"

    # 确保关键列存在
    required_cols = [col_rec, col_spatial, col_temporal]
    for c in required_cols:
        if c not in df.columns:
            raise KeyError(f"缺少必要列: {c}")

    # 丢弃缺失值样本（只针对关键列）
    df_key = df.dropna(subset=required_cols).copy()

    # 统计 1：仅空间 信息优于 仅时间 信息 的样本数量
    n_spatial_gt_temporal = int((df_key[col_spatial] > df_key[col_temporal]).sum())

    # 统计 2：联合信息优于两个单一信息 的样本数量
    mask_joint_better = (df_key[col_rec] > df_key[col_spatial]) & (df_key[col_rec] > df_key[col_temporal])
    n_joint_gt_both = int(mask_joint_better.sum())

    # 在该子集上计算相对贡献占比
    sub = df_key.loc[mask_joint_better].copy()
    sub["delta_spatial"] = sub[col_rec] - sub[col_temporal]
    sub["delta_temporal"] = sub[col_rec] - sub[col_spatial]
    sub["delta_sum"] = sub["delta_spatial"] + sub["delta_temporal"]

    # 数值稳定性（理论上 delta_sum>0，因为 F1_rec/acc_rec>两者；这里仍做保护）
    sub = sub[sub["delta_sum"] > 0]

    sub["contrib_spatial_ratio"] = sub["delta_spatial"] / sub["delta_sum"]
    sub["contrib_temporal_ratio"] = sub["delta_temporal"] / sub["delta_sum"]
    sub["improvement_over_best_single"] = sub[col_rec] - sub[[col_spatial, col_temporal]].max(axis=1)

    # 导出结果（只保留常用标识与关键字段）
    keep_cols = [
        # 标识列（若存在则一并导出，便于回溯）
        col for col in ["system:index", "id", "year", "month", "type"] if col in sub.columns
    ] + [
        col_rec, col_spatial, col_temporal,
        "delta_spatial", "delta_temporal", "improvement_over_best_single",
        "contrib_spatial_ratio", "contrib_temporal_ratio",
    ]

    out_df = sub[keep_cols].copy()

    if output_path is not None:
        out_df.to_csv(output_path, index=False, encoding="utf-8-sig")

    stats = {
        "total_samples": int(len(df)),
        "valid_samples": int(len(df_key)),
        "n_spatial_gt_temporal": n_spatial_gt_temporal,
        "n_joint_gt_both": n_joint_gt_both,
        "output_path": str(output_path) if output_path is not None else None,
    }
    return stats


if __name__ == "__main__":
    stats = analyze_contributions()
    print("分析完成：")
    for k, v in stats.items():
        print(f"  {k}: {v}")
