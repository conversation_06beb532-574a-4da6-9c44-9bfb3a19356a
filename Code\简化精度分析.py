import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score
from scipy import stats
from scipy.stats import pearsonr
import warnings
warnings.filterwarnings('ignore')

# 数据路径
DEFAULT_PATHS = { 
    "points_jrc_gap": r"D:\STNS\STNS\Global_validation\参数15\points_jrc_gap.csv",   # 提供 count_clean
    "points_jrc": r"D:\STNS\STNS\Global_validation\参数15\points_jrc.csv",            # 提供 gaps_rate
    "terrain": r"D:\STNS\STNS\Global_validation\参数15\terrain.csv",                 # 提供 slope
    "validation": r"D:\STNS\STNS\Global_validation\参数15\validation.csv",            # 提供 F1_rec, acc_rec
}

# 兼容可能的列名别名
ALIASES = {
    "id": ["id", "ID", "point_id", "pid"],
    "type": ["type", "Type", "TYPE", "point_type"],
    "gaps_rate": ["gaps_rate", "gap_rate", "gaps", "gapsrate"],
    "count_clean": ["count_clean", "clean_count", "n_clean", "clean_samples"],
    "slope": ["slope", "Slope", "slope_deg", "slope_degree", "slope_degrees"],
    "f1_rec": ["f1_rec", "F1_rec", "f1_reconstruction", "F1_reconstruction"],
    "acc_rec": ["acc_rec", "Acc_rec", "acc_reconstruction", "Acc_reconstruction"],
}

def _try_read_csv(path: str) -> pd.DataFrame:
    """尝试多种编码读取CSV文件"""
    encs = ["utf-8-sig", "utf-8", "gbk", "cp936", None]
    for enc in encs:
        try:
            return pd.read_csv(path, encoding=enc)
        except:
            continue
    raise RuntimeError(f"读取CSV失败: {path}")

def _norm_cols(df: pd.DataFrame) -> pd.DataFrame:
    """标准化列名"""
    out = df.copy()
    out.columns = [str(c).strip().lower().replace(" ", "_") for c in out.columns]
    return out

def _find_col(df: pd.DataFrame, alias: list) -> str:
    """根据别名查找列"""
    cols = set(df.columns)
    for a in alias:
        k = str(a).strip().lower().replace(" ", "_")
        if k in cols:
            return k
    raise KeyError(f"找不到列，尝试别名: {alias}")

def _select_rename(df: pd.DataFrame, needs: dict) -> pd.DataFrame:
    """选择并重命名列"""
    d = _norm_cols(df)
    mapping = {k: _find_col(d, v) for k, v in needs.items()}
    sub = d[list(mapping.values())].copy().rename(columns={v: k for k, v in mapping.items()})
    return sub

def load_data(paths: dict) -> pd.DataFrame:
    """加载并合并数据"""
    # 读取各个文件
    jrc = _try_read_csv(paths["points_jrc"])
    gap = _try_read_csv(paths["points_jrc_gap"])
    ter = _try_read_csv(paths["terrain"])
    val = _try_read_csv(paths["validation"])
    
    # 选择和重命名列
    jrc = _select_rename(jrc, {"id": ALIASES["id"], "gaps_rate": ALIASES["gaps_rate"]})
    gap = _select_rename(gap, {"id": ALIASES["id"], "count_clean": ALIASES["count_clean"]})
    ter = _select_rename(ter, {"id": ALIASES["id"], "slope": ALIASES["slope"]})
    val = _select_rename(val, {"id": ALIASES["id"], "f1_rec": ALIASES["f1_rec"], "acc_rec": ALIASES["acc_rec"]})
    
    # 转换为数值类型
    for df in [jrc, gap, ter, val]:
        for col in df.columns:
            if col != "id":
                df[col] = pd.to_numeric(df[col], errors="coerce")
    
    # 合并数据
    df = val.merge(jrc, on="id", how="inner")\
           .merge(gap, on="id", how="inner")\
           .merge(ter, on="id", how="inner")
    
    # 删除缺失值
    df = df.dropna().reset_index(drop=True)
    return df

def partial_correlation(x, y, z):
    """计算偏相关系数"""
    # 计算简单相关系数
    rxy = pearsonr(x, y)[0]
    rxz = pearsonr(x, z)[0]
    ryz = pearsonr(y, z)[0]
    
    # 计算偏相关系数
    numerator = rxy - rxz * ryz
    denominator = np.sqrt((1 - rxz**2) * (1 - ryz**2))
    
    if denominator == 0:
        return np.nan
    
    return numerator / denominator

def compute_partial_correlations(df):
    """计算偏相关系数"""
    print("=" * 60)
    print("偏相关分析")
    print("=" * 60)
    
    variables = ['gaps_rate', 'slope', 'count_clean']
    targets = ['acc_rec', 'f1_rec']
    
    results = {}
    
    for target in targets:
        print(f"\n{target}的偏相关系数:")
        print("-" * 40)
        target_results = {}
        
        for i, var in enumerate(variables):
            # 控制其他变量
            other_vars = [v for j, v in enumerate(variables) if j != i]
            
            if len(other_vars) == 1:
                # 控制一个变量
                partial_corr = partial_correlation(df[var], df[target], df[other_vars[0]])
            else:
                # 控制多个变量 - 使用线性回归残差方法
                # 对目标变量回归其他变量
                X_other = df[other_vars].values
                y_target = df[target].values
                reg_target = LinearRegression().fit(X_other, y_target)
                residual_target = y_target - reg_target.predict(X_other)
                
                # 对当前变量回归其他变量
                y_var = df[var].values
                reg_var = LinearRegression().fit(X_other, y_var)
                residual_var = y_var - reg_var.predict(X_other)
                
                # 计算残差的相关系数
                partial_corr = pearsonr(residual_var, residual_target)[0]
            
            target_results[var] = partial_corr
            print(f"  {var}: {partial_corr:.4f}")
        
        results[target] = target_results
    
    return results

def regression_analysis(df):
    """回归分析"""
    print("\n" + "=" * 60)
    print("回归分析")
    print("=" * 60)
    
    X_vars = ['gaps_rate', 'slope', 'count_clean']
    y_vars = ['acc_rec', 'f1_rec']
    
    results = {}
    
    for y_var in y_vars:
        print(f"\n{y_var}的回归分析:")
        print("-" * 40)
        
        X = df[X_vars].values
        y = df[y_var].values
        
        # 线性回归
        model = LinearRegression()
        model.fit(X, y)
        y_pred = model.predict(X)
        
        # 计算R²
        r2 = r2_score(y, y_pred)
        
        print(f"R² = {r2:.4f}")
        print(f"截距 = {model.intercept_:.4f}")
        print("回归系数:")
        
        for var, coef in zip(X_vars, model.coef_):
            print(f"  {var}: {coef:.6f}")
        
        # 计算各变量与目标变量的简单相关系数
        print("简单相关系数:")
        correlations = {}
        for var in X_vars:
            corr, p_val = pearsonr(df[var], df[y_var])
            correlations[var] = {'correlation': corr, 'p_value': p_val}
            significance = "***" if p_val < 0.001 else "**" if p_val < 0.01 else "*" if p_val < 0.05 else ""
            print(f"  {var}: r={corr:.4f}, p={p_val:.4f} {significance}")
        
        results[y_var] = {
            'model': model,
            'r2': r2,
            'correlations': correlations
        }
    
    return results

def create_visualization(df, save_path="Code/简化精度分析结果.png"):
    """创建可视化图表"""
    plt.rcParams["font.sans-serif"] = ["SimHei", "Microsoft YaHei", "Arial Unicode MS"]
    plt.rcParams["axes.unicode_minus"] = False
    
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    
    X_vars = ['gaps_rate', 'slope', 'count_clean']
    y_vars = ['acc_rec', 'f1_rec']
    
    for i, y_var in enumerate(y_vars):
        for j, x_var in enumerate(X_vars):
            ax = axes[i, j]
            
            # 散点图
            ax.scatter(df[x_var], df[y_var], alpha=0.6, s=20)
            
            # 回归线
            z = np.polyfit(df[x_var], df[y_var], 1)
            p = np.poly1d(z)
            ax.plot(df[x_var], p(df[x_var]), "r-", alpha=0.8)
            
            # 计算相关系数
            corr, p_val = pearsonr(df[x_var], df[y_var])
            
            ax.set_xlabel(x_var)
            ax.set_ylabel(y_var)
            ax.set_title(f'{y_var} vs {x_var}')
            ax.text(0.05, 0.95, f'r = {corr:.3f}\np = {p_val:.3f}', 
                   transform=ax.transAxes, 
                   bbox=dict(boxstyle="round", facecolor="white", alpha=0.8),
                   verticalalignment='top')
            ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"\n可视化图表已保存: {save_path}")

def main():
    """主函数"""
    print("开始简化精度分析...")
    
    # 加载数据
    df = load_data(DEFAULT_PATHS)
    print(f"数据加载完成，样本数: {len(df)}")
    
    # 数据概览
    print(f"\n数据概览:")
    print(df[['gaps_rate', 'slope', 'count_clean', 'acc_rec', 'f1_rec']].describe())
    
    # 1. 偏相关分析
    partial_results = compute_partial_correlations(df)
    
    # 2. 回归分析
    regression_results = regression_analysis(df)
    
    # 3. 创建可视化
    create_visualization(df)
    
    return df, partial_results, regression_results

if __name__ == "__main__":
    df, partial_results, regression_results = main()
