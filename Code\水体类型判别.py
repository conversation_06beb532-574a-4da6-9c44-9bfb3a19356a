import numpy as np

# 像素索引列表
pixel_indices = [(100, 100), (100, 200), (200, 200), (200, 100)]

# 将像素索引转换为坐标
x, y = zip(*pixel_indices)
coords = np.column_stack((x, y))

# 计算对象的几何中心
centroid = np.mean(coords, axis=0)
print('几何中心:', centroid)

# 计算对象的最小外接矩形
rect = cv2.minAreaRect(coords)
rect_center, rect_size, rect_angle = rect
print('最小外接矩形：', rect_center, rect_size, rect_angle)

# 计算对象的最小外接圆
(x,y),radius = cv2.minEnclosingCircle(coords)
center = (int(x),int(y))
radius = int(radius)
print('最小外接圆：', center, radius)

# 计算对象的面积
area = cv2.contourArea(coords)
print('面积：', area)

# 计算对象的周长
perimeter = cv2.arcLength(coords, True)
print('周长：', perimeter)

# 计算对象的紧凑度
compactness = perimeter**2 / (4*np.pi*area)
print('紧凑度：', compactness)

# 计算对象的凸度
hull = cv2.convexHull(coords)
hull_area = cv2.contourArea(hull)
convexity = area / hull_area
print('凸度：', convexity)
