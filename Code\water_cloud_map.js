////////////////////Cloud Masking
// Create Landsat 45789 Collection Function.
function createLandsatCollection(params) {
    var defaultParams = {
        region: Map.getBounds(true),
        start: '1982-01-01',
        end: formatDate(new Date()),
        mapImage: function (image) { return image }
    }
    params = mergeObjects([defaultParams, params])

    var filter = ee.Filter.and(
        ee.Filter.bounds(params.region),
        ee.Filter.date(params.start, params.end)
    )
    function cloudMask(image) {
        // Bits 3 and 5 are cloud shadow and cloud, respectively.
        var dilatedBitMask = (1 << 1)
        var cirrusBitMask = (1 << 2)
        var cloudShadowBitMask = (1 << 3)
        var cloudsBitMask = (1 << 4)
        var snowBitMask = (1 << 5)
        var cleanBitMask = (1 << 6)
        // Get the pixel QA band.
        var qa = image.select('QA_PIXEL');
        // Both flags should be set to zero, indicating clear conditions.
        var mask = qa.bitwiseAnd(cloudShadowBitMask).eq(0)
            .and(qa.bitwiseAnd(cloudsBitMask).eq(0))
        // .and(qa.bitwiseAnd(snowBitMask).eq(0))
        // .and(qa.bitwiseAnd(cirrusBitMask).eq(0))
        // .and(qa.bitwiseAnd(dilatedBitMask).eq(0))
        // .and(qa.bitwiseAnd(cleanBitMask).eq(0))

        var mask2 = image.mask().reduce(ee.Reducer.min());

        return image.updateMask(mask).updateMask(mask2).copyProperties(image);
    }

    // function cloudMask(image){
    //   var im = ee.Algorithms.Landsat.simpleCloudScore(image)
    //   var mask = im.select('cloud').lt(cloudThresh)
    //   var mask2 = image.mask().reduce(ee.Reducer.min());

    //   return im.updateMask(mask).updateMask(mask2).copyProperties(image)
    // }

    var l4 = ee.ImageCollection('LANDSAT/LT04/C02/T1_TOA')
        .filter(filter)
        .map(cloudMask)
        .select(
            ['B1', 'B2', 'B3', 'B4', 'B5'],
            ['blue', 'green', 'red', 'nir', 'swir1']
        )

    var l5 = ee.ImageCollection('LANDSAT/LT05/C02/T1_TOA')
        .filter(filter)
        .map(cloudMask)
        .select(
            ['B1', 'B2', 'B3', 'B4', 'B5'],
            ['blue', 'green', 'red', 'nir', 'swir1']
        )

    var l7 = ee.ImageCollection('LANDSAT/LE07/C02/T1_TOA')
        .filter(filter)
        .map(cloudMask)
        .select(
            ['B1', 'B2', 'B3', 'B4', 'B5'],
            ['blue', 'green', 'red', 'nir', 'swir1']
        )

    var l8 = ee.ImageCollection('LANDSAT/LC08/C02/T1_TOA')
        .filter(filter)
        .map(cloudMask)
        .select(
            ['B2', 'B3', 'B4', 'B5', 'B6'],
            ['blue', 'green', 'red', 'nir', 'swir1']
        )

    var l9 = ee.ImageCollection('LANDSAT/LC09/C02/T1_TOA')
        .filter(filter)
        .map(cloudMask)
        .select(
            ['B2', 'B3', 'B4', 'B5', 'B6'],
            ['blue', 'green', 'red', 'nir', 'swir1']
        )

    return l4.merge(l5).merge(l7).merge(l8).merge(l9)
        // .map(Rmshadow)
        .map(mapImage)
        .sort('system:time_start')

    function mapImage(image) {
        return params.mapImage(image)
            .clip(params.region)
    }

    function formatDate(date) {
        var d = new Date(date),
            month = '' + (d.getMonth() + 1),
            day = '' + d.getDate(),
            year = d.getFullYear()

        if (month.length < 2)
            month = '0' + month
        if (day.length < 2)
            day = '0' + day

        return [year, month, day].join('-')
    }

    function mergeObjects(objects) {
        return objects.reduce(function (acc, o) {
            for (var a in o) { acc[a] = o[a] }
            return acc
        }, {})
    }
}

// Index Function.
// NDSI
function toNDSI(image) {
    var bands = image.select('green', 'swir1')
    return bands.expression(
        '(i.green - i.swir1) / (i.green + i.swir1)',
        { i: bands }
    ).multiply(10000).rename('NDSI')
}
// NDVI
function toNDVI(image) {
    var bands = image.select('nir', 'red')
    return bands.expression(
        '(i.nir - i.red) / (i.nir + i.red)',
        { i: bands }
    ).multiply(10000).rename('NDVI')
}
// EVI
function toEVI(image) {
    var bands = image.select('red', 'nir', 'blue')
    return bands.expression(
        '2.5 * (i.nir - i.red) / (i.nir + 6 * i.red - 7.5 * i.blue + 1)',
        { i: bands }
    ).multiply(10000).int16().rename('EVI')
}
// MNDWI
function toNDWI(image) {
    var bands = image.select('green', 'swir1')
    return bands.expression(
        '(i.green - i.swir1) / (i.green + i.swir1)',
        { i: bands }
    ).multiply(10000).int16().rename('NDWI')
}
// NDWI
function toMNDWI(image) {
    var bands = image.select('green', 'nir')
    return bands.expression(
        '(i.green - i.nir) / (i.green + i.nir)',
        { i: bands }
    ).multiply(10000).int16().rename('MNDWI')
}
// LSWI
function toLSWI(image) {
    var bands = image.select('nir', 'swir1')
    return bands.expression(
        '(i.nir - i.swir1) / (i.nir + i.swir1)',
        { i: bands }
    ).multiply(10000).int16().rename('LSWI')
}
// AWEIsh
function toAWEIsh(image) {
    var bands = image.select('blue', 'green', 'nir', 'swir1', 'swir2')
    return bands.expression(
        'i.blue + 2.5*i.green - 1.5*(i.nir+i.swir1) - 0.25*i.swir2',
        { i: bands }
    ).multiply(10000).int16().rename('AWEIsh')
}
// BSI
var toBSI = function (image) {
    var bsi = image.expression(
        '((RED + SWIR) - (NIR + BLUE)) / ((RED + SWIR) + (NIR + BLUE)) ',
        {
            'RED': image.select('red'),
            'BLUE': image.select('blue'),
            'NIR': image.select('nir'),
            'SWIR': image.select('swir1'),
        })
        .rename('BSI')
        .multiply(10000)
        .int16()
    return bsi
};
var toNDBI = function (image) {
    var ndbi = image.expression(
        '(SWIR1-NIR) / (SWIR1+NIR)',
        {
            'SWIR1': image.select('swir1'),
            'NIR': image.select('nir')
        })
        .rename('NDBI')
        .multiply(10000)
        .int16()
    return ndbi
}
// All index add
var addIndex = function (img) {
    return img.addBands(toEVI(img))
        .addBands(toNDVI(img))
        .addBands(toMNDWI(img))
        .addBands(toBSI(img))
        .addBands(toLSWI(img))
        // .addBands(toNDBI(img))
        .addBands(toNDSI(img))
        .addBands(toNDWI(img))
}

var calWater = function (img) {
    var MNDWI = img.select('MNDWI')
    var NDVI = img.select('NDVI')
    var BSI = img.select('BSI')
    var EVI = img.select('EVI')
    var LSWI = img.select('LSWI')
    var NDBI = img.select('NDBI')
    var NDSI = img.select('NDSI')
    // var AWEIsh = img.select('AWEIsh') 
    // var water = ((MNDWI.gt(EVI)).or(MNDWI.gt(NDVI)).or(LSWI.gt(NDVI))).rename('water')
    var water = ((MNDWI.gt(EVI)).or(MNDWI.gt(NDVI)).and(EVI.lt(1000))).rename('water')

    return water.unmask(-1)
}
// var thresholding = require('users/gena/packages:thresholding')
// var calWater = function(img) {
//   var ndwi = img.select('MNDWI')
//   var hist = ndwi.reduceRegion({
//     reducer: ee.Reducer.histogram(256), 
//     geometry: poi, 
//     scale: 30, 
//     maxPixels: 1e13
//   }).values().get(0)

//   var th = thresholding.otsu(hist)

//   var water = ndwi.gt(th).rename('water')

//   return water.unmask(-1)
// }
//-----------------------------------------------------
// var poi = geometry
var poi = geometry
var mths = ee.List.sequence(1, 12);
var yrs = ee.List.sequence(2011, 2022)
var countScale = 30; //pixel size for counting (meter). Make sure to adjust the 
var cloudThresh = 30

///Composite into Monthly images
var lsCol = createLandsatCollection({
    region: poi,
    start: '1986-04-01',
    end: '2023-01-01',
    mapImage: function (image) {
        return image
    }
})

print(lsCol.limit(10))

lsCol = ee.ImageCollection.fromImages(yrs.map(function (y) {
    var out = mths.map(function (mth) {
        var Lf = lsCol.filter(ee.Filter.calendarRange({ start: y, field: 'year' })).filter(ee.Filter.calendarRange({ start: mth, field: 'month' }))

        Lf = Lf.median()

        Lf = Lf.set({ 'system:time_start': ee.Date.fromYMD(y, mth, 1).millis() })
            .set('date', ee.Number(y).multiply(100).add(ee.Number(mth)).int())

        return (Lf)
    })

    return (out)
}).flatten())

lsCol = lsCol.map(function (im) { return (im.set({ 'empty': ee.Algorithms.If(im.bandTypes(), 0, 1) })) }).filterMetadata('empty', 'equals', 0)
Map.addLayer(lsCol.filterDate('1986-10-01').first(), {}, 'ls', false)

var waterCol = lsCol.map(function (img) {
    var index = addIndex(img)
    var water = calWater(index)
    water = water.clip(poi)
    return water.copyProperties(img, ['system:time_start', 'date'])
})

Map.addLayer(waterCol.filterDate('1986-11-01').first(), { palette: ['grey', 'green', 'blue'] }, 'my method')

// Export.
var size = lsCol.size().getInfo()
var listOfImage = waterCol.toList(size)

for (var i = 0; i < size; i++) {
    var img = ee.Image(listOfImage.get(i));
    var id = img.get('date').getInfo().toString();
    var assetId = 'ChinaWater/waterCol_cloud_poyang_r6'

    Export.image.toAsset({
        image: img,
        description: id,
        assetId: assetId + '/' + id,
        region: poi,
        scale: 30,
        maxPixels: 1e13
    })
}