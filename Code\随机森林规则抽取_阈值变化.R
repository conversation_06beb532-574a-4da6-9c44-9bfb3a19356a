library(randomForest)
library(caret)
library(GA)
library(rattle)
library(inTrees)
library(pROC)
library(dplyr)


inws1 = 'C:\\Users\\<USER>\\Desktop\\Water\\Validation\\data.csv'
df = read.csv(inws1)

data = subset(df, select=-c(1,3,5,6,17,24,29,30,33))

data$refer <- ifelse(data$refer==2, "water", 
                     ifelse(data$refer==1, "other", "null"))
data = data[data$refer != 'null', ]

remove_outliers <- function(column) {
  threshold <- 2 * sd(column)
  column[abs(column - mean(column)) >= threshold] <- NA
  return(column)
}


data[,-c(22,24)] <- apply(data[,-c(22,24)], 2, remove_outliers)
data <- data[complete.cases(data), ]

set.seed(123)

train_index <- sample(nrow(data), round(0.9 * nrow(data)))

data_train <- data[train_index, ]
data_test <- data[-train_index, ]

library(RRF)
X_train <- data_train[,-c(22,24)]
X_test <- data_test[,-c(22,24)]

target_train <- data_train[,"refer"]
target_test <- data_test[,"refer"]

data_test$refer <- ifelse(data_test$refer=="water", TRUE, 
                          ifelse(data_test$refer=="shadow" | data_test$refer=='snow' | data_test$refer=='other', FALSE, "null"))
target_test <- data_test$refer

# 3. 定义适应度函数
cal <- function(x) {
  predict <- variable>x-0.01 & variable<x+0.01
  # 分类为水的准确率
  accuracy <- sum(predict==TRUE & target_test==TRUE) / sum(target_test==TRUE)
  return(accuracy)
}
acc_func <- function(index) {
  X <- X_test
  max <- max(X[,index])
  min <- min(X[,index])
  evaluation <- data.frame(seq(min, max, length.out = 1000))
  evaluation$acc <- apply(evaluation, 1, cal)
  
  return(evaluation)
}
variable <- X_test[22]

eval <- acc_func(22)
names(eval) <- c('value', 'acc')

plot(eval)

model <- function(x, a, b, c) {
  a * exp(b * x) + c
}

fit <- nls(eval$acc ~ model(eval$value, a, b, c), start = list(a = 1, b = 0.1, c=1))
summary(fit)

lines(eval$value, predict(fit), col='red')




