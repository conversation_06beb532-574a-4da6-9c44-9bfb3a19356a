import arcpy
import glob
import os
from itertools import izip

arcpy.CheckOutExtension("Spatial")

inws = r"C:\Users\<USER>\Desktop\ChinaWater"
outws = r"C:\Users\<USER>\Desktop\ChinaWater"
base = r"C:\Users\<USER>\Desktop\ChinaWater\1991\1991-0000000000-0000000000.tif"
out_coor_system = arcpy.Describe(base).spatialReference
dataType = arcpy.Describe(base).DataType
piexl_type = arcpy.Describe(base).pixelType
cellWidth = arcpy.Describe(base).meanCellWidth
bandCount = arcpy.Describe(base).bandCount

for year in range(1990,1991):
    path = inws+'/'+str(year)
    
    rasters = glob.glob(os.path.join(path, "*.tif"))
    if len(rasters) != 0:
        name = os.path.basename(rasters[0])[:4]+'_2'
        print(name)
        arcpy.MosaicToNewRaster_management(rasters, outws, name+'.tif', out_coor_system,
                                           '16_BIT_SIGNED', cellWidth, bandCount, "LAST", "FIRST")
