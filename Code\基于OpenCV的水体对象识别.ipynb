{"cells": [{"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mT<PERSON> crashed while executing code in the the current cell or a previous cell. Please review the code in the cell(s) to identify a possible cause of the failure. Click <a href='https://aka.ms/vscodeJupyterKernelCrash'>here</a> for more info. View Jupyter <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["import cv2\n", "import numpy as np\n", "\n", "# 读取图像\n", "inws = r'C:\\Users\\<USER>\\Desktop\\Water\\2021_new_test.tif'\n", "img = cv2.imread(inws)\n", "# cv2.imshow('Raster Image', img)\n", "# cv2.<PERSON><PERSON><PERSON>(0)\n", "# cv2.destroyAllWindows()\n", "\n", "# # 将图像转换为灰度图像\n", "gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)\n", "\n", "# # 根据阈值进行二值化处理\n", "ret, binary = cv2.threshold(gray, 0, 10000, cv2.THRESH_BINARY + cv2.THRESH_OTSU)\n", "\n", "# # 显示二值化结果\n", "cv2.imshow('binary', binary)\n", "# cv2.<PERSON><PERSON><PERSON>(0)\n", "# cv2.destroyAllWindows()\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["39.0"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["ret"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}