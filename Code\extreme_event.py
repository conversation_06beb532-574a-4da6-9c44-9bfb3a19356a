# -*- coding: utf-8 -*-
import gdal, ogr, osr, os
import numpy as np
import statsmodels.api as sm
from multiprocessing import Pool
import time
# import gdal_rast_funcs as grast
from scipy.interpolate import interp1d
import glob


def extremeEvent_interpt(event_ser):
    # Initialize a NumPy array representing days after 1-Sep
    day_ser = np.arange(1, 365)
    x_filt = np.extract(event_ser != 0, day_ser)
    num_event = 0
    max_dur = 0
    try:
        indices = np.where(np.diff(x_filt) >= 3)[0] + 1

        arr_event = np.split(x_filt, indices)

        for sub_arr in arr_event:

            dur_sub = sub_arr.max()-sub_arr.min() + 1
            con = (dur_sub > 3 and np.any(np.diff(sub_arr) >= 1)) or \
                  (dur_sub == 3 and np.all(np.diff(sub_arr) == 1))

            if con:
                num_event += 1

                if max_dur <= dur_sub:
                    max_dur = dur_sub

        return [max_dur, num_event]

    except:

        return [-9, -9]

def array2gtiff(newRasterfn, LowrLft_Origin, pixelWidth, pixelHeight, array, EPSG):
    # Update users
    print("Starting: Write " + newRasterfn + " to GeoTiff")

    # Get number of bands
    bands = array.shape[0]

    # Get number of columns and rows
    cols = array.shape[2]
    rows = array.shape[1]

    # Get raster origin
    originX = LowrLft_Origin[0]
    originY = LowrLft_Origin[1]

    # Get a GeoTiff driver
    driver = gdal.GetDriverByName('GTiff')

    # Initialize a output raster with GeoTransform parameters from input tile and array shape
    outRaster = driver.Create(newRasterfn, cols, rows, bands, gdal.GDT_Int16)

    outRaster.SetGeoTransform((originX, pixelWidth, 0, originY, 0, pixelHeight))

    outRasterSRS = osr.SpatialReference()

    outRasterSRS.ImportFromEPSG(EPSG)

    outRaster.SetProjection(outRasterSRS.ExportToWkt())

    # Write each band to the output raster, starting with band 1
    for b in range(1, bands + 1):
        # Get the output band @ b
        outband = outRaster.GetRasterBand(b)

        # Slice the 3D array at b-1 (2D)
        ary = array[b - 1]

        # Write the 2D NumPy array to the band
        outband.WriteArray(ary)

        # Flush
        outband.FlushCache()

    # Update user
    print("Finished: Write " + newRasterfn + " to GeoTiff")


def ConvertArray(dir_pth, rows, cols, year):
    tiles = glob.glob(os.path.join(dir_pth, "event_" + str(year) + "*.tif"))
    stacks_num = len(tiles)

    arr_stack = np.full((rows * cols * stacks_num), 0, 'int16').reshape(stacks_num, rows, cols)
    # Initialize output NumPy array with input dimensions and 4 bands
    for t in tiles[:stacks_num]:
        tile_pth = t
        # Update user
        #         print("Starting: Converting "+tile_pth+" @ "+str(time.time()))
        data = gdal.Open(tile_pth)

        #         print("Converting"+t+"to Numpy")
        # Commit the GeoTiff to memory as 3D NumPy array
        num = tiles.index(t)
        arr_stack[num] = data.ReadAsArray()
    #         print("Finished: Converting"+t+"to Numpy")
    return arr_stack


def Process(arr_stack):
    # Initialize output NumPy array with input dimensions and 4 bands
    extremeEvent_stack = np.full((rows * cols * 5), 0).reshape(5, rows, cols)
    # Process keeper
    qrt = rows * 0.25
    thsh = qrt

    # For each row
    for row in range(rows):
        # Update user on progress
        if row >= thsh:
            print("Processing " + str(round((float(row) / float(rows)) * 100)) + "%")
            thsh = thsh + qrt
        # For each col
        for col in range(cols):

            # Get the ndvi time series from the 'time_cube' @ row and col
            extremeEvent_vect = arr_stack[0:, row, col]
            # Get grow duration metrics
            metrics = extremeEvent_interpt(extremeEvent_vect)

            # grow arr_event Band
            extremeEvent_stack[0, row, col] = metrics[0]

            # grow Start Band
            extremeEvent_stack[1, row, col] = metrics[1]

            # grow End Band
            extremeEvent_stack[2, row, col] = metrics[2]

            # grow Dur Band
            extremeEvent_stack[3, row, col] = metrics[3]

            # grow num_event Band
            extremeEvent_stack[4, row, col] = metrics[4]

    print("Finished: Process")
    return extremeEvent_stack


if __name__ == '__main__':
    # Environment Setting.
    # os.environ['PROJ_LIB'] = r'C:\Users\<USER>\anaconda3\Library\share\proj'
    # os.environ['GDAL_DATA'] = r'C:\Users\<USER>\anaconda3\Library\share'

    # Image Setting
    tileRefer = gdal.Open(r'F:\0426ndvi\NDVI_2000_02_18.tif')
    arr = tileRefer.ReadAsArray()
    rows = arr.shape[0]
    cols = arr.shape[1]

    # Export Setting
    GeoTran = tileRefer.GetGeoTransform()
    # Get origin of raster
    RastOrigin = (GeoTran[0], GeoTran[3])
    # Get pixel dimensions
    PixWidth = GeoTran[1]
    PixHeight = GeoTran[5]
    EPSG = 4326

    dir_pth = r"F:\0426ndvi"

    for year in np.arange(1981, 2023):
        print('Begin: ' + str(year) + ' @ ' + str(time.time()))
        newRasterfn = r"H:\project\photovoltaic\data\event\result" + str(year) + ".tif"
        # Work Flow
        arr_stack = ConvertArray(dir_pth, rows, cols, year)
        grow_stack = Process(arr_stack)
        array2gtiff(newRasterfn, RastOrigin, PixWidth, PixHeight, grow_stack, EPSG)
        print('End: ' + str(year) + ' @ ' + str(time.time()))
    # Update user
    print("Finished: Work " + dir_pth + " @ " + str(time.time()))