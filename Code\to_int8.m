%影像数据参数
[aa,R] = geotiffread('D:\xiaozhen\water_change\1990.tif');
info=geotiffinfo('D:\xiaozhen\water_change\1990.tif');
[m,n]=size(aa);
clear aa
%影像数量
years = 7;
for year=1990:1996
    water = importdata(['D:\xiaozhen\water_change\', int2str(year), '.tif']);
    water = int8(water/100);
    filename = ['D:\xiaozhen\', int2str(year) '.tif'];
    geotiffwrite(filename, water, R, 'GeoKeyDirectoryTag', info.GeoTIFFTags.GeoKeyDirectoryTag);
end