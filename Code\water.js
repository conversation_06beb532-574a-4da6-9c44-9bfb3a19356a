//---------------------Module-----------------
function excludeBand(bandName, image) {
    var bandNames = image.bandNames()
    var bandIndexes = ee.List.sequence(0, bandNames.size().subtract(1))
        .filter(
            ee.Filter.neq('item', bandNames.indexOf(bandName))
        )
    return image.select(bandIndexes)
}
var GapFill = function (img) {
    var img_fill = img.focal_mean(1, 'circle', 'pixels', 2)
    return img_fill.blend(img).set('system:time_start', img.get('system:time_start'))
}

// Establishing Identification Rules.
var calWaterThis = function (img) {
    var MNDWI = img.select('MNDWI')
    var NDVI = img.select('NDVI')
    var BSI = img.select('BSI')
    var EVI = img.select('EVI')
    var LSWI = img.select('LSWI')
    var NDBI = img.select('NDBI')
    var NDSI = img.select('NDSI')
    // var AWEIsh = img.select('AWEIsh')
    var water = ((MNDWI.gt(EVI)).or(MNDWI.gt(BSI)).or(LSWI.gt(NDVI))).and(EVI.lt(1000)).rename('water')
    return water
}
var calWaterThis_without_threshold = function (img) {
    var MNDWI = img.select('MNDWI')
    var NDVI = img.select('NDVI')
    var BSI = img.select('BSI')
    var EVI = img.select('EVI')
    var LSWI = img.select('LSWI')
    var NDBI = img.select('NDBI')
    var NDSI = img.select('NDSI')
    // var AWEIsh = img.select('AWEIsh')
    var water = ((MNDWI.gt(EVI)).or(MNDWI.gt(NDVI)).or(LSWI.gt(NDVI))).rename('water')
    return water
}

var calWater2 = function (img) {
    var green_nir_divide = img.select('green_nir_divide')
    var nir = img.select('nir')
    var red_swir1_divide = img.select('red_swir1_divide')
    var water = (green_nir_divide.gt(0.935)).or(nir.lt(0.1)).and(red_swir1_divide.gt(1.254)).rename('water')
    return water
}
var calWater = function (img) {
    var MNDWI = img.select('MNDWI')
    var NDVI = img.select('NDVI')
    var EVI = img.select('EVI')
    var AWEIsh = img.select('AWEIsh')
    var water = ((MNDWI.gt(NDVI)).or(MNDWI.gt(EVI))).and(EVI.lt(1000)).rename('water')
    return water
}
var calShadow = function (img) {
    var AWEIsh = img.select('AWEIsh')
    var shadow = AWEIsh.lt(0)
    return shadow
}
var calSnow = function (img) {
    var NDSI = img.select('NDSI')
    var green = img.select('green')
    var nir = img.select('nir')
    var snow = NDSI.gte(4000).and(green.gte(1000)).and(nir.gte(1100)).rename('snow')
    return snow
}

// Frequency Function.
var rate = function (imgCol) {
    var count = imgCol.count()
    var sum = imgCol.sum()
    return sum.divide(count).multiply(10000).int16()
};
function combinePairwise(image, algorithm, suffix) {
    suffix = suffix || ''
    return ee.Image(image.bandNames().iterate(function (b1, accImage) {
        b1 = ee.String(b1)
        accImage = ee.Image(accImage)
        var img1 = image.select(b1).rename('img1')
        var i1 = image.bandNames().indexOf(b1)
        var combinations = ee.Image(image.bandNames().slice(i1.add(1)).iterate(function (b2, accImage) {
            b2 = ee.String(b2)
            accImage = ee.Image(accImage)
            var img2 = image.select(b2).rename('img2')
            return accImage.addBands(
                algorithm(img1, img2)
                    .rename(b1.cat('_').cat(b2).cat(suffix || ''))
            )
        }, ee.Image([])))
        return accImage.addBands(combinations)
    }, ee.Image([])))
}

// Index Function.
// NDSI
function toNDSI(image) {
    var bands = image.select('green', 'swir1')
    return bands.expression(
        '(i.green - i.swir1) / (i.green + i.swir1)',
        { i: bands }
    ).multiply(10000).rename('NDSI')
}
// NDVI
function toNDVI(image) {
    var bands = image.select('nir', 'red')
    return bands.expression(
        '(i.nir - i.red) / (i.nir + i.red)',
        { i: bands }
    ).multiply(10000).rename('NDVI')
}
// EVI
function toEVI(image) {
    var bands = image.select('red', 'nir', 'blue')
    return bands.expression(
        '2.5 * (i.nir - i.red) / (i.nir + 6 * i.red - 7.5 * i.blue + 1)',
        { i: bands }
    ).multiply(10000).int16().rename('EVI')
}
// MNDWI
function toMNDWI(image) {
    var bands = image.select('green', 'swir1')
    return bands.expression(
        '(i.green - i.swir1) / (i.green + i.swir1)',
        { i: bands }
    ).multiply(10000).int16().rename('MNDWI')
}
// LSWI
function toLSWI(image) {
    var bands = image.select('nir', 'swir1')
    return bands.expression(
        '(i.nir - i.swir1) / (i.nir + i.swir1)',
        { i: bands }
    ).multiply(10000).int16().rename('LSWI')
}
// AWEIsh
function toAWEIsh(image) {
    var bands = image.select('blue', 'green', 'nir', 'swir1', 'swir2')
    return bands.expression(
        'i.blue + 2.5*i.green - 1.5*(i.nir+i.swir1) - 0.25*i.swir2',
        { i: bands }
    ).multiply(10000).int16().rename('AWEIsh')
}
// BSI
var toBSI = function (image) {
    var bsi = image.expression(
        '((RED + SWIR) - (NIR + BLUE)) / ((RED + SWIR) + (NIR + BLUE)) ',
        {
            'RED': image.select('red'),
            'BLUE': image.select('blue'),
            'NIR': image.select('nir'),
            'SWIR': image.select('swir1'),
        })
        .rename('BSI')
        .multiply(10000)
        .int16()
    return bsi
};
var toNDBI = function (image) {
    var ndbi = image.expression(
        '(SWIR1-NIR) / (SWIR1+NIR)',
        {
            'SWIR1': image.select('swir1'),
            'NIR': image.select('nir')
        })
        .rename('NDBI')
        .multiply(10000)
        .int16()
    return ndbi
}
// All index add
var addIndex = function (img) {
    return img.addBands(toEVI(img))
        .addBands(toNDVI(img))
        .addBands(toMNDWI(img))
        .addBands(toBSI(img))
        .addBands(toLSWI(img))
        // .addBands(toNDBI(img))
        .addBands(toNDSI(img))
}

var elevation = ee.Image("NASA/NASADEM_HGT/001").select('elevation');
var Rmshadow = function (img) {
    var SUN_AZIMUTH = ee.Number(img.get('SUN_AZIMUTH'))
    var SUN_ELEVATION = ee.Number(img.get('SUN_ELEVATION'))
    // Output 1 where pixels are illumunated and 0 where they are shadowed.
    var shadow = ee.Terrain.hillshade(elevation, SUN_AZIMUTH, SUN_ELEVATION).divide(255)
    //山体阴影属于无效观测，光线照不到，所以不计入总数
    return img.updateMask(shadow.gt(0.8))
}
function applyScaleFactorsL457(image) {
    var opticalBands = image.select('SR_B.').multiply(0.0000275).add(-0.2);
    var thermalBand = image.select('ST_B6').multiply(0.00341802).add(149.0);
    return image.addBands(opticalBands, null, true)
        .addBands(thermalBand, null, true)
}
function applyScaleFactorsL89(image) {
    var opticalBands = image.select('SR_B.').multiply(0.0000275).add(-0.2);
    var thermalBands = image.select('ST_B.*').multiply(0.00341802).add(149.0);

    var qa = image.select('QA_PIXEL');
    return image.addBands(opticalBands, null, true)
        .addBands(thermalBands, null, true)
        .addBands(qa)
}

function cloudMask(image) {
    // Bits 3 and 5 are cloud shadow and cloud, respectively.
    var cloudShadowBitMask = (1 << 3);
    var cloudsBitMask = (1 << 4);
    // Get the pixel QA band.
    var qa = image.select('QA_PIXEL');
    // Both flags should be set to zero, indicating clear conditions.
    var mask = qa.bitwiseAnd(cloudShadowBitMask).eq(0)
        .and(qa.bitwiseAnd(cloudsBitMask).eq(0))

    return image.updateMask(mask)
}

function snowMask(image) {
    var snowBitMask = (1 << 5)
    var qa = image.select('QA_PIXEL');
    var mask = qa.bitwiseAnd(snowBitMask).eq(0)
    return image.updateMask(mask)//.unmask(0)
}
// Create Landsat 45789 Collection Function.
function createLandsatCollection(params) {
    var defaultParams = {
        region: Map.getBounds(true),
        start: '1982-01-01',
        end: formatDate(new Date()),
        mapImage: function (image) { return image }
    }
    params = mergeObjects([defaultParams, params])

    var filter = ee.Filter.and(
        ee.Filter.bounds(params.region),
        ee.Filter.date(params.start, params.end)
    )

    var l4 = ee.ImageCollection('LANDSAT/LT04/C02/T1_L2')
        .filter(filter)
        .map(applyScaleFactorsL457)
        .select(
            ['SR_B1', 'SR_B2', 'SR_B3', 'SR_B4', 'SR_B5', 'QA_PIXEL'],
            ['blue', 'green', 'red', 'nir', 'swir1', 'QA_PIXEL']
        )
    // print(l4.limit(10))
    // print('l4', l4.size())
    var l5 = ee.ImageCollection('LANDSAT/LT05/C02/T1_L2')
        .filter(filter)
        .map(applyScaleFactorsL457)
        .select(
            ['SR_B1', 'SR_B2', 'SR_B3', 'SR_B4', 'SR_B5', 'QA_PIXEL'],
            ['blue', 'green', 'red', 'nir', 'swir1', 'QA_PIXEL']
        )
    // print(l5.limit(10))
    // print('l5', l5.size())
    // GapFill
    var l7 = ee.ImageCollection('LANDSAT/LE07/C02/T1_L2')
        .filter(filter)
        .map(applyScaleFactorsL457)
        // .map(GapFill)
        .select(
            ['SR_B1', 'SR_B2', 'SR_B3', 'SR_B4', 'SR_B5', 'QA_PIXEL'],
            ['blue', 'green', 'red', 'nir', 'swir1', 'QA_PIXEL']
        )
    // print(l7.limit(10))
    // print('l7', l7.size())
    var l8 = ee.ImageCollection('LANDSAT/LC08/C02/T1_L2')
        .filter(filter)
        .map(applyScaleFactorsL89)
        .select(
            ['SR_B2', 'SR_B3', 'SR_B4', 'SR_B5', 'SR_B6', 'QA_PIXEL'],
            ['blue', 'green', 'red', 'nir', 'swir1', 'QA_PIXEL']
        )
    // print('l8', l8.size())

    var l9 = ee.ImageCollection('LANDSAT/LC09/C02/T1_L2')
        .filter(filter)
        .map(applyScaleFactorsL89)
        .select(
            ['SR_B2', 'SR_B3', 'SR_B4', 'SR_B5', 'SR_B6', 'QA_PIXEL'],
            ['blue', 'green', 'red', 'nir', 'swir1', 'QA_PIXEL']
        )

    return l4.merge(l5).merge(l7).merge(l8).merge(l9)
        .map(mapImage)

    function mapImage(image) {
        return params.mapImage(image)
        //.clip(params.region)
    }

    function formatDate(date) {
        var d = new Date(date),
            month = '' + (d.getMonth() + 1),
            day = '' + d.getDate(),
            year = d.getFullYear()

        if (month.length < 2)
            month = '0' + month
        if (day.length < 2)
            day = '0' + day

        return [year, month, day].join('-')
    }

    function mergeObjects(objects) {
        return objects.reduce(function (acc, o) {
            for (var a in o) { acc[a] = o[a] }
            return acc
        }, {})
    }
}

function maskS2clouds(image) {
    var qa = image.select('QA60');

    // Bits 10 and 11 are clouds and cirrus, respectively.
    var cloudBitMask = 1 << 10;
    var cirrusBitMask = 1 << 11;

    // Both flags should be set to zero, indicating clear conditions.
    var mask = qa.bitwiseAnd(cloudBitMask).eq(0)
        .and(qa.bitwiseAnd(cirrusBitMask).eq(0));

    return image.updateMask(mask)//.copyProperties(image);
}

//-------------Workflow--------------
// 1. Parameter Setting.


// var region = region

var region = geometry

var year = 2021

// 2. Create Collection.
// for (var year = 2021; year <= 2021; year++) {
//     for (var i = 0; i <= 5; i++) {
//     var region = region.filterMetadata('Id', 'equals', i); // 分区

var ls_merged4578 = createLandsatCollection({
    region: region,
    start: year + '-01-01',
    end: year + 1 + '-01-01',
    mapImage: function (image) {
        var img_rm_shadow = Rmshadow(image)
        var img_rm_snow = snowMask(img_rm_shadow)
        var img_rm_cloud = cloudMask(img_rm_snow)

        // Slope mask.
        // 填补掩膜值，以便统计频率时记入总数
        return img_rm_cloud
    }
});
function divide(img1, img2) {
    return img1.expression('img1 / img2', {
        img1: img1,
        img2: img2
    })
}
var origin = ['blue', 'green', 'red', 'nir', 'swir1']
var imgCol = ls_merged4578.select(origin).map(function (image) {
    var indxes = addIndex(image)
    var diffImg = combinePairwise(image, divide, '_divide')
    return indxes.addBands(diffImg)
})
// print(ls_merged4578.first())
// Map.addLayer(ls_merged4578.first())
//---Sentinel---
// var dataset = ee.ImageCollection('COPERNICUS/S2_SR_HARMONIZED')
//     .filterBounds(region)
//     .filterDate(year + '-01-01', year + 1 + '-01-01')
//     .map(maskS2clouds)
//     .select(['B2', 'B3', 'B4', 'B8', 'B11'],
//         ['blue', 'green', 'red', 'nir', 'swir1'])
//     .map(addIndex)

// print(dataset)
// Map.addLayer(dataset)
// print(ls_merged4578.size())

// 3. Generation year-by-year results.
var imgCol = ls_merged4578.filter(ee.Filter.calendarRange(year, year, 'year'))
    .map(function (img) {
        var water = calWater2(img).rename('water')
        return water
    })
// print(imgCol)
// Map.addLayer(imgCol, {}, 'imgCol')

var countImg = imgCol.count()
// Map.addLayer(countImg, {}, 'countImg')

// rate
var rateImg = rate(imgCol).set('year', year)

// 4. Select test data and visualize.
var water_rate = rateImg.select('water')//.unmask(0).clip(region)
Map.addLayer(water_rate, { palette: ['green', 'red'], min: 0, max: 10000 })

    // 5. Statistic.
    // var img = ee.Image.constant(1)
    // var vectors = waterMask.addBands(img).reduceToVectors({
    //     geometry: region,
    //     scale: 30,
    //     // labelProperty: 'area',
    //     reducer: ee.Reducer.count(),
    //     eightConnected: true,
    //     geometryType: 'polygon',
    //     maxPixels: 1e13,
    //     // tileScale: 16
    // })
    // var areaVectors = vectors.map(function(vector){
    // var num = ee.Number.parse(vector.get('count')).multiply(30).multiply(30)
    // return vector.set('area', num)
    // })
    // var lake = vectors.filter(ee.Filter.gt('count', 1000000/30/30))
    // print(lake.size())

    // 6. Export image.
    // Export.image.toDrive({
    //     image: water_rate,
    //     description: '' + year,
    //     scale: 30,
    //     maxPixels: 1e13,
    //     region: region.geometry().bounds()
    // })
    // }
// }