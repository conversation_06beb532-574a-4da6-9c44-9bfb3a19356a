# 1. 阈值分割
import cv2

# 读取概率矩阵
prob_matrix = cv2.imread('prob_matrix.png', cv2.IMREAD_GRAYSCALE)

# 阈值分割
threshold = 128
binary_image = cv2.threshold(prob_matrix, threshold, 255, cv2.THRESH_BINARY)[1]
# 2. 连通区域分析
import cv2

# 读取二值图像
binary_image = cv2.imread('binary_image.png', cv2.IMREAD_GRAYSCALE)

# 连通区域分析
output = cv2.connectedComponentsWithStats(binary_image, connectivity=8, ltype=cv2.CV_32S)

# 获取连通区域数目
num_labels = output[0]

# 获取连通区域属性
labels = output[1]
stats = output[2]
centroids = output[3]

# 绘制连通区域
for i in range(1, num_labels):
    # 获取连通区域面积和周长
    area = stats[i, cv2.CC_STAT_AREA]
    perimeter = cv2.arcLength(cv2.findContours(labels == i, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)[0], True)
    circularity = 4 * 3.1416 * area / perimeter ** 2
    
    # 检查是否为水体对象
    if area > min_area and circularity > min_circularity:
        # 绘制连通区域
        cv2.drawContours(image, [cv2.findContours(labels == i, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)[0]], -1, (0, 255, 0), 2)
# 3. 去除小面积噪声对象
import cv2

# 读取二值图像
binary_image = cv2.imread('binary_image.png', cv2.IMREAD_GRAYSCALE)

# 定义结构元素
kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))

# 开运算去噪
opening_image = cv2.morphologyEx(binary_image, cv2.MORPH_OPEN, kernel)
# 4. 合并相邻的水体对象
import cv2

# 读取二值图像
binary_image = cv2.imread('binary_image.png', cv2.IMREAD_GRAYSCALE)

# 定义结构元素
kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))

# 膨胀操作
dilated_image = cv2.dilate(binary_image, kernel, iterations=1)

# 腐蚀操作
eroded_image = cv2.erode(dilated_image, kernel, iterations=1)
# 5. 填补孔洞
import cv2

# 读取二值图像
binary_image = cv2.imread('binary_image.png', cv2.IMREAD_GRAYSCALE)

# 定义结构元素
kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))

# 开运算获取孔洞
opening_image = cv2.morphologyEx(binary_image, cv2.MORPH_OPEN, kernel)
hole_image = cv2.bitwise_not(opening_image)

# 形态学重建填补孔洞
filled_image = cv2.morphologyEx(hole_image, cv2.MORPH_CLOSE, kernel)
filled_image = cv2.bitwise_not(filled_image)