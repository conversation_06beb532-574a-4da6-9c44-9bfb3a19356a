library(randomForest)
library(caret)
library(GA)
library(rattle)
library(inTrees)
library(pROC)

inws1 = 'C:\\Users\\<USER>\\Desktop\\Water\\Validation\\data.csv'
df = read.csv(inws1)

data = subset(df, select=-c(1,3,5,6,17,24,29,30,33))

data$refer <- ifelse(data$type==100 | data$type==101 | data$type==102 | data$type==110 | data$type==111 | data$type==112, 'snow', 
                     ifelse(data$type==1, "other", "null"))
data$refer <- ifelse(data$type==10 | data$type==11 | data$type==12, "shadow", 
                     ifelse(data$type==1, "other", "null"))

data$refer <- ifelse(data$refer==2, "water", 
                     ifelse(data$refer==1, "other", "null"))
data$refer <- ifelse(data$type==2, "water", 
                     ifelse(data$type==10 | data$type==11 | data$type==12, "shadow", 
                            ifelse(data$type==100 | data$type==101 | data$type==102 | data$type==110 | data$type==111 | data$type==112, 'snow', 
                                   ifelse(data$type==1, "other", "null"))))
data = data[data$refer != 'null', ]

remove_outliers <- function(column) {
  threshold <- 2 * sd(column)
  column[abs(column - mean(column)) >= threshold] <- NA
  return(column)
}


data[,-c(22,24)] <- apply(data[,-c(22,24)], 2, remove_outliers)
data <- data[complete.cases(data), ]

set.seed(123)

train_index <- sample(nrow(data), round(0.9 * nrow(data)))

data_train <- data[train_index, ]
data_test <- data[-train_index, ]

library(RRF)
X_train <- data_train[,-c(22,24)]
X_test <- data_test[,-c(22,24)]

target_train <- data_train[,"refer"]
target_test <- data_test[,"refer"]
rf <- RRF(X_train,as.factor(target_train),ntree=500) # build an ordinary RF

treeList <- RF2List(rf)
tree_rules  <- extractRules(treeList,X_train, ntree=500, maxdepth = 5, random=TRUE, digits=3)
tree_rules <- unique(tree_rules)

data_test$refer <- ifelse(data_test$refer=="water", TRUE, 
                      ifelse(data_test$refer=="shadow" | data_test$refer=='snow' | data_test$refer=='other', FALSE, "null"))
data_test$refer <- ifelse(data_test$refer=="snow", TRUE, 
                          ifelse(data_test$refer=='other', FALSE, "null"))
data_test$refer <- ifelse(data_test$refer=="shadow", TRUE, 
                          ifelse(data_test$refer=='other', FALSE, "null"))
target_test <- data_test$refer
# 3. 定义适应度函数
fitness_function <- function(selected_rules) {
  X <- X_test
  # 应用规则子集对测试数据进行预测
  predictions <- eval(parse(text = selected_rules))
  
  # confusion_matrix <- table(target_test, predictions)
  # print(confusion_matrix)
  # 计算预测准确率
  accuracy <- mean(predictions == target_test)

    # recall <- confusion_matrix[2, 2] / sum(confusion_matrix[2, ])
  # 根据准确率和规则数量计算适应度值
  fitness_value <- accuracy# - 0.0001 * nchar(selected_rules)
  
  return(fitness_value)
}
data_test$refer <- ifelse(data_test$refer=="water", TRUE, 
                          ifelse(data_test$refer=="shadow" | data_test$refer=='snow' | data_test$refer=='other', FALSE, "null"))
target_test <- data_test$refer
ruleExec <- data.frame(ruleExec)
ruleExec$acc <- apply(ruleExec, 1, fitness_function)


library(caret)

data_test$predict <-  water
data_test$predict <- data_test$green_nir_divide>=1.053 | data_test$nir<0.07 #最优
data_test$predict <- data_test$EVI<=546 & data_test$red<0.514 & data_test$swir1<=0.054

data_test$zou <- (data_test$MNDWI>data_test$EVI | data_test$MNDWI>data_test$NDVI) & (data_test$EVI<1000)
data_test$before <- data_test$MNDWI>data_test$EVI | data_test$MNDWI>data_test$NDVI | (data_test$LSWI>data_test$NDVI)
data_test$before2 <- (data_test$MNDWI>data_test$EVI | data_test$MNDWI>data_test$BSI | data_test$LSWI>data_test$NDVI) & data_test$EVI<1000

data_test$mndwi <- data_test$MNDWI>0
confusionMatrix(data=as.factor(data_test$predict), reference = as.factor(data_test$refer))