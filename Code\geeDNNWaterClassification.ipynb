{"cells": [{"cell_type": "code", "execution_count": 2, "id": "d34e25d1", "metadata": {}, "outputs": [], "source": ["import ee\n", "import geemap"]}, {"cell_type": "code", "execution_count": 3, "id": "f2b83d60", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fetching credentials using gcloud\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Traceback (most recent call last):\n", "  File \"C:\\Users\\<USER>\\.conda\\envs\\geoai\\Lib\\site-packages\\ee\\oauth.py\", line 298, in _load_app_default_credentials\n", "    subprocess.run(command, check=True)\n", "  File \"C:\\Users\\<USER>\\.conda\\envs\\geoai\\Lib\\subprocess.py\", line 548, in run\n", "    with Popen(*popenargs, **kwargs) as process:\n", "         ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"C:\\Users\\<USER>\\.conda\\envs\\geoai\\Lib\\subprocess.py\", line 1024, in __init__\n", "    self._execute_child(args, executable, preexec_fn, close_fds,\n", "  File \"C:\\Users\\<USER>\\.conda\\envs\\geoai\\Lib\\subprocess.py\", line 1509, in _execute_child\n", "    hp, ht, pid, tid = _winapi.CreateProcess(executable, args,\n", "                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "FileNotFoundError: [WinError 2] 系统找不到指定的文件。\n", "\n", "The above exception was the direct cause of the following exception:\n", "\n", "Traceback (most recent call last):\n", "  File \"<frozen runpy>\", line 198, in _run_module_as_main\n", "  File \"<frozen runpy>\", line 88, in _run_code\n", "  File \"C:\\Users\\<USER>\\.conda\\envs\\geoai\\Scripts\\earthengine.exe\\__main__.py\", line 7, in <module>\n", "  File \"C:\\Users\\<USER>\\.conda\\envs\\geoai\\Lib\\site-packages\\ee\\cli\\eecli.py\", line 87, in main\n", "    _run_command()\n", "  File \"C:\\Users\\<USER>\\.conda\\envs\\geoai\\Lib\\site-packages\\ee\\cli\\eecli.py\", line 61, in _run_command\n", "    dispatcher.run(args, config)\n", "  File \"C:\\Users\\<USER>\\.conda\\envs\\geoai\\Lib\\site-packages\\ee\\cli\\commands.py\", line 360, in run\n", "    self.command_dict[vars(args)[self.dest]].run(args, config)\n", "  File \"C:\\Users\\<USER>\\.conda\\envs\\geoai\\Lib\\site-packages\\ee\\cli\\commands.py\", line 402, in run\n", "    ee.Authenticate(**args_auth)\n", "  File \"C:\\Users\\<USER>\\.conda\\envs\\geoai\\Lib\\site-packages\\ee\\__init__.py\", line 103, in Authenticate\n", "    return oauth.authenticate(authorization_code, quiet, code_verifier, auth_mode,\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"C:\\Users\\<USER>\\.conda\\envs\\geoai\\Lib\\site-packages\\ee\\oauth.py\", line 400, in authenticate\n", "    _load_app_default_credentials(auth_mode == 'gcloud', scopes, quiet)\n", "  File \"C:\\Users\\<USER>\\.conda\\envs\\geoai\\Lib\\site-packages\\ee\\oauth.py\", line 301, in _load_app_default_credentials\n", "    raise Exception('gcloud command not found. ' + tip) from e\n", "Exception: gcloud command not found. Please ensure that gcloud is installed.\n", "More information: https://developers.google.com/earth-engine/guides/python_install\n", "\n"]}], "source": ["#access permission of GEE\n", "!earthengine authenticate"]}, {"cell_type": "code", "execution_count": 5, "id": "2d03e54a", "metadata": {}, "outputs": [{"data": {"text/html": ["<p>To authorize access needed by Earth Engine, open the following\n", "        URL in a web browser and follow the instructions:</p>\n", "        <p><a href=https://code.earthengine.google.com/client-auth?scopes=https%3A//www.googleapis.com/auth/earthengine%20https%3A//www.googleapis.com/auth/devstorage.full_control&request_id=YdM-mK8ziY2qKKXFE3YnZGd1JFIhVqwctO0ZANeMDG8&tc=xoC29kcbxFUY1Zu2OG2ajfQ3b9hX3tf9-4t19Yqju18&cc=PLQhVRaovxeToB-SqtrepkxyPXggtBDM4rPUgTUMxAg>https://code.earthengine.google.com/client-auth?scopes=https%3A//www.googleapis.com/auth/earthengine%20https%3A//www.googleapis.com/auth/devstorage.full_control&request_id=YdM-mK8ziY2qKKXFE3YnZGd1JFIhVqwctO0ZANeMDG8&tc=xoC29kcbxFUY1Zu2OG2ajfQ3b9hX3tf9-4t19Yqju18&cc=PLQhVRaovxeToB-SqtrepkxyPXggtBDM4rPUgTUMxAg</a></p>\n", "        <p>The authorization workflow will generate a code, which you should paste in the box below.</p>\n", "        "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Successfully saved authorization token.\n"]}], "source": ["ee.Authenti<PERSON>()\n", "ee.Initialize()"]}, {"cell_type": "code", "execution_count": 6, "id": "9348ec87", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6a223c13d20245fcabba4d7d4e436dca", "version_major": 2, "version_minor": 0}, "text/plain": ["Map(center=[20, 0], controls=(WidgetControl(options=['position', 'transparent_bg'], widget=HBox(children=(Togg…"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["Map = geemap.Map()\n", "Map"]}, {"attachments": {}, "cell_type": "markdown", "id": "45d559a9", "metadata": {}, "source": ["# 导入样本数据"]}, {"cell_type": "code", "execution_count": 7, "id": "7ae6722b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["19887\n", "{'type': 'FeatureCollection', 'columns': {'landcover': 'Long', 'nir': 'Float', 'red': 'Float', 'swir1': 'Float', 'system:index': 'String'}, 'version': 1687646395382945, 'id': 'users/studyroomGEE/testFolder/DNNWaterTest', 'properties': {'system:asset_size': 887729}, 'features': [{'type': 'Feature', 'geometry': {'type': 'Point', 'coordinates': [114.2202926395152, 30.327978295470984]}, 'id': '00000000000000000edf', 'properties': {'landcover': 0, 'nir': 0.3388, 'red': 0.0625, 'swir1': 0.1815}}, {'type': 'Feature', 'geometry': {'type': 'Point', 'coordinates': [114.67708741053124, 30.29779464044021]}, 'id': '0000000000000000110e', 'properties': {'landcover': 0, 'nir': 0.251, 'red': 0.0625, 'swir1': 0.1546}}, {'type': 'Feature', 'geometry': {'type': 'Point', 'coordinates': [114.53910498753342, 30.724673302488686]}, 'id': '00000000000000001a2e', 'properties': {'landcover': 0, 'nir': 0.3145, 'red': 0.0625, 'swir1': 0.1671}}, {'type': 'Feature', 'geometry': {'type': 'Point', 'coordinates': [114.2873976512685, 30.331750694962196]}, 'id': '00000000000000001c69', 'properties': {'landcover': 0, 'nir': 0.2498, 'red': 0.0625, 'swir1': 0.2165}}, {'type': 'Feature', 'geometry': {'type': 'Point', 'coordinates': [114.54530313799772, 30.473233512286978]}, 'id': '000000000000000021fd', 'properties': {'landcover': 0, 'nir': 0.119, 'red': 0.0625, 'swir1': 0.0599}}]}\n"]}], "source": ["waterDNN_test = ee.FeatureCollection(\"users/studyroomGEE/testFolder/DNNWaterTest\")\n", "print(waterDNN_test.size().getInfo())\n", "print(waterDNN_test.limit(5).getInfo())"]}, {"attachments": {}, "cell_type": "markdown", "id": "96704169", "metadata": {}, "source": ["# 重新组织样本"]}, {"cell_type": "code", "execution_count": 8, "id": "7133ea0d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['landcover', 'nir', 'red', 'swir1']\n", "feaBands_2 size 4\n", "feaBands_2 ['landcover', 'nir', 'red', 'swir1']\n", "trainSet2 [[0, 0.3388, 0.0625, 0.1815], [0, 0.251, 0.0625, 0.1546], [0, 0.3145, 0.0625, 0.1671], [0, 0.2498, 0.0625, 0.2165]]\n"]}], "source": ["firstElement = waterDNN_test.first()\n", "feaBands = firstElement.toDictionary().keys()\n", "print(feaBands.getInfo())\n", "\n", "feaBands_2 = feaBands #.add('class').add(\"class_1\")\n", "print(\"feaBands_2 size\",feaBands_2.size().getInfo())\n", "print(\"feaBands_2\",feaBands_2.getInfo())\n", "feaSize = feaBands_2.size().getInfo()\n", "\n", "trainSet2 = waterDNN_test.reduceColumns(**{\n", "   \"reducer\":ee.Reducer.toList().repeat(feaBands_2.size()),\n", "   \"selectors\":feaBands_2\n", "}).values().get(0)\n", "trainSet2 = ee.Array(trainSet2).transpose()\n", "print(\"trainSet2\",trainSet2.slice(0,0,4).getInfo())"]}, {"attachments": {}, "cell_type": "markdown", "id": "dc81669c", "metadata": {}, "source": ["# 获取标签"]}, {"cell_type": "code", "execution_count": 9, "id": "0756f145", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["X [[0.3388 0.0625 0.1815]\n", " [0.251  0.0625 0.1546]\n", " [0.3145 0.0625 0.1671]\n", " [0.2498 0.0625 0.2165]\n", " [0.119  0.0625 0.0599]]\n", "(19887, 3)\n", "Y [0. 0. 0. 0. 0.]\n"]}], "source": ["#汇总样本\n", "import numpy as np\n", "np.random.seed(0) \n", "trainSet3 = trainSet2.getInfo()\n", "trainSet3 = np.array(trainSet3)\n", "X = trainSet3[:,1:]\n", "print(\"X\",X[0:5])\n", "print(X.shape)\n", "\n", "Y = trainSet3[:,0]\n", "print(\"Y\",Y[0:5])"]}, {"cell_type": "code", "execution_count": 10, "id": "167de6bd", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "from matplotlib import pyplot as plt\n", "import torch\n", "from torch import nn\n", "import torch.nn.functional as F\n", "from torch.utils.data import Dataset,DataLoader,TensorDataset"]}, {"cell_type": "code", "execution_count": 11, "id": "1f41390d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'torch.Tensor'>\n", "<class 'torch.Tensor'>\n", "<PERSON>.<PERSON><PERSON>([19887, 3])\n", "<PERSON>.<PERSON><PERSON>([19887])\n", "torch.float32\n"]}], "source": ["X1 = torch.from_numpy(np.float32(X))\n", "Y1 = torch.from_numpy(np.float32(Y))\n", "print(type(X1))\n", "print(type(Y1))\n", "print(X1.size())\n", "print(Y1.size())\n", "print(Y1.dtype)"]}, {"cell_type": "code", "execution_count": 12, "id": "0795477b", "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "#构建输入数据管道\n", "\"\"\"\n", "ds = TensorDataset(X1,Y1)\n", "dl = DataLoader(ds,batch_size = 16,shuffle=True)"]}, {"cell_type": "code", "execution_count": 13, "id": "08ceb54b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["init loss: 0.6885104179382324\n", "init metric: 0.5625\n", "epoch = 10 loss =  0.2755349222832897 metric =  0.9103982300884956\n", "epoch = 20 loss =  0.5720709165682221 metric =  0.9086819522724167\n", "epoch = 30 loss =  1.5288166067110776 metric =  0.909134486463889\n", "epoch = 40 loss =  1.8145576841083588 metric =  0.9044616519206009\n", "epoch = 50 loss =  2.4880045400862754 metric =  0.9080819254523789\n"]}], "source": ["\"\"\"\n", "2, 定义模型\n", "\"\"\"\n", "class DNNModel(nn.Module):\n", "    def __init__(self):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.fc1 = nn.Linear(3,128)\n", "        self.fc2 = nn.<PERSON>ar(128,64)\n", "        self.fc3 = nn.<PERSON><PERSON>(64,1)\n", "\n", "    def forward(self,x):\n", "        x = F.relu(self.fc1(x))\n", "        x = <PERSON>.relu(self.fc2(x))\n", "        y = nn.<PERSON>g<PERSON><PERSON>()(self.fc3(x))\n", "        y = y.squeeze(-1)\n", "        return y\n", "\n", "    def loss_func(self,y_pred,y_true):\n", "        return nn.BCELoss()(y_pred,y_true)\n", "\n", "    def metric_func(self,y_pred,y_true):\n", "        y_pred = torch.where(y_pred > 0.5, torch.ones_like(y_pred, dtype=torch.float32),torch.zeros_like(y_pred, dtype=torch.float32))\n", "        acc = torch.mean(1 - torch.abs(y_true - y_pred))\n", "        return acc\n", "\n", "    @property\n", "    def optimizer(self):\n", "        return torch.optim.<PERSON>(self.parameters(), lr=0.001)\n", "\n", "\n", "model = DNNModel()\n", "\n", "# 测试模型结构\n", "(features,labels) = next(iter(dl))\n", "predictions = model(features)\n", "\n", "loss = model.loss_func(predictions,labels)\n", "metric = model.metric_func(predictions,labels)\n", "\n", "print(\"init loss:\",loss.item())\n", "print(\"init metric:\",metric.item())\n", "\n", "\"\"\"\n", "3，训练模型\n", "\"\"\"\n", "def train_step(model, features, labels):\n", "\n", "    # 正向传播求损失\n", "    predictions = model(features)\n", "    loss = model.loss_func(predictions,labels)\n", "    metric = model.metric_func(predictions,labels)\n", "\n", "    # 反向传播求梯度\n", "    loss.backward()\n", "\n", "    # 更新模型参数\n", "    model.optimizer.step()\n", "    model.optimizer.zero_grad()\n", "\n", "    return loss.item(),metric.item()\n", "\n", "# 测试train_step效果\n", "features,labels = next(iter(dl))    #非for循环就用next\n", "train_step(model,features,labels)\n", "\n", "def train_model(model,epochs):\n", "    for epoch in range(1,epochs+1):\n", "        loss_list,metric_list = [],[]\n", "        for features, labels in dl:\n", "            lossi,metrici = train_step(model,features,labels)\n", "            loss_list.append(lossi)\n", "            metric_list.append(metrici)\n", "        loss = np.mean(loss_list)\n", "        metric = np.mean(metric_list)\n", "\n", "        if epoch%10==0:\n", "            print(\"epoch =\",epoch,\"loss = \",loss,\"metric = \",metric)\n", "\n", "train_model(model, epochs = 50)"]}, {"attachments": {}, "cell_type": "markdown", "id": "d0251fad", "metadata": {}, "source": ["# 查看网络参数名称"]}, {"cell_type": "code", "execution_count": 14, "id": "42b320ec", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["fc1.weight\n", "fc1.bias\n", "fc2.weight\n", "fc2.bias\n", "fc3.weight\n", "fc3.bias\n"]}], "source": ["for name in model.state_dict():\n", "  print(name)"]}, {"attachments": {}, "cell_type": "markdown", "id": "7498c867", "metadata": {}, "source": [" # 查看网络模型参数值"]}, {"cell_type": "code", "execution_count": 15, "id": "f3067dc8", "metadata": {}, "outputs": [], "source": ["fc1_w = model.state_dict()['fc1.weight'].numpy().tolist()\n", "# print(fc1_w)\n", "# print(len(fc1_w))\n", "# print(len(fc1_w[0]))\n", "fc1_b = model.state_dict()['fc1.bias'].numpy().tolist()\n", "# print(fc1_b)\n", "fc2_w = model.state_dict()['fc2.weight'].numpy().tolist()\n", "# print(fc2_w)\n", "fc2_b = model.state_dict()['fc2.bias'].numpy().tolist()\n", "# print(fc2_b)\n", "fc3_w = model.state_dict()['fc3.weight'].numpy().tolist()\n", "# print(fc3_w)\n", "fc3_b = model.state_dict()['fc3.bias'].numpy().tolist()\n", "# print(fc3_b)"]}, {"attachments": {}, "cell_type": "markdown", "id": "a41d67ef", "metadata": {}, "source": ["# 将参数值转化为GEE可以调用的数据格式，这一步很关键"]}, {"cell_type": "code", "execution_count": 16, "id": "9821d878", "metadata": {}, "outputs": [], "source": ["fc1_w = ee.Array(fc1_w)\n", "fc1_w = ee.Image(fc1_w)\n", "fc1_b = ee.Array(fc1_b)\n", "fc1_b = ee.Image(fc1_b).toArray(1)\n", "\n", "fc2_w = ee.Array(fc2_w)\n", "fc2_w = ee.Image(fc2_w)\n", "fc2_b = ee.Array(fc2_b)\n", "fc2_b = ee.Image(fc2_b).toArray(1)\n", "\n", "fc3_w = ee.Array(fc3_w)\n", "fc3_w = ee.Image(fc3_w)\n", "fc3_b = ee.Array(fc3_b)\n", "fc3_b = ee.Image(fc3_b).toArray(1)\n"]}, {"cell_type": "code", "execution_count": 17, "id": "5efef03f", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6a223c13d20245fcabba4d7d4e436dca", "version_major": 2, "version_minor": 0}, "text/plain": ["Map(bottom=2077.0, center=[23.160563309048314, -54.09667968750001], controls=(WidgetControl(options=['position…"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["AOI1 = ee.Geometry.Polygon([[[113.79434897854806, 30.790698376210745],\\\n", "          [113.79434897854806, 30.094534162052266],\\\n", "          [114.75290610745431, 30.094534162052266],\\\n", "          [114.75290610745431, 30.790698376210745]]]);\n", "AOI2 = ee.Geometry.Polygon([[[117.17765814397498, 31.79514587976386],\\\n", "          [117.17765814397498, 31.359925949997173],\\\n", "          [117.93571478459998, 31.359925949997173],\\\n", "          [117.93571478459998, 31.79514587976386]]], None, False)\n", "AOI3 = ee.Geometry.Polygon([[[115.97726309673538, 29.259925180464972],\\\n", "          [115.97726309673538, 28.80605348450367],\\\n", "          [116.72707999126663, 28.80605348450367],\\\n", "          [116.72707999126663, 29.259925180464972]]])\n", "roi = AOI3\n", "# print(roi.getInfo())\n", "Map.addLayer(roi)\n", "Map.centerObject(roi)\n", "Map"]}, {"cell_type": "code", "execution_count": 18, "id": "6ca6ac0e", "metadata": {}, "outputs": [], "source": ["#remove cloud for Landsat 4, 5 and 7\n", "def rmL457Cloud(image):\n", "    \n", "    qa = image.select('pixel_qa')\n", "    cloud = qa.bitwiseAnd(1 << 5)\\\n", "              .And(qa.bitwiseAnd(1 << 7))\\\n", "              .Or(qa.bitwiseAnd(1 << 3))\n", "    mask2 = image.mask().reduce(ee.Reducer.min())\n", "    mask3 = image.select(\"B1\").gt(2000)\n", "    return image.updateMask(cloud.Not()).updateMask(mask2).updateMask(mask3.Not()).toDouble().divide(1e4)\\\n", "              .copyProperties(image)\\\n", "              .copyProperties(image, [\"system:time_start\",'system:time_end'])\\\n", "\n", "#  reomove cloud for Landsat-8\n", "def rmL8Cloud(image):\n", "    \n", "    cloudShadowBitMask = (1 << 3)\n", "    cloudsBitMask = (1 << 5);\n", "    qa = image.select('pixel_qa');\n", "    mask = qa.bitwiseAnd(cloudShadowBitMask).eq(0)\\\n", "                 .And(qa.bitwiseAnd(cloudsBitMask).eq(0))\n", "    mask2 = image.select(\"B2\").gt(2000)               \n", "    return image.updateMask(mask).updateMask(mask2.Not()).toDouble().divide(1e4)\\\n", "              .copyProperties(image)\\\n", "              .copyProperties(image, [\"system:time_start\",'system:time_end'])"]}, {"cell_type": "code", "execution_count": 19, "id": "c6bec763", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6a223c13d20245fcabba4d7d4e436dca", "version_major": 2, "version_minor": 0}, "text/plain": ["Map(bottom=109263.0, center=[29.033250850320215, 116.35217154400101], controls=(WidgetControl(options=['positi…"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["# Assign a common name to the sensor-specific bands.\n", "LC8_BANDS = ['B2',   'B3',    'B4',  'B5',  'B6',    'B7'] #/Landsat 8\n", "LC7_BANDS = ['B1',   'B2',    'B3',  'B4',  'B5',    'B7'] # Landsat 7\n", "LC5_BANDS = ['B1',   'B2',    'B3',  'B4',  'B5',    'B7'] # Landsat 5\n", "S2_BANDS  = ['B2',   'B3',    'B4',  'B8',  'B11',   'B12'] #Sentinel-2\n", "STD_NAMES = ['blue', 'green', 'red', 'nir', 'swir1', 'swir2']\n", "\n", "# efine year variable\n", "year = '2020'\n", "\n", "# dataset input\n", "#  landsat 8\n", "l8Col = ee.ImageCollection('LANDSAT/LC08/C01/T1_SR')\\\n", "           .map(rmL8Cloud)\\\n", "           .filterBounds(roi)\\\n", "           .filterDate(year+'-05-01',year+'-07-01')\\\n", "            .select(LC8_BANDS, STD_NAMES)\n", "# landsat 7 \n", "l7Col = ee.ImageCollection('LANDSAT/LE07/C01/T1_SR')\\\n", "           .map(rmL457Cloud)\\\n", "           .filterBounds(roi)\\\n", "           .filterDate(year+'-05-01',year+'-07-01')\\\n", "           .select(LC7_BANDS, STD_NAMES);\n", "# landsat 5  \n", "l5Col = ee.ImageCollection('LANDSAT/LT05/C01/T1_SR')\\\n", "           .map(rmL457Cloud)\\\n", "           .filterBounds(roi)\\\n", "           .filterDate(year+'-05-01',year+'-07-01')\\\n", "           .select(LC5_BANDS, STD_NAMES)\n", "\n", "# l8Col.merge(l7Col).merge(S2Col)\n", "L578COl = ee.ImageCollection(l7Col.merge(l8Col).merge(l5Col))\n", "                \n", "PalettePanel = {'bands':[\"swir1\",\"nir\",\"red\"],'min':0,'max':0.3}\n", "\n", "imgMedian = L578COl.min().clip(roi).select([\"swir1\",\"nir\",\"red\"])\n", "\n", "Map.addLayer(imgMedian,{'Bands':['swir','nir','red'],'min':0,'max':0.3},'imgMedian')\n", "Map"]}, {"attachments": {}, "cell_type": "markdown", "id": "75da62a7", "metadata": {}, "source": ["# 前向推理过程，相当于复写网络模型"]}, {"cell_type": "code", "execution_count": 20, "id": "68c59c1e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["featureBand ['nir', 'red', 'swir1']\n"]}], "source": ["featureBand = feaBands_2.remove('landcover')\n", "print(\"featureBand\",featureBand.getInfo())"]}, {"cell_type": "code", "execution_count": 21, "id": "9a72e0cf", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6a223c13d20245fcabba4d7d4e436dca", "version_major": 2, "version_minor": 0}, "text/plain": ["Map(bottom=109263.0, center=[29.033358552757804, 116.35208129882814], controls=(WidgetControl(options=['positi…"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["imgArray = imgMedian.select(featureBand).toArray().toArray(1)\n", "\n", "t = fc1_w.matrixMultiply(imgArray).add(fc1_b)\n", "t = t.gt(0).multiply(t)\n", "\n", "# Map.addLayer(t)\n", "t = fc2_w.matrixMultiply(t).add(fc2_b)\n", "t = t.gt(0).multiply(t)\n", "\n", "t = fc3_w.matrixMultiply(t).add(fc3_b)\n", "t = t.arrayGet([0,0]).clip(roi)\n", "\n", "t = ee.Image(1).divide(t.multiply(-1).exp().add(ee.Image(1))).clip(roi)\n", "\n", "# Map.addLayer(t,{'palette':['grey','blue'],'min':0,'max':1},'t')\n", "Map.addLayer(t.gt(0.5).selfMask(),{'palette':'red'},'water')\n", "Map"]}, {"cell_type": "code", "execution_count": null, "id": "c9860902", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}