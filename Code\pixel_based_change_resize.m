%影像数据参数
[aa,R] = geotiffread('D:\xiaozhen\water_change\1990.tif');
aa=imresize(imread('D:\xiaozhen\water_change\1990.tif'), 0.1, 'bilinear');%先导入纬度数据
info=geotiffinfo('D:\xiaozhen\water_change\1990.tif');
[m,n]=size(aa);
% 删除aa变量，释放内存
clear aa

%水体数据数量
years = 7;
num = m*n;
waterSum = zeros(num, years, 'uint16');

%变化数据存储
changeRate = zeros(m,n, 'int16')+NaN;
pz = zeros(m,n, 'double')+NaN;
%分区数量
zones=30;
%分区循环
%slice1 = floor(m*n*((zone-1)/zones))+1;
%slice2 = floor(m*n*(zone/zones));
num = m*n;
%将所有时序的数据按列导入数组
for year=1990:1996
    %进行重采样，缩放至0.1
    water = imresize(imread(['D:\xiaozhen\water_change\', int2str(year), '.tif']), 0.1, 'bilinear');
    water(water<0) = NaN;
    waterSum(:, year-1989) = reshape(water, m*n, 1);
end

%删除water变量，释放内存
clear water

%按行计算变化斜率、显著性
p=parpool(32);

parfor i=1:num
    water = waterSum(i,:)';
    try
        if sum(water)>0
            X = [ones(size(water)), (1:years)'];
            water = double(water);
            X = double(X);
            [b,bint,r,rint,stats] = regress(water,X);
        
            changeRate(i) = b(2);
            pz(i) = stats(3);
        end

        if mod(i/num, 0.1) == 0
            print('precess: (1.0)', i/num);
        end

    catch
        changeRate(i) = -9999;
        pz(i) = -9999;
    end
end
delete(p);
%写出斜率栅格
R.RasterSize = [m, n];
filename = ['D:\xiaozhen\changeRate', int2str(zones), '.tif'];
geotiffwrite(filename, changeRate, R);

%写出显著性栅格
filename = ['D:\xiaozhen\pz', int2str(zones), '.tif'];
geotiffwrite(filename, pz, R);