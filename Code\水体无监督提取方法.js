////////////////////Cloud Masking
// Create Landsat 45789 Collection Function.
function createLandsatCollection(params) {
    var defaultParams = {
        region: Map.getBounds(true),
        start: '1982-01-01',
        end: formatDate(new Date()),
        mapImage: function (image) { return image }
    }
    params = mergeObjects([defaultParams, params])

    var filter = ee.Filter.and(
        ee.Filter.bounds(params.region),
        ee.Filter.date(params.start, params.end)
    )
    function applyScaleFactorsL457(image) {
        var opticalBands = image.select('SR_B.').multiply(0.0000275).add(-0.2);
        var thermalBand = image.select('ST_B6').multiply(0.00341802).add(149.0);
        return image.addBands(opticalBands, null, true)
            .addBands(thermalBand, null, true)
    }
    function applyScaleFactorsL89(image) {
        var opticalBands = image.select('SR_B.').multiply(0.0000275).add(-0.2);
        var thermalBands = image.select('ST_B.*').multiply(0.00341802).add(149.0);
        return image.addBands(opticalBands, null, true)
            .addBands(thermalBands, null, true)
    }
    function cloudMask(image) {
        // Bits 3 and 5 are cloud shadow and cloud, respectively.
        var dilatedBitMask = (1<<1)
        var cirrusBitMask = (1 <<2)
        var cloudShadowBitMask = (1 << 3);
        var cloudsBitMask = (1 << 4);
        var snowBitMask = (1 << 5)
        var cleanBitMask = (1<<6)
        // Get the pixel QA band.
        var qa = image.select('QA_PIXEL');
        // Both flags should be set to zero, indicating clear conditions.
        var mask = qa.bitwiseAnd(cloudShadowBitMask).eq(0)
            .and(qa.bitwiseAnd(cloudsBitMask).eq(0))
            .and(qa.bitwiseAnd(snowBitMask).eq(0))
            .and(qa.bitwiseAnd(cirrusBitMask).eq(0))
            .and(qa.bitwiseAnd(dilatedBitMask).eq(0))
            // .and(qa.bitwiseAnd(cleanBitMask).eq(0))
            
        var mask2 = image.mask().reduce(ee.Reducer.min());

        return image.updateMask(mask).updateMask(mask2).copyProperties(image);
    }
    
    var elevation = ee.Image("NASA/NASADEM_HGT/001").select('elevation');

    var Rmshadow = function (img) {
        var SUN_AZIMUTH = ee.Number(img.get('SUN_AZIMUTH'))
        var SUN_ELEVATION = ee.Number(img.get('SUN_ELEVATION'))
        // Output 1 where pixels are illumunated and 0 where they are shadowed.
        var shadow = ee.Terrain.hillshade(elevation, SUN_AZIMUTH, SUN_ELEVATION).divide(255)
        //山体阴影属于无效观测，光线照不到，所以不计入总数
        return img.updateMask(shadow.gt(0.7))
    }

    var l4 = ee.ImageCollection('LANDSAT/LT04/C02/T1_L2')
        .filter(filter)
        .map(applyScaleFactorsL457)
        .map(cloudMask)
        .select(
            ['SR_B1', 'SR_B2', 'SR_B3', 'SR_B4', 'SR_B5'],
            ['blue', 'green', 'red', 'nir', 'swir1']
        )

    var l5 = ee.ImageCollection('LANDSAT/LT05/C02/T1_L2')
        .filter(filter)
        .map(applyScaleFactorsL457)
        .map(cloudMask)
        .select(
            ['SR_B1', 'SR_B2', 'SR_B3', 'SR_B4', 'SR_B5'],
            ['blue', 'green', 'red', 'nir', 'swir1']
        )

    var l7 = ee.ImageCollection('LANDSAT/LE07/C02/T1_L2')
        .filter(filter)
        .map(applyScaleFactorsL457)
        .map(cloudMask)
        .select(
            ['SR_B1', 'SR_B2', 'SR_B3', 'SR_B4', 'SR_B5'],
            ['blue', 'green', 'red', 'nir', 'swir1']
        )

    var l8 = ee.ImageCollection('LANDSAT/LC08/C02/T1_L2')
        .filter(filter)
        .map(applyScaleFactorsL89)
        .map(cloudMask)
        .select(
            ['SR_B2', 'SR_B3', 'SR_B4', 'SR_B5', 'SR_B6'],
            ['blue', 'green', 'red', 'nir', 'swir1']
        )

    var l9 = ee.ImageCollection('LANDSAT/LC09/C02/T1_L2')
        .filter(filter)
        .map(applyScaleFactorsL89)
        .map(cloudMask)
        .select(
            ['SR_B2', 'SR_B3', 'SR_B4', 'SR_B5', 'SR_B6'],
            ['blue', 'green', 'red', 'nir', 'swir1']
        )

    return l4.merge(l5).merge(l7).merge(l8).merge(l9)
        .map(Rmshadow)
        .map(mapImage)
        .sort('system:time_start')

    function mapImage(image) {
        return params.mapImage(image)
            .clip(params.region)
    }

    function formatDate(date) {
        var d = new Date(date),
            month = '' + (d.getMonth() + 1),
            day = '' + d.getDate(),
            year = d.getFullYear()

        if (month.length < 2)
            month = '0' + month
        if (day.length < 2)
            day = '0' + day

        return [year, month, day].join('-')
    }

    function mergeObjects(objects) {
        return objects.reduce(function (acc, o) {
            for (var a in o) { acc[a] = o[a] }
            return acc
        }, {})
    }
}

// Index Function.
// NDSI
function toNDSI(image) {
    var bands = image.select('green', 'swir1')
    return bands.expression(
        '(i.green - i.swir1) / (i.green + i.swir1)',
        { i: bands }
    ).multiply(10000).rename('NDSI')
}
// NDVI
function toNDVI(image) {
    var bands = image.select('nir', 'red')
    return bands.expression(
        '(i.nir - i.red) / (i.nir + i.red)',
        { i: bands }
    ).multiply(10000).rename('NDVI')
}
// EVI
function toEVI(image) {
    var bands = image.select('red', 'nir', 'blue')
    return bands.expression(
        '2.5 * (i.nir - i.red) / (i.nir + 6 * i.red - 7.5 * i.blue + 1)',
        { i: bands }
    ).multiply(10000).int16().rename('EVI')
}
// MNDWI
function toNDWI(image) {
    var bands = image.select('green', 'swir1')
    return bands.expression(
        '(i.green - i.swir1) / (i.green + i.swir1)',
        { i: bands }
    ).multiply(10000).int16().rename('NDWI')
}
// NDWI
function toMNDWI(image) {
    var bands = image.select('green', 'nir')
    return bands.expression(
        '(i.green - i.nir) / (i.green + i.nir)',
        { i: bands }
    ).multiply(10000).int16().rename('MNDWI')
}
// LSWI
function toLSWI(image) {
    var bands = image.select('nir', 'swir1')
    return bands.expression(
        '(i.nir - i.swir1) / (i.nir + i.swir1)',
        { i: bands }
    ).multiply(10000).int16().rename('LSWI')
}
// AWEIsh
function toAWEIsh(image) {
    var bands = image.select('blue', 'green', 'nir', 'swir1', 'swir2')
    return bands.expression(
        'i.blue + 2.5*i.green - 1.5*(i.nir+i.swir1) - 0.25*i.swir2',
        { i: bands }
    ).multiply(10000).int16().rename('AWEIsh')
}
// BSI
var toBSI = function (image) {
    var bsi = image.expression(
        '((RED + SWIR) - (NIR + BLUE)) / ((RED + SWIR) + (NIR + BLUE)) ',
        {
            'RED': image.select('red'),
            'BLUE': image.select('blue'),
            'NIR': image.select('nir'),
            'SWIR': image.select('swir1'),
        })
        .rename('BSI')
        .multiply(10000)
        .int16()
    return bsi
};
var toNDBI = function (image) {
    var ndbi = image.expression(
        '(SWIR1-NIR) / (SWIR1+NIR)',
        {
            'SWIR1': image.select('swir1'),
            'NIR': image.select('nir')
        })
        .rename('NDBI')
        .multiply(10000)
        .int16()
    return ndbi
}
// All index add
var addIndex = function (img) {
    return img.addBands(toEVI(img))
        .addBands(toNDVI(img))
        .addBands(toMNDWI(img))
        .addBands(toBSI(img))
        .addBands(toLSWI(img))
        // .addBands(toNDBI(img))
        .addBands(toNDSI(img))
        .addBands(toNDWI(img))
}

var calWater = function (img) {
    var MNDWI = img.select('MNDWI')
    var NDVI = img.select('NDVI')
    var BSI = img.select('BSI')
    var EVI = img.select('EVI')
    var LSWI = img.select('LSWI')
    var NDBI = img.select('NDBI')
    var NDSI = img.select('NDSI')
    // var AWEIsh = img.select('AWEIsh')
    // var water = ((MNDWI.gt(EVI)).or(MNDWI.gt(NDVI)).or(LSWI.gt(NDVI))).rename('water')
    var water = ((MNDWI.gt(EVI)).or(MNDWI.gt(NDVI)).and(EVI.lt(1000))).rename('water')
    
    return water.unmask(-1)
}
var thresholding = require('users/gena/packages:thresholding')



var calWater2 = function(img) {
  var ndwi = img.select('NDWI')
  var hist = ndwi.reduceRegion({
    reducer: ee.Reducer.histogram(256), 
    geometry: geometry, 
    scale: 30, 
    maxPixels: 1e13
  }).values().get(0)
  
  var th = thresholding.otsu(hist)
  
  var water = ndwi.gt(th).rename('water')
  
  return water.unmask(-1)
}
//-----------------------------------------------------
// var poi = geometry
var poi = geometry

var mths=ee.List.sequence(1,12);
var yrs=ee.List.sequence(2010,2020)
var countScale=30; //pixel size for counting (meter). Make sure to adjust the 

///Composite into Monthly images
var lsCol = createLandsatCollection({ 
    region: poi,
    start: '1990-01-01',
    end: '2023-01-01',
    mapImage: function (image){ 
      return image
    }
})

lsCol = ee.ImageCollection.fromImages(yrs.map(function(y){
  var out=mths.map(function(mth){
  var Lf=lsCol.filter(ee.Filter.calendarRange({start:y,field:'year'})).filter(ee.Filter.calendarRange({start:mth,field:'month'}))
  Lf = Lf.mean()
  Lf = Lf.set({'system:time_start':ee.Date.fromYMD(y,mth,1).millis()})
         .set('date', ee.Number(y).multiply(100).add(ee.Number(mth)).int())
  
  return(Lf)
  })
  
  return(out)
}).flatten())

////////////////////My method.
lsCol=lsCol.map(function(im) {return(im.set({'empty':ee.Algorithms.If(im.bandTypes(),0,1)}))}).filterMetadata('empty','equals',0)
var bounds = poi
var scale = Map.getScale()
var cannyThreshold = 0.7
var cannySigma = 1
var minValue = -0.5
var waterCol = lsCol.map(function(img){
  var index = addIndex(img)
  // var water = calWater(index)
  var water2 = calWater2(index)
  return water2.copyProperties(img, ['system:time_start', 'date'])
})

// print(waterCol.limit(10))
////////////////////Unsupervised Classification
//MNDWI
lsCol=lsCol.map(function(im){return(im.addBands(im.normalizedDifference(['green','swir1'])))})
//Count number of unmasked pixels
// lsCol=lsCol.map(function(im) {
//   return(im.set({'PixTot':
//   im.select(0).gt(0).reduceRegion({
//   reducer: ee.Reducer.sum(),
//   geometry: poi,
//   scale: countScale,
//   maxPixels: 1e13})
//   .get('blue')}))})

//Get median threshold from K.means unsupervised classification
function getThreshKMeans(im1){
  im1=im1.select('nd')
  // Make the training dataset.
  var training = im1.sample({
    region: poi,
    scale: countScale,
    numPixels: 2000});
  // Instantiate the clusterer and train it.
  var clusterer = ee.Clusterer.wekaKMeans(2).train(training);
  var classified = im1.cluster(clusterer);
  //max NDWI for each class
  var maxOnes=ee.Number(im1.mask(classified).reduceRegion({'reducer':ee.Reducer.max(),'geometry':poi,'scale':countScale,'bestEffort':true}).get('nd'))
  var maxZeros=ee.Number(im1.mask(classified.eq(0)).reduceRegion({'reducer':ee.Reducer.max(),'geometry':poi,'scale':countScale,'bestEffort':true}).get('nd'))
  return(im1.set({'thresh':maxOnes.min(maxZeros)}))
  }
//Only use high quality images, here images where 1000/1001 the poi or more are unmasked
// var Med_thresh=ee.Number(lsCol.filterMetadata('PixTot','not_less_than',ee.Number(lsCol.aggregate_max("PixTot")).divide(1.0001))
//     .map(getThreshKMeans).reduceColumns(ee.Reducer.median(),['thresh']).get('median'))

// print('MNDWI Threshold:',Med_thresh)
var Med_thresh = 0.129
//Classify images
var waterCol2 = lsCol.map(function(im){return(im.addBands(im.select('nd')).gt(Med_thresh).unmask(-1).select(['nd_1'],['water'])).copyProperties(im, ['system:time_start', 'date'])})
print(lsCol.limit(10))
Map.addLayer(lsCol.filterDate('2018-09-01').first(), {bands: ['red', 'green', 'blue']}, 'lsCol')
Map.addLayer(waterCol2.filterDate('2018-09-01').first().updateMask(image.mask()), {palette: ['grey', 'green', 'blue']}, 'MNDWI')
Map.addLayer(waterCol.filterDate('2018-09-01').first().updateMask(image.mask()), {palette: ['grey', 'green', 'blue']}, 'my method')
// Map.addLayer(image2, {palette: ['green', 'blue']}, 'filled_img')

var size = lsCol.size().getInfo()
var listOfImage = waterCol.toList(size)

// for (var i = 0; i < size; i++) {
//     var img = ee.Image(listOfImage.get(i));
//     var id = img.get('date').getInfo().toString();
//     var assetId = 'waterCol_cloud_Poyang_my_method'
    
//     Export.image.toAsset({
//       image: img,
//       description: id,
//       assetId: assetId+'/'+id,
//       region: poi,
//       scale: 30,
//       maxPixels: 1e13
//     })
//   }