{"cells": [{"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["import os\n", "import glob\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["inws = r'C:\\Users\\<USER>\\Desktop\\Water\\Validation\\data'\n", "files = glob.glob(os.path.join(inws, \"*.csv\"))\n", "result = pd.DataFrame()\n", "for file in files:\n", "    df = pd.read_csv(file)\n", "    result = pd.concat([result, df])    \n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["outws = r'C:\\Users\\<USER>\\Desktop\\Water\\Validation'"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["result = result.drop_duplicates()\n", "result.to_csv(outws+'\\\\data.csv')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}