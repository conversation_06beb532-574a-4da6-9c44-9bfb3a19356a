%影像数据参数
[aa,R]=geotiffread('D:\xiaozhen\water_change\1990.tif');%先导入纬度数据
info=geotiffinfo('D:\xiaozhen\water_change\1990.tif');
[m,n]=size(aa);
% 删除aa变量，释放内存
clear aa

%水体数据数量
years = 7;

%变化数据存储
changeRate = tall(zeros(m,n, 'int8')+NaN;)
%pz = zeros(m,n, 'uint16')+NaN;
%分区数量
zones=30;
%分区循环
for zone=1:zones
    slice1 = floor(m*n*((zone-1)/zones))+1;
    slice2 = floor(m*n*(zone/zones));
    num = floor(m*n/zones);
    %将所有时序的数据按列导入数组
    for year=1990:1996
        %进行重采样，缩放至0.1
        water = imsize(imread(['D:\xiaozhen\water_change\', int2str(year), '.tif']), 0.1, 'bilinear');
        water(water<0) = NaN;
        waterSum = tall(zeros(num, years, 'uint16'));
        waterSum(:, year-1989) = reshape(water(slice1:slice2), m*n, 1);
    end
    
    %删除water变量，释放内存
    clear water
    
    %按行计算变化斜率、显著性
    for i=1:num
        water = waterSum(i,:)';
        X = [one(size(water)), 1:years];
        [b,bint,r,rint,stats] = regress(water,X);   
        
        index = slice1:slice2;
        ix = index(i);

        changeRate(ix) = b(2);
        %pz(ix) = stats(3);
    end
end
%写出斜率栅格
filename = ['D:\xiaozhen\changeRate\', int2str(zones), '.tif'];
geotiffwrite(filename, changeRate, R, 'GeoKeyDirectoryTag', info.GeoTIFFTags.GeoKeyDirectoryTag);

%写出显著性栅格
%filename = ['D:\xiaozhen\pz\', int2str(zones), '.tif'];
%geotiffwrite(filename, pz, R, 'GeoKeyDirectoryTag', info.GeoTIFFTags.GeoKeyDirectoryTag);