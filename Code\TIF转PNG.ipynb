{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import glob\n", "import os\n", "from PIL import Image\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["inws = r'C:\\Users\\<USER>\\Desktop\\Water\\Plot\\TIF格式'\n", "files = glob.glob(os.path.join(inws, '*.tif'))"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["outws = r'C:\\Users\\<USER>\\Desktop\\Water\\Plot\\PNG格式'"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mT<PERSON> crashed while executing code in the the current cell or a previous cell. Please review the code in the cell(s) to identify a possible cause of the failure. Click <a href='https://aka.ms/vscodeJupyterKernelCrash'>here</a> for more info. View Jupyter <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["for file in files:\n", "    img = Image.open(file)\n", "    \n", "    name = os.path.basename(file)[:-4]+'.png'\n", "    # print(os.path.join(outws, name))\n", "    img.save(os.path.join(outws, name), format='PNG')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}}, "nbformat": 4, "nbformat_minor": 2}