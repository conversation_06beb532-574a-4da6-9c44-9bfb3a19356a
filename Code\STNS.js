//-----------------------<PERSON><PERSON>ton for filling.-----------
// start_year: the start of year of study period.
// end_year: the end of year of study period.
// num: the radius of nerghborhood pixels.
// year_range: the time range for water frequency and spatial information. year_range = 3 is recommended.
// year_span: the time range for temporal information. year_span = 10 is recommended.
// month_span: the time range for temporal information. month_span = 3 is recommended.
var fill = function (img, waterCol, start_year, end_year, k, num, year_range, kernel, year_span, month_span) {
    var date = ee.Date(img.get('system:time_start'))
    var year = ee.Number(date.get('year'))
    var month = ee.Number(date.get('month'))

    var parallelScale = 1

    // ------------Probability---------
    var filter = ee.Filter.calendarRange({
        start: (year.subtract(k)).max(start_year),
        end: (year.add(k)).min(end_year),
        field: 'year'
    })

    var probability = waterCol.filter(filter)
        .map(function (img) { return img.updateMask(img.neq(-1)) })
        .reduce({ reducer: ee.Reducer.mean(), parallelScale: parallelScale })

    // -----------------Spatial----------------
    var img_neighbor = img.neighborhoodToArray(kernel, -1)

    var filter2 = ee.Filter.calendarRange({
        start: (year.subtract(year_range)).max(start_year),
        end: (year.add(year_range)).min(end_year),
        field: 'year'
    })

    // Gengerate feature from other pixels in different image.
    var to_feature = function (im) {
        var nei = im.neighborhoodToArray(kernel, -1)

        // Get the current status pixel
        var status = im

        var nei_pixels = nei.add(status.multiply(10))

        return nei_pixels
    }
    var feature = waterCol.filter(filter2).map(to_feature)

    var judgeCol_1_func = function (img) {
        // Prior water, other water or non_water
        // 11 -> 1
        var other_11 = img.eq(11)
        // 10 -> 0
        var other_10 = (img.eq(10)).multiply(10).subtract(10)
        // 11 -> 1, 10 -> 0, other values -> -10
        var other_11_10 = other_11.add(other_10)

        // 11->1, 10->0,-10 vs. 1, 0, -1 -> 1, 1, -9
        var judge = other_11_10.eq(img_neighbor.multiply(ee.Image(-9)))

        var mean = judge.arrayReduce(ee.Reducer.mean(), [0, 1])

        return mean.arrayGet(ee.Image(0).addBands(ee.Image(0)))
    }

    var judgeCol_1 = feature.map(judgeCol_1_func)

    var judgeCol_0_func = function (img) {
        // Prior non-water, other non-water.
        // 01 -> 1
        var other_01 = img.eq(1)
        // 00 -> 0
        var other_00 = img.eq(0).multiply(10).subtract(10)
        // 01 -> 1, 00 -> 0, other values -> -10
        var other_01_00 = other_01.add(other_00)

        // 1,0,-10 vs. 1,0,-1
        var judge = other_01_00.eq(img_neighbor.multiply(ee.Image(-9)))

        var mean = judge.arrayReduce(ee.Reducer.mean(), [0, 1])

        return mean.arrayGet(ee.Image(0).addBands(ee.Image(0)))
    }
    var judgeCol_0 = feature.map(judgeCol_0_func)

    //----------------Temporal---------------
    var start_date = date.advance(ee.Number(month_span).multiply(-1), 'month')
    var end_date = date.advance(month_span + 1, 'month')
    var img_near = waterCol.filterDate(start_date, end_date).toBands().toArray()

    var filter3 = ee.Filter.and(
        ee.Filter.calendarRange({
            start: (year.subtract(year_span)).max(start_year),
            end: (year.add(year_span)).min(end_year),
            field: 'year'
        }),
        ee.Filter.calendarRange({
            start: month,
            end: month,
            field: 'month'
        }))

    // Generate feature from other pixels in different year within near months.
    var to_feature_T = function (im) {

        var year_other = ee.Number(ee.Date(im.get('system:time_start')).get('year'))

        var date_other = ee.Date.fromYMD(year_other, month, 1)

        var start_date_other = date_other.advance(ee.Number(month_span).multiply(-1), 'month')
        var end_date_other = date_other.advance(month_span + 1, 'month')

        var other_near = waterCol.filterDate(start_date_other, end_date_other)
            .toBands()
            .toArray()
            .set('start_date', start_date_other)
            .set('end_date', end_date_other)

        // Get the status pixel.
        var status = im

        // ------Attention.
        var near_pixels = other_near.add(status.multiply(10))

        return near_pixels
    }
    var feature_T = waterCol.filter(filter3).map(to_feature_T)

    var judgeCol_1_T_func = function (img) {

        // 11->1, 0
        var other_11 = img.eq(11)

        // 10->1, 0 --> 10, 0 --> 0, -10 
        var other_10 = img.eq(10).multiply(10).subtract(10)

        // 1, -9, -10
        var other_11_10 = other_11.add(other_10)

        // -9(11), 0(10), -10 vs. -9(1), 0(0)
        var judge = other_11_10.eq(img_near.multiply(-9))

        var mean = judge.arrayReduce(ee.Reducer.mean(), [0])

        return mean.arrayGet(ee.Image(0))
    }
    var judgeCol_1_T = feature_T.map(judgeCol_1_T_func)

    var judgeCol_0_T_func = function (img) {
        // 01->1, 0
        var other_01 = img.eq(1)

        // 00->1, 0 --> 10, 0 --> 0, -10
        var other_00 = img.eq(0).multiply(10).subtract(10)

        // -9, 0, -10
        var other_01_00 = other_01.add(other_00)

        // -9(01), 0(00), -10 vs. -9(1), 0(0)
        var judge = other_01_00.eq(img_near.multiply(-9))

        var mean = judge.arrayReduce(ee.Reducer.mean(), [0])

        return mean.arrayGet(ee.Image(0))
    }
    var judgeCol_0_T = feature_T.map(judgeCol_0_T_func)

    // Native Byes Classifier

    // aviod 0.
    var water_other = judgeCol_1.reduce({ reducer: ee.Reducer.mean(), parallelScale: parallelScale }).add(ee.Image(0.001))
    var non_water_other = judgeCol_0.reduce({ reducer: ee.Reducer.mean(), parallelScale: parallelScale }).add(ee.Image(0.001))

    // aviod 0.
    var water_other_T = judgeCol_1_T.reduce({ reducer: ee.Reducer.mean(), parallelScale: parallelScale }).add(ee.Image(0.001))
    var non_water_other_T = judgeCol_0_T.reduce({ reducer: ee.Reducer.mean(), parallelScale: parallelScale }).add(ee.Image(0.001))

    var p_water = (water_other).multiply(water_other_T).multiply(probability)
    var p_non_water = (non_water_other).multiply(non_water_other_T).multiply(ee.Image(1).subtract(probability))

    var fill_image = ee.Image(p_water.gt(p_non_water).copyProperties(img, ['system:time_start']))
    fill_image = fill_image.set('date', year.multiply(100).add(month))

    // maybe not so necessary. it is used beacuse some errors occured in the prior image.
    var filled = img.where(img.eq(-1), fill_image)

    return filled
}

var fill_with_l = function (img, waterCol, start_year, end_year, k, num, year_range, kernel, year_span, month_span) {
    var date = ee.Date(img.get('system:time_start'))
    var year = ee.Number(date.get('year'))
    var month = ee.Number(date.get('month'))

    var parallelScale = 1

    // ------------Probability---------
    var filter = ee.Filter.calendarRange({
        start: (year.subtract(k)).max(start_year),
        end: (year.add(k)).min(end_year),
        field: 'year'
    })

    var probability = waterCol.filter(filter)
        .map(function (img) { return img.updateMask(img.neq(-1)) })
        .reduce({ reducer: ee.Reducer.mean(), parallelScale: parallelScale })

    // -----------------Spatial----------------
    var img_neighbor = img.neighborhoodToArray(kernel, -1)

    var filter2 = ee.Filter.calendarRange({
        start: (year.subtract(year_range)).max(start_year),
        end: (year.add(year_range)).min(end_year),
        field: 'year'
    })

    // Gengerate feature from other pixels in different image.
    var to_feature = function (im) {
        var nei = im.neighborhoodToArray(kernel, -1)

        // Get the current status pixel
        var status = im

        var nei_pixels = nei.add(status.multiply(10))

        return nei_pixels
    }
    var feature = waterCol.filter(filter2).map(to_feature)

    var judgeCol_1_func = function (img) {
        // Prior water, other water or non_water
        // 11 -> 1
        var other_11 = img.eq(11)
        // 10 -> 0
        var other_10 = (img.eq(10)).multiply(10).subtract(10)
        // 11 -> 1, 10 -> 0, other values -> -10
        var other_11_10 = other_11.add(other_10)

        // 11->1, 10->0,-10 vs. 1, 0, -1 -> 1, 1, -9
        var judge = other_11_10.eq(img_neighbor.multiply(ee.Image(-9)))

        var mean = judge.arrayReduce(ee.Reducer.mean(), [0, 1])

        return mean.arrayGet(ee.Image(0).addBands(ee.Image(0)))
    }

    var judgeCol_1 = feature.map(judgeCol_1_func)

    var judgeCol_0_func = function (img) {
        // Prior non-water, other non-water.
        // 01 -> 1
        var other_01 = img.eq(1)
        // 00 -> 0
        var other_00 = img.eq(0).multiply(10).subtract(10)
        // 01 -> 1, 00 -> 0, other values -> -10
        var other_01_00 = other_01.add(other_00)

        // 1,0,-10 vs. 1,0,-1
        var judge = other_01_00.eq(img_neighbor.multiply(ee.Image(-9)))

        var mean = judge.arrayReduce(ee.Reducer.mean(), [0, 1])

        return mean.arrayGet(ee.Image(0).addBands(ee.Image(0)))
    }
    var judgeCol_0 = feature.map(judgeCol_0_func)

    //----------------Temporal---------------
    var start_date = date.advance(ee.Number(month_span).multiply(-1), 'month')
    var end_date = date.advance(month_span + 1, 'month')
    var img_near = waterCol.filterDate(start_date, end_date).toBands().toArray()

    var filter3 = ee.Filter.and(
        ee.Filter.calendarRange({
            start: (year.subtract(year_span)).max(start_year),
            end: (year.add(year_span)).min(end_year),
            field: 'year'
        }),
        ee.Filter.calendarRange({
            start: month,
            end: month,
            field: 'month'
        }))

    // Generate feature from other pixels in different year within near months.
    var to_feature_T = function (im) {

        var year_other = ee.Number(ee.Date(im.get('system:time_start')).get('year'))

        var date_other = ee.Date.fromYMD(year_other, month, 1)

        var start_date_other = date_other.advance(ee.Number(month_span).multiply(-1), 'month')
        var end_date_other = date_other.advance(month_span + 1, 'month')

        var other_near = waterCol.filterDate(start_date_other, end_date_other)
            .toBands()
            .toArray()
            .set('start_date', start_date_other)
            .set('end_date', end_date_other)

        // Get the status pixel.
        var status = im

        // ------Attention.
        var near_pixels = other_near.add(status.multiply(10))

        return near_pixels
    }
    var feature_T = waterCol.filter(filter3).map(to_feature_T)

    var judgeCol_1_T_func = function (img) {

        // 11->1, 0
        var other_11 = img.eq(11)

        // 10->1, 0 --> 10, 0 --> 0, -10 
        var other_10 = img.eq(10).multiply(10).subtract(10)

        // 1, -9, -10
        var other_11_10 = other_11.add(other_10)

        // -9(11), 0(10), -10 vs. -9(1), 0(0)
        var judge = other_11_10.eq(img_near.multiply(-9))

        var mean = judge.arrayReduce(ee.Reducer.mean(), [0])

        return mean.arrayGet(ee.Image(0))
    }
    var judgeCol_1_T = feature_T.map(judgeCol_1_T_func)

    var judgeCol_0_T_func = function (img) {
        // 01->1, 0
        var other_01 = img.eq(1)

        // 00->1, 0 --> 10, 0 --> 0, -10
        var other_00 = img.eq(0).multiply(10).subtract(10)

        // -9, 0, -10
        var other_01_00 = other_01.add(other_00)

        // -9(01), 0(00), -10 vs. -9(1), 0(0)
        var judge = other_01_00.eq(img_near.multiply(-9))

        var mean = judge.arrayReduce(ee.Reducer.mean(), [0])

        return mean.arrayGet(ee.Image(0))
    }
    var judgeCol_0_T = feature_T.map(judgeCol_0_T_func)

    // Native Byes Classifier

    // aviod 0.
    var water_other = judgeCol_1.reduce({ reducer: ee.Reducer.mean(), parallelScale: parallelScale }).add(ee.Image(0.001))
    var non_water_other = judgeCol_0.reduce({ reducer: ee.Reducer.mean(), parallelScale: parallelScale }).add(ee.Image(0.001))

    // aviod 0.
    var water_other_T = judgeCol_1_T.reduce({ reducer: ee.Reducer.mean(), parallelScale: parallelScale }).add(ee.Image(0.001))
    var non_water_other_T = judgeCol_0_T.reduce({ reducer: ee.Reducer.mean(), parallelScale: parallelScale }).add(ee.Image(0.001))

    var p_water = (water_other).multiply(water_other_T).multiply(probability)
    var p_non_water = (non_water_other).multiply(non_water_other_T).multiply(ee.Image(1).subtract(probability))

    var fill_image = ee.Image(p_water.gt(p_non_water).copyProperties(img, ['system:time_start']))
    fill_image = fill_image.set('date', year.multiply(100).add(month))

    // maybe not so necessary. it is used beacuse some errors occured in the prior image.
    var filled = img.where(img.eq(-1), fill_image)

    var L_img = (p_water.subtract(p_non_water)).divide(p_water.add(p_non_water)).multiply(100).int8().rename('L_degree')
    return filled.addBands(L_img)
}

exports = {
    fill: fill,
    fill_with_l: fill_with_l,
}