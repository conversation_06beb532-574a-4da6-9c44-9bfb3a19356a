{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import cv2\n", "import numpy as np\n", "from PIL import Image\n", "\n", "# 定义图像大小\n", "img_size = (512, 512)\n", "\n", "# 创建一个空白图像\n", "img = np.zeros(img_size, dtype=np.uint8)\n", "\n", "# 定义云覆盖的百分比\n", "cloud_cover_percentage = 30\n", "\n", "# 计算需要生成的云覆盖像素数\n", "num_cloud_pixels = int(img_size[0] * img_size[1] * (cloud_cover_percentage / 100.0))\n", "\n", "# 在图像上随机生成云覆盖像素\n", "cloud_pixels = np.random.choice(a=[False, True], size=img_size, p=[1 - cloud_cover_percentage / 100.0, cloud_cover_percentage / 100.0])\n", "\n", "# 将云覆盖像素设置为白色（255）\n", "img[cloud_pixels] = 255\n", "\n", "# 使用 OpenCV 的 GaussianBlur 函数模拟云的模糊效果\n", "blurred_img = cv2.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(img, (13, 13), 0)\n", "\n", "# 将图像保存为 JPEG 文件\n", "cv2.imwrite('cloud_cover.jpg', blurred_img)\n", "\n", "# 显示图像\n", "img = Image.open('cloud_cover.jpg')\n", "img.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}