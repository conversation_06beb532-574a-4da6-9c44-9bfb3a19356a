// Create Landsat 45789 Collection Function.
function createLandsatCollection(params) {
    var defaultParams = {
        region: Map.getBounds(true),
        start: '1982-01-01',
        end: formatDate(new Date()),
        mapImage: function (image) { return image }
    }
    params = mergeObjects([defaultParams, params])

    var filter = ee.Filter.and(
        ee.Filter.bounds(params.region),
        ee.Filter.date(params.start, params.end)
    )
    function applyScaleFactorsL457(image) {
        var opticalBands = image.select('SR_B.').multiply(0.0000275).add(-0.2);
        var thermalBand = image.select('ST_B6').multiply(0.00341802).add(149.0);
        return image.addBands(opticalBands, null, true)
            .addBands(thermalBand, null, true)
    }
    function applyScaleFactorsL89(image) {
        var opticalBands = image.select('SR_B.').multiply(0.0000275).add(-0.2);
        var thermalBands = image.select('ST_B.*').multiply(0.00341802).add(149.0);
        return image.addBands(opticalBands, null, true)
            .addBands(thermalBands, null, true)
    }
    function cloudMask(image) {
        // Bits 3 and 5 are cloud shadow and cloud, respectively.
        var cloudShadowBitMask = (1 << 3);
        var cloudsBitMask = (1 << 4);
        var snowBitMask = (1 << 5)
        // Get the pixel QA band.
        var qa = image.select('QA_PIXEL');
        // Both flags should be set to zero, indicating clear conditions.
        var mask = qa.bitwiseAnd(cloudShadowBitMask).eq(0)
            .and(qa.bitwiseAnd(cloudsBitMask).eq(0))
            .and(qa.bitwiseAnd(snowBitMask).eq(0));
        return image.updateMask(mask).copyProperties(image);
    }

    var l4 = ee.ImageCollection('LANDSAT/LT04/C02/T1_L2')
        .filter(filter)
        .map(applyScaleFactorsL457)
        .map(cloudMask)
        .select(
            ['SR_B1', 'SR_B2', 'SR_B3', 'SR_B4', 'SR_B5'],
            ['blue', 'green', 'red', 'nir', 'swir1']
        )

    var l5 = ee.ImageCollection('LANDSAT/LT05/C02/T1_L2')
        .filter(filter)
        .map(applyScaleFactorsL457)
        .map(cloudMask)
        .select(
            ['SR_B1', 'SR_B2', 'SR_B3', 'SR_B4', 'SR_B5'],
            ['blue', 'green', 'red', 'nir', 'swir1']
        )

    var l7 = ee.ImageCollection('LANDSAT/LE07/C02/T1_L2')
        .filter(filter)
        .map(applyScaleFactorsL457)
        .map(cloudMask)
        .select(
            ['SR_B1', 'SR_B2', 'SR_B3', 'SR_B4', 'SR_B5'],
            ['blue', 'green', 'red', 'nir', 'swir1']
        )

    var l8 = ee.ImageCollection('LANDSAT/LC08/C02/T1_L2')
        .filter(filter)
        .map(applyScaleFactorsL89)
        .map(cloudMask)
        .select(
            ['SR_B2', 'SR_B3', 'SR_B4', 'SR_B5', 'SR_B6'],
            ['blue', 'green', 'red', 'nir', 'swir1']
        )

    var l9 = ee.ImageCollection('LANDSAT/LC09/C02/T1_L2')
        .filter(filter)
        .map(applyScaleFactorsL89)
        .map(cloudMask)
        .select(
            ['SR_B2', 'SR_B3', 'SR_B4', 'SR_B5', 'SR_B6'],
            ['blue', 'green', 'red', 'nir', 'swir1']
        )

    return l4.merge(l5).merge(l7).merge(l8).merge(l9)
        .map(mapImage)
        .sort('system:time_start')

    function mapImage(image) {
        return params.mapImage(image)
            .clip(params.region)
    }

    function formatDate(date) {
        var d = new Date(date),
            month = '' + (d.getMonth() + 1),
            day = '' + d.getDate(),
            year = d.getFullYear()

        if (month.length < 2)
            month = '0' + month
        if (day.length < 2)
            day = '0' + day

        return [year, month, day].join('-')
    }

    function mergeObjects(objects) {
        return objects.reduce(function (acc, o) {
            for (var a in o) { acc[a] = o[a] }
            return acc
        }, {})
    }
}