library(randomForest)
library(caret)
library(GA)
library(rattle)
library(inTrees)
inws1 = 'C:\\Users\\<USER>\\Desktop\\Water\\Validation\\data.csv'
df = read.csv(inws1)

data = subset(df, select=-c(1,3,5,6,17,24,29,30,33))
data = subset(df, select=-c(1,4,10,11,38,45))

data$refer <- ifelse(data$refer==2, "water", 
                    ifelse(data$refer==1, "other", "null"))
data$refer <- ifelse(data$type==2, "water", 
                  ifelse(data$type==10 | data$type==11 | data$type==12, "shadow", 
                          ifelse(data$type==100 | data$type==101 | data$type==102 | data$type==110 | data$type==111 | data$type==112, 'snow', 
                                 ifelse(data$type==1, "other", "null"))))
data = data[data$refer != 'null', ]

set.seed(123)

train_index <- sample(nrow(data), round(0.7 * nrow(data)))

data_train <- data[train_index, ]
data_test <- data[-train_index, ]

library(RRF)
X_train <- data_train[,-c(22,24)]
X_train <- data_train[,-c(43,44,45,47,48)]
X_test <- data_test[,-c(22,24)]
X_test <- data_test[,-c(43,44,45,47,48)]

target_train <- data_train[,"refer"]
target_test <- data_test[,"refer"]
rf <- RRF(X_train,as.factor(target_train),ntree=500) # build an ordinary RF

treeList <- RF2List(rf)
tree_rules  <- extractRules(treeList,X_train, ntree=500, maxdepth = 12, random=TRUE, digits=3)
tree_rules <- data.frame(tree_rules)

target_test <- ifelse(data_test$refer=="water", TRUE, 
                           ifelse(data_test$refer=="shadow" | data_test$refer=='snow' | data_test$refer=='other', FALSE, "null"))
# 3. 定义适应度函数
fitness_function <- function(rule_subset) {
  # 提取选定的规则子集
  selected_rules <- tree_rules[rule_subset, ]
  X <- X_test
  # 应用规则子集对测试数据进行预测
  predictions <- eval(parse(text = selected_rules))
  
  # 计算预测准确率
  accuracy <- mean(predictions == target_test)
  error <- mean(predictions != target_test)
  # 根据准确率和规则数量计算适应度值
  fitness_value <- accuracy - error# - 0.01 * nchar(selected_rules)

  return(fitness_value)
}

# 4. 初始化遗传算法参数
n_rules <- nrow(tree_rules)
population_size <- 500
mutation_rate <- 0.1
crossover_rate <- 0.8

# 5. 运行遗传算法
ga <- ga(type = "real-valued", 
         fitness = fitness_function, 
         lower=1, upper=n_rules,
         nBits = 2, 
         popSize = population_size, 
         pcrossover = crossover_rate, 
         pmutation = mutation_rate,
         maxiter = 100,
         seed = 123)

optimal_rule_subset <- as.logical(ga@solution)
optimal_rules <- data.frame(tree_rules[optimal_rule_subset, ])
final_predictions <- applyRules(optimal_rules, new_data)
summary(ga)
