import arcpy
import glob
import os
from itertools import izip

arcpy.CheckOutExtension("Spatial")

inws = r"C:\Users\<USER>\Desktop\Water\Refer_water\JRC_water"
outws = r"C:\Users\<USER>\Desktop\Water\Refer_water"
base = r"C:\Users\<USER>\Desktop\Water\Refer_water\JRC_water\occurrence_0E_0Nv1_4_2021.tif"
out_coor_system = arcpy.Describe(base).spatialReference
dataType = arcpy.Describe(base).DataType
piexl_type = arcpy.Describe(base).pixelType
cellWidth = arcpy.Describe(base).meanCellWidth
bandCount = arcpy.Describe(base).bandCount

images = glob.glob(os.path.join(inws, "*.tif"))

name = 'JRC_water_occurrence'
print(name)
arcpy.MosaicToNewRaster_management(images, outws, name+'.tif', out_coor_system,
                                       '8_BIT_UNSIGNED', cellWidth, bandCount, "LAST", "FIRST")
