{"cells": [{"cell_type": "code", "execution_count": 1, "id": "e2f6a0be", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\geoai\\Lib\\site-packages\\numpy\\_distributor_init.py:30: UserWarning: loaded more than 1 DLL from .libs:\n", "c:\\Users\\<USER>\\.conda\\envs\\geoai\\Lib\\site-packages\\numpy\\.libs\\libopenblas.FB5AE2TYXYH2IJRDKGDGQ3XBKLKTF43H.gfortran-win_amd64.dll\n", "c:\\Users\\<USER>\\.conda\\envs\\geoai\\Lib\\site-packages\\numpy\\.libs\\libopenblas64__v0.3.21-gcc_10_3_0.dll\n", "  warnings.warn(\"loaded more than 1 DLL from .libs:\"\n", "c:\\Users\\<USER>\\.conda\\envs\\geoai\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py:60: UserWarning: Pandas requires version '1.3.6' or newer of 'bottleneck' (version '1.3.5' currently installed).\n", "  from pandas.core import (\n"]}], "source": ["import matplotlib.pyplot as plt\n", "import pandas as pd\n", "import ast\n", "import numpy as np\n", "import torch\n", "from torch.utils.data import TensorDataset,DataLoader"]}, {"cell_type": "markdown", "id": "02a003fa", "metadata": {}, "source": ["\n", "# 导入样本数据"]}, {"cell_type": "code", "execution_count": 2, "id": "7973c98f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>index</th>\n", "      <th>system:index</th>\n", "      <th>blue</th>\n", "      <th>green</th>\n", "      <th>nir</th>\n", "      <th>red</th>\n", "      <th>swir1</th>\n", "      <th>swir2</th>\n", "      <th>water</th>\n", "      <th>.geo</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>00000000000000000010_0_0</td>\n", "      <td>[[0.11262262612581253, 0.10906070470809937, 0....</td>\n", "      <td>[[0.09050378948450089, 0.0853729173541069, 0.0...</td>\n", "      <td>[[0.13518458604812622, 0.12574565410614014, 0....</td>\n", "      <td>[[0.09225605428218842, 0.08221802115440369, 0....</td>\n", "      <td>[[0.1474815011024475, 0.0964062362909317, 0.08...</td>\n", "      <td>[[0.09772726893424988, 0.0657082349061966, 0.0...</td>\n", "      <td>0</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>00000000000000000010_1_0</td>\n", "      <td>[[0.12951022386550903, 0.12674354016780853, 0....</td>\n", "      <td>[[0.11024990677833557, 0.10886450111865997, 0....</td>\n", "      <td>[[0.2770344913005829, 0.2679743766784668, 0.26...</td>\n", "      <td>[[0.11067773401737213, 0.1076914370059967, 0.1...</td>\n", "      <td>[[0.2476751208305359, 0.24453145265579224, 0.2...</td>\n", "      <td>[[0.12630951404571533, 0.1264660656452179, 0.1...</td>\n", "      <td>0</td>\n", "      <td>{\"type\":\"MultiPoint\",\"coordinates\":[]}</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   index              system:index  \\\n", "0      0  00000000000000000010_0_0   \n", "1      1  00000000000000000010_1_0   \n", "\n", "                                                blue  \\\n", "0  [[0.11262262612581253, 0.10906070470809937, 0....   \n", "1  [[0.12951022386550903, 0.12674354016780853, 0....   \n", "\n", "                                               green  \\\n", "0  [[0.09050378948450089, 0.0853729173541069, 0.0...   \n", "1  [[0.11024990677833557, 0.10886450111865997, 0....   \n", "\n", "                                                 nir  \\\n", "0  [[0.13518458604812622, 0.12574565410614014, 0....   \n", "1  [[0.2770344913005829, 0.2679743766784668, 0.26...   \n", "\n", "                                                 red  \\\n", "0  [[0.09225605428218842, 0.08221802115440369, 0....   \n", "1  [[0.11067773401737213, 0.1076914370059967, 0.1...   \n", "\n", "                                               swir1  \\\n", "0  [[0.1474815011024475, 0.0964062362909317, 0.08...   \n", "1  [[0.2476751208305359, 0.24453145265579224, 0.2...   \n", "\n", "                                               swir2  water  \\\n", "0  [[0.09772726893424988, 0.0657082349061966, 0.0...      0   \n", "1  [[0.12630951404571533, 0.1264660656452179, 0.1...      0   \n", "\n", "                                     .geo  \n", "0  {\"type\":\"MultiPoint\",\"coordinates\":[]}  \n", "1  {\"type\":\"MultiPoint\",\"coordinates\":[]}  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["sampleData = '../training/CNN_samples.csv' \n", "data = pd.read_csv(sampleData)\n", "\n", "for i in np.arange(2, 6):\n", "    sampleData = '../training/CNN_samples_'+str(i)+'.csv'\n", "    temp = pd.read_csv(sampleData)\n", "    data = pd.concat([data, temp])\n", "\n", "data = data.reset_index()\n", "data.head(2)"]}, {"cell_type": "code", "execution_count": 3, "id": "0e376db0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["105207\n", "(105207, 10)\n"]}], "source": ["# 查看一下导入数据的格式，并且删除一些不需要的列数据\n", "print(len(data))\n", "print(data.shape)"]}, {"cell_type": "code", "execution_count": 4, "id": "fd636f6e", "metadata": {}, "outputs": [], "source": ["# 删除system:indexlie\n", "data.drop(columns=['system:index', '.geo'], inplace=True)\n", "\n", "bands = ['blue', 'green', 'nir', 'red', 'swir1', 'swir2', 'water']\n", "\n", "data = data[bands]"]}, {"cell_type": "markdown", "id": "6bba9ed2", "metadata": {}, "source": ["# 读取训练集、验证集"]}, {"cell_type": "markdown", "id": "2bc9f95d", "metadata": {}, "source": ["两倍标准差筛选"]}, {"cell_type": "code", "execution_count": 5, "id": "c2a0651a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_26212\\79233517.py:11: FutureWarning: DataFrame.applymap has been deprecated. Use DataFrame.map instead.\n", "  data_center = data[featureName].applymap(extract_center)\n"]}], "source": ["featureName = list(data.columns.values[:-1])\n", "labelName = ['water']\n", "\n", "def extract_center(element):\n", "    try:\n", "        element = eval(element)\n", "        return element[3][3]\n", "    except:\n", "        return np.nan\n", "\n", "data_center = data[featureName].applymap(extract_center)\n", "mean = data_center.mean(axis=0)\n", "std = data_center.std(axis=0)\n"]}, {"cell_type": "code", "execution_count": 6, "id": "08c9114a", "metadata": {}, "outputs": [], "source": ["data_center = data_center[featureName]"]}, {"cell_type": "code", "execution_count": 20, "id": "a8abc168", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["94067\n", "94067\n"]}], "source": ["print(len(data))\n", "\n", "del_df = data_center.mask(np.abs(data_center - mean) > 2 * std, other=np.nan)\n", "new_column_names = [col + \"_central\" for col in del_df.columns]\n", "del_df.rename(columns=dict(zip(del_df.columns, new_column_names)), inplace=True)\n", "\n", "join_df = data.join(del_df).dropna()[featureName+labelName]\n", "\n", "print(len(join_df))"]}, {"cell_type": "code", "execution_count": 21, "id": "c137b917", "metadata": {}, "outputs": [], "source": ["data = join_df"]}, {"cell_type": "code", "execution_count": 22, "id": "a14ea534", "metadata": {}, "outputs": [], "source": ["# 加一列random，并将其随机赋值为0~100之间\n", "data['random'] = np.random.randint(1, 100, data.shape[0])\n", "data.head(2)\n", "\n", "# 划分数据集\n", "train_data = data[data['random'] <= 70]\n", "val_data = data[data['random'] > 70]"]}, {"cell_type": "code", "execution_count": 23, "id": "ee5bc4f3", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_26212\\2030276273.py:1: FutureWarning: DataFrame.applymap has been deprecated. Use DataFrame.map instead.\n", "  trainData = np.array(train_data[featureName].applymap(ast.literal_eval).values.tolist())\n"]}, {"name": "stdout", "output_type": "stream", "text": ["trainData first element:\n", " tensor([[0.1126, 0.1091, 0.1091, 0.1139, 0.1139, 0.1124, 0.1124],\n", "        [0.1189, 0.1157, 0.1144, 0.1094, 0.1094, 0.1129, 0.1129],\n", "        [0.1173, 0.1187, 0.1207, 0.1177, 0.1169, 0.1187, 0.1187],\n", "        [0.1070, 0.1092, 0.1150, 0.1152, 0.1175, 0.1238, 0.1263],\n", "        [0.0946, 0.0930, 0.0992, 0.1061, 0.1123, 0.1188, 0.1128],\n", "        [0.1004, 0.0985, 0.0960, 0.1016, 0.1078, 0.1076, 0.1076],\n", "        [0.1059, 0.1046, 0.1046, 0.1057, 0.1057, 0.1043, 0.1056]])\n", "trainData_label first element:\n", " tensor([0.])\n"]}], "source": ["trainData = np.array(train_data[featureName].applymap(ast.literal_eval).values.tolist())\n", "trainData = torch.tensor(trainData, dtype=torch.double)\n", "trainData_label = torch.tensor(np.array(train_data[labelName]), dtype=torch.double)\n", "print(\"trainData first element:\\n\",trainData[0,0,:,:])\n", "print(\"trainData_label first element:\\n\",trainData_label[0])"]}, {"cell_type": "code", "execution_count": 25, "id": "156aeddf", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_26212\\899215047.py:1: FutureWarning: DataFrame.applymap has been deprecated. Use DataFrame.map instead.\n", "  valData = np.array(val_data[featureName].applymap(ast.literal_eval).values.tolist())\n"]}, {"name": "stdout", "output_type": "stream", "text": ["valData shape:\n", " <PERSON>.<PERSON><PERSON>([27745, 6, 7, 7])\n", "valData first element:\n", " tensor([[0.1295, 0.1267, 0.1267, 0.1259, 0.1259, 0.1209, 0.1209],\n", "        [0.1238, 0.1221, 0.1221, 0.1238, 0.1238, 0.1218, 0.1218],\n", "        [0.1147, 0.1208, 0.1208, 0.1244, 0.1244, 0.1223, 0.1223],\n", "        [0.1143, 0.1218, 0.1218, 0.1285, 0.1285, 0.1260, 0.1260],\n", "        [0.1199, 0.1245, 0.1245, 0.1248, 0.1248, 0.1278, 0.1278],\n", "        [0.1214, 0.1205, 0.1205, 0.1194, 0.1194, 0.1226, 0.1226],\n", "        [0.1226, 0.1189, 0.1189, 0.1181, 0.1181, 0.1181, 0.1181]])\n", "************************************************************\n", "valData_label first element:\n", " tensor([0.])\n", "valData_label shape:\n", " torch.Si<PERSON>([27745, 1])\n"]}], "source": ["valData = np.array(val_data[featureName].applymap(ast.literal_eval).values.tolist())\n", "valData = torch.tensor(valData, dtype=torch.double)\n", "valData_label = torch.tensor(np.array(val_data[labelName]), dtype=torch.double)\n", "\n", "# 打印数据查看基本信息\n", "print(\"valData shape:\\n\",valData.shape)\n", "print(\"valData first element:\\n\",valData[0,0,:,:])\n", "\n", "print(\"*\"*60)\n", "print(\"valData_label first element:\\n\",valData_label[0])\n", "print(\"valData_label shape:\\n\",valData_label.shape)"]}, {"cell_type": "markdown", "id": "80e6aa81", "metadata": {}, "source": ["# 将数据集转成torch结构并进行组织"]}, {"cell_type": "code", "execution_count": 26, "id": "5b176fa7", "metadata": {}, "outputs": [], "source": ["# 使用TensorDataset构建trainDataset和valDataset\n", "trainDataset = TensorDataset(trainData, trainData_label)\n", "valDataset = TensorDataset(valData, valData_label)\n", "\n", "# 定义batch_size\n", "batch_size = 32\n", "\n", "# 创建 DataLoader\n", "trainLoader = DataLoader(trainDataset, batch_size=batch_size, shuffle=True)\n", "valLoader = DataLoader(valDataset, batch_size=batch_size, shuffle=False)  # 在验证集上一般不需要打乱数据"]}, {"cell_type": "markdown", "id": "2a9d6d4c", "metadata": {}, "source": ["# 定义网络模型"]}, {"cell_type": "code", "execution_count": 27, "id": "55c4885e", "metadata": {}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "import torch.optim as optim\n", "import torch.nn.functional as F\n", "from torch.nn import ReLU, Sigmoid\n", "torch.set_default_dtype(torch.double)"]}, {"cell_type": "code", "execution_count": 28, "id": "f14d9536", "metadata": {}, "outputs": [], "source": ["class myCNNModel(nn.Module):\n", "    def __init__(self, inputChannel, outputChannel):\n", "        super(myCN<PERSON><PERSON><PERSON><PERSON>,self).__init__()\n", "        self.conv1 = nn.Conv2d(in_channels=inputChannel,out_channels=8,kernel_size=(3, 3), padding=0)\n", "        self.conv2 = nn.Conv2d(in_channels=8,out_channels=16,kernel_size=(3, 3), padding=0)\n", "        self.conv3 = nn.Conv2d(in_channels=16,out_channels=32,kernel_size=(3, 3))\n", "        self.conv4 = nn.Conv2d(in_channels=32,out_channels=outputChannel,kernel_size=(1,1))\n", "    \n", "    def forward(self,x):\n", "        x1 = <PERSON>.relu(self.conv1(x))\n", "        x1 = <PERSON><PERSON>relu(self.conv2(x1))\n", "        x1 = <PERSON><PERSON>relu(self.conv3(x1))\n", "        x1 = <PERSON><PERSON>relu(self.conv4(x1))\n", "        return x1"]}, {"cell_type": "code", "execution_count": 42, "id": "7e22bf93", "metadata": {}, "outputs": [], "source": ["# 难以排除积雪、冰川\n", "class myCNNModel(nn.Module):\n", "    def __init__(self, inputChannel, outputChannel):\n", "        super(myCN<PERSON><PERSON><PERSON><PERSON>,self).__init__()\n", "        self.conv1 = nn.Conv2d(in_channels=inputChannel,out_channels=8,kernel_size=(5, 5), padding=0)\n", "        self.pool1 = nn.MaxPool2d(kernel_size=2, stride=1)  # 添加最大池化层\n", "        self.conv2 = nn.Conv2d(in_channels=8,out_channels=16,kernel_size=(2, 2), padding=0)\n", "        self.conv3 = nn.Conv2d(in_channels=16,out_channels=outputChannel,kernel_size=(1,1))\n", "    \n", "    def forward(self,x):\n", "        x1 = <PERSON>.relu(self.conv1(x))\n", "        x1 = self.pool1(x1)\n", "        x1 = <PERSON><PERSON>relu(self.conv2(x1))\n", "        x1 = <PERSON><PERSON>relu(self.conv3(x1))\n", "        return x1"]}, {"cell_type": "code", "execution_count": 29, "id": "482f3fc8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["model structure:\n", " myCNNModel(\n", "  (conv1): Conv2d(6, 8, kernel_size=(3, 3), stride=(1, 1))\n", "  (conv2): Conv2d(8, 16, kernel_size=(3, 3), stride=(1, 1))\n", "  (conv3): Conv2d(16, 32, kernel_size=(3, 3), stride=(1, 1))\n", "  (conv4): Conv2d(32, 2, kernel_size=(1, 1), stride=(1, 1))\n", ")\n"]}], "source": ["# 初始化模型、损失函数和优化器\n", "input_size = 6\n", "output_size = 2\n", "\n", "model = myCNNModel(input_size,output_size)\n", "\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "# model = model.to(device)\n", "\n", "optimizer = torch.optim.Adam(model.parameters(), lr=0.001)  \n", "criterion = nn.CrossEntropyLoss(reduction='mean')  \n", "print(\"model structure:\\n\",model)"]}, {"cell_type": "markdown", "id": "3a6b1d25", "metadata": {}, "source": ["# 测试网络模型输出结果"]}, {"cell_type": "code", "execution_count": 30, "id": "ff710f92", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["testSamplelabel:\n", " tensor([0.])\n", "************************************************************\n", "testOutput:\n", " tensor([[[[0.0000]],\n", "\n", "         [[0.1921]]]], grad_fn=<ReluBackward0>)\n"]}], "source": ["testSample = trainDataset[0][0].unsqueeze(0)\n", "# print(\"testSample:\\n\",testSample)\n", "testSamplelabel = trainDataset[0][1]#.view(-1).long()\n", "print(\"testSamplelabel:\\n\",testSamplelabel)\n", "\n", "testOutput = model(testSample)#.view(-1, 2)\n", "print(\"*\"*60)\n", "print(\"testOutput:\\n\",testOutput)"]}, {"cell_type": "markdown", "id": "6d22ef79", "metadata": {}, "source": ["# 训练及验证"]}, {"cell_type": "code", "execution_count": 31, "id": "199d8808", "metadata": {}, "outputs": [], "source": ["train_losses = []\n", "train_accuracies = []\n", "val_accuracies = []"]}, {"cell_type": "code", "execution_count": 32, "id": "ee59b6b8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.9770409082717607\n", "Epoch 1/50, Train Loss: 0.8796, Train Accuracy: 0.9661, Validation Accuracy: 0.9770\n", "0.9797440980356821\n", "Epoch 2/50, Train Loss: 0.8548, Train Accuracy: 0.9771, Validation Accuracy: 0.9797\n", "0.9800684808073526\n", "Epoch 3/50, Train Loss: 0.8534, Train Accuracy: 0.9780, Validation Accuracy: 0.9801\n", "0.9805009911695801\n", "Epoch 4/50, Train Loss: 0.8509, Train Accuracy: 0.9788, Validation Accuracy: 0.9805\n", "0.9806812038205082\n", "Epoch 5/50, Train Loss: 0.8496, Train Accuracy: 0.9797, Validation Accuracy: 0.9807\n", "Epoch 6/50, Train Loss: 0.8486, Train Accuracy: 0.9801, Validation Accuracy: 0.9751\n", "0.9824112452694179\n", "Epoch 7/50, Train Loss: 0.8478, Train Accuracy: 0.9800, Validation Accuracy: 0.9824\n", "Epoch 8/50, Train Loss: 0.8473, Train Accuracy: 0.9807, Validation Accuracy: 0.9813\n", "Epoch 9/50, Train Loss: 0.8466, Train Accuracy: 0.9809, Validation Accuracy: 0.9805\n", "Epoch 10/50, Train Loss: 0.8471, Train Accuracy: 0.9805, Validation Accuracy: 0.9809\n", "Epoch 11/50, Train Loss: 0.8459, Train Accuracy: 0.9811, Validation Accuracy: 0.9824\n", "Epoch 12/50, Train Loss: 0.8452, Train Accuracy: 0.9811, Validation Accuracy: 0.9822\n", "Epoch 13/50, Train Loss: 0.8450, Train Accuracy: 0.9815, Validation Accuracy: 0.9809\n", "0.982591457920346\n", "Epoch 14/50, Train Loss: 0.8438, Train Accuracy: 0.9817, Validation Accuracy: 0.9826\n", "0.9826995855109029\n", "Epoch 15/50, Train Loss: 0.8440, Train Accuracy: 0.9818, Validation Accuracy: 0.9827\n", "Epoch 16/50, Train Loss: 0.8437, Train Accuracy: 0.9818, Validation Accuracy: 0.9823\n", "Epoch 17/50, Train Loss: 0.8437, Train Accuracy: 0.9817, Validation Accuracy: 0.9816\n", "0.9828797981618309\n", "Epoch 18/50, Train Loss: 0.8436, Train Accuracy: 0.9820, Validation Accuracy: 0.9829\n", "Epoch 19/50, Train Loss: 0.8429, Train Accuracy: 0.9817, Validation Accuracy: 0.9825\n", "Epoch 20/50, Train Loss: 0.8426, Train Accuracy: 0.9820, Validation Accuracy: 0.9819\n", "Epoch 21/50, Train Loss: 0.8423, Train Accuracy: 0.9819, Validation Accuracy: 0.9818\n", "Epoch 22/50, Train Loss: 0.8419, Train Accuracy: 0.9821, Validation Accuracy: 0.9823\n", "Epoch 23/50, Train Loss: 0.8418, Train Accuracy: 0.9821, Validation Accuracy: 0.9828\n", "0.9833123085240584\n", "Epoch 24/50, Train Loss: 0.8415, Train Accuracy: 0.9824, Validation Accuracy: 0.9833\n", "Epoch 25/50, Train Loss: 0.8406, Train Accuracy: 0.9827, Validation Accuracy: 0.9818\n", "Epoch 26/50, Train Loss: 0.8417, Train Accuracy: 0.9821, Validation Accuracy: 0.9807\n", "Epoch 27/50, Train Loss: 0.8403, Train Accuracy: 0.9823, Validation Accuracy: 0.9815\n", "Epoch 28/50, Train Loss: 0.8401, Train Accuracy: 0.9826, Validation Accuracy: 0.9826\n", "Epoch 29/50, Train Loss: 0.8394, Train Accuracy: 0.9828, Validation Accuracy: 0.9826\n", "Epoch 30/50, Train Loss: 0.8387, Train Accuracy: 0.9834, Validation Accuracy: 0.9831\n", "0.9834204361146153\n", "Epoch 31/50, Train Loss: 0.6467, Train Accuracy: 0.9830, Validation Accuracy: 0.9834\n", "0.9835646062353577\n", "Epoch 32/50, Train Loss: 0.1405, Train Accuracy: 0.9837, Validation Accuracy: 0.9836\n", "0.9837808614164715\n", "Epoch 33/50, Train Loss: 0.1366, Train Accuracy: 0.9834, Validation Accuracy: 0.9838\n", "Epoch 34/50, Train Loss: 0.1347, Train Accuracy: 0.9835, Validation Accuracy: 0.9836\n", "Epoch 35/50, Train Loss: 0.1337, Train Accuracy: 0.9837, Validation Accuracy: 0.9833\n", "Epoch 36/50, Train Loss: 0.1321, Train Accuracy: 0.9841, Validation Accuracy: 0.9834\n", "Epoch 37/50, Train Loss: 0.1306, Train Accuracy: 0.9845, Validation Accuracy: 0.9837\n", "Epoch 38/50, Train Loss: 0.1295, Train Accuracy: 0.9843, Validation Accuracy: 0.9833\n", "Epoch 39/50, Train Loss: 0.1290, Train Accuracy: 0.9845, Validation Accuracy: 0.9831\n", "0.9838529464768426\n", "Epoch 40/50, Train Loss: 0.1274, Train Accuracy: 0.9846, Validation Accuracy: 0.9839\n", "Epoch 41/50, Train Loss: 0.1270, Train Accuracy: 0.9847, Validation Accuracy: 0.9830\n", "Epoch 42/50, Train Loss: 0.1255, Train Accuracy: 0.9849, Validation Accuracy: 0.9837\n", "0.9839971165975852\n", "Epoch 43/50, Train Loss: 0.1251, Train Accuracy: 0.9851, Validation Accuracy: 0.9840\n", "Epoch 44/50, Train Loss: 0.1248, Train Accuracy: 0.9851, Validation Accuracy: 0.9837\n", "Epoch 45/50, Train Loss: 0.1227, Train Accuracy: 0.9850, Validation Accuracy: 0.9827\n", "Epoch 46/50, Train Loss: 0.1227, Train Accuracy: 0.9850, Validation Accuracy: 0.9833\n", "Epoch 47/50, Train Loss: 0.1219, Train Accuracy: 0.9852, Validation Accuracy: 0.9830\n", "Epoch 48/50, Train Loss: 0.1204, Train Accuracy: 0.9855, Validation Accuracy: 0.9824\n", "Epoch 49/50, Train Loss: 0.1208, Train Accuracy: 0.9857, Validation Accuracy: 0.9837\n", "Epoch 50/50, Train Loss: 0.1196, Train Accuracy: 0.9854, Validation Accuracy: 0.9833\n"]}], "source": ["numepoch = 50\n", "best_val_metric = float('-inf')\n", "\n", "for epoch in range(numepoch):\n", "    model.train()\n", "    total_loss = 0\n", "    total_correct = 0\n", "    total_samples = 0  \n", "    \n", "    \n", "    for inputs, labels in trainLoader:\n", "        # inputs = inputs.to(device)\n", "        # labels = labels.to(device)\n", "        \n", "        optimizer.zero_grad()\n", "        outputs = model(inputs)\n", "        loss = criterion(outputs.view(-1, output_size), labels.view(-1).long())\n", "        loss.backward()\n", "        optimizer.step()\n", "\n", "        total_loss += loss.item()\n", "        total_correct += (torch.argmax(outputs, dim=1).view(-1) == labels.view(-1)).sum().item()\n", "        total_samples += labels.size(0)\n", "   \n", "    # 在验证集上进行验证\n", "    model.eval()\n", "    with torch.no_grad():\n", "        total_val_loss = 0\n", "        total_val_correct = 0\n", "        num_validation_samples = 0\n", "        \n", "        for inputs, labels in valLoader:\n", "            # inputs = inputs.to(device)\n", "            # labels = labels.to(device)\n", "            \n", "            val_outputs = model(inputs)\n", "            val_loss = criterion(val_outputs.view(-1, output_size), labels.view(-1).long())\n", "            total_val_loss += val_loss.item()\n", "            total_val_correct += (torch.argmax(val_outputs, dim=1).view(-1) == labels.view(-1)).sum().item()\n", "            num_validation_samples += labels.size(0)\n", "\n", "        val_accuracy = total_val_correct / num_validation_samples\n", "        \n", "    # 计算平均损失值和准确率\n", "    avg_loss = total_loss / len(valLoader)\n", "    avg_val_loss = total_val_loss / len(valLoader)\n", "    train_accuracy = total_correct / total_samples\n", "    \n", "    # 将每个epoch的训练和验证集的loss和准确率数据添加到列表中\n", "    train_losses.append(avg_loss)\n", "    train_accuracies.append(train_accuracy)\n", "    val_accuracies.append(val_accuracy)\n", "\n", "\n", "    # 存储最佳模型（根据验证集指标）\n", "    if val_accuracy > best_val_metric:\n", "        best_val_metric = val_accuracy\n", "        print(best_val_metric)\n", "        torch.save(model.state_dict(), 'best_model.pth')\n", "        \n", "    print(f\"Epoch {epoch+1}/{numepoch}, Train Loss: {avg_loss:.4f}, Train Accuracy: {train_accuracy:.4f}, Validation Accuracy: {val_accuracy:.4f}\")"]}, {"cell_type": "code", "execution_count": 33, "id": "86bb41f6", "metadata": {}, "outputs": [{"data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x23b98393410>]"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# plt.plot(train_losses)\n", "plt.plot(val_accuracies)"]}, {"cell_type": "code", "execution_count": 34, "id": "760c1875", "metadata": {}, "outputs": [], "source": ["path_trained_model = 'best_model.pth'\n", "model = myCNNModel(input_size,output_size)\n", "model.load_state_dict(torch.load(path_trained_model))\n", "model.eval()\n", "\n", "weights_and_biases = {}\n", "\n", "for layer_name, params in model.named_parameters():\n", "    weights_and_biases[layer_name] = params.detach().numpy().tolist()"]}, {"cell_type": "code", "execution_count": 35, "id": "c9ad905d", "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['conv1.weight', 'conv1.bias', 'conv2.weight', 'conv2.bias', 'conv3.weight', 'conv3.bias', 'conv4.weight', 'conv4.bias'])"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["weights_and_biases.keys()"]}, {"cell_type": "code", "execution_count": 43, "id": "9bbd75e3", "metadata": {}, "outputs": [], "source": ["import pyperclip\n", "output_value = str(weights_and_biases['conv1.weight'])\n", "pyperclip.copy(output_value)\n"]}, {"cell_type": "code", "execution_count": 47, "id": "b2142568", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[[[0.9412034790746073]], [[0.22629065703159568]], [[-0.08578444846446077]], [[0.09279582183925551]], [[0.04353019657817773]], [[0.15847815525309022]], [[0.0402108511484255]], [[-0.13835608554422063]], [[-0.480795547007583]], [[0.029415918601008614]], [[0.20307402324805543]], [[0.1586402861554564]], [[0.32941571600633224]], [[0.137039345446548]], [[-0.5019161223477115]], [[-0.44738619855966644]], [[-0.2959512307106996]], [[-0.19452094283410343]], [[-0.3959525564028332]], [[-0.07082611377328266]], [[0.2526287324189286]], [[0.041582643735694674]], [[0.033534214832437244]], [[0.05452842477795054]], [[0.1933915336423645]], [[-0.21653995875891197]], [[0.1138314722589578]], [[-0.07109525558771257]], [[-0.08946962486369589]], [[-0.34602343475455427]], [[-0.09821889430466071]], [[-0.30993715288827717]], [[0.02535265926817055]], [[-0.485091344030589]], [[0.19775733819584893]], [[-0.01670369229420924]], [[0.25340057041950514]], [[0.20799081026480712]], [[-0.3502830522865003]], [[-0.44260142756515236]], [[0.19279173402801653]], [[-0.3321251422516283]], [[0.18045538632619346]], [[-0.22583214993956946]], [[-0.06567761495021429]], [[0.053857942469277165]], [[-0.04992799409026303]], [[0.05162753462638646]], [[0.0334046800959294]], [[-0.5099263585469411]], [[0.2958983350068946]], [[0.12723995787752185]], [[-0.66470340985674]], [[-0.07596651692892419]], [[0.06956099956661253]], [[-0.16828965077032448]], [[0.21078563077907125]], [[0.2067422715595373]], [[-0.24355572459644254]], [[0.12858622017715843]], [[0.013879981618684997]], [[-0.021662590518174953]], [[-0.04802257482465305]], [[-0.5634040129361397]]], [[[-0.7622143905637384]], [[-0.15370226881795088]], [[-0.032504019885410934]], [[0.18335152330359755]], [[-0.008960798843002878]], [[-0.32206709572671033]], [[-0.007976196424977206]], [[0.22784008342438805]], [[0.2124228165690736]], [[0.06161202254078165]], [[-0.5451049655428782]], [[-0.00997638590465367]], [[-0.3619823077553419]], [[0.178041584594517]], [[0.24865274710741275]], [[0.21920245954878284]], [[0.26205328216180657]], [[0.23508481048173085]], [[0.17941341520927864]], [[0.09750060856554271]], [[-0.5035408139684991]], [[0.1464752915254335]], [[-0.0011870370423874743]], [[-0.1059506138094836]], [[0.15721506101972701]], [[0.3108024036919612]], [[-0.05477080368144811]], [[-0.06438014479547781]], [[-0.01507365382124404]], [[0.3015102378016395]], [[-0.17494810626518242]], [[0.29441365803028546]], [[0.04440879860160495]], [[0.6825211463993242]], [[0.09036434407441805]], [[-0.010827640978565143]], [[-0.26481590502448676]], [[-0.6669186111662846]], [[0.4034486163028743]], [[0.31184711940315935]], [[0.1774201004807869]], [[0.15879171531627256]], [[-0.059381395288341035]], [[0.6430943078311632]], [[-0.062234418908161025]], [[-0.041868254397686634]], [[0.15448311163845713]], [[-0.06732299256082482]], [[0.05072582743096718]], [[0.4375832499836981]], [[-0.5667782843570354]], [[0.1341157876494965]], [[0.7817136086201756]], [[0.2271342456885605]], [[-0.007574917312857048]], [[0.12981737341285193]], [[-0.18384476703408356]], [[-0.022891707540570964]], [[0.22623892037039978]], [[0.15312372069612895]], [[-0.08098226081700893]], [[0.009155945776085085]], [[-0.07116959675920648]], [[0.6471620166091793]]]]\n"]}], "source": ["print(weights_and_biases['conv4.weight'])"]}, {"cell_type": "code", "execution_count": null, "id": "13940161", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "geoai", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}