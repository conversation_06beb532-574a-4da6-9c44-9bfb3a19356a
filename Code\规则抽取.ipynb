{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "invalid syntax (<string>, line 1)", "output_type": "error", "traceback": ["Traceback \u001b[1;36m(most recent call last)\u001b[0m:\n", "  File \u001b[0;32mc:\\ProgramData\\Anaconda3\\lib\\site-packages\\IPython\\core\\interactiveshell.py:3369\u001b[0m in \u001b[0;35mrun_code\u001b[0m\n    exec(code_obj, self.user_global_ns, self.user_ns)\n", "  Input \u001b[0;32mIn [2]\u001b[0m in \u001b[0;35m<cell line: 39>\u001b[0m\n    fitness_scores = [fitness_function(rule) for rule in population]\n", "  Input \u001b[0;32mIn [2]\u001b[0m in \u001b[0;35m<listcomp>\u001b[0m\n    fitness_scores = [fitness_function(rule) for rule in population]\n", "  Input \u001b[0;32mIn [2]\u001b[0m in \u001b[0;35mfitness_function\u001b[0m\n    pred_labels = [eval(rule.replace('<=', '<=').replace('>', '>=')) for data in X]\n", "\u001b[1;36m  Input \u001b[1;32mIn [2]\u001b[1;36m in \u001b[1;35m<listcomp>\u001b[1;36m\u001b[0m\n\u001b[1;33m    pred_labels = [eval(rule.replace('<=', '<=').replace('>', '>=')) for data in X]\u001b[0m\n", "\u001b[1;36m  File \u001b[1;32m<string>:1\u001b[1;36m\u001b[0m\n\u001b[1;33m    |   |--- class: 1.0\u001b[0m\n\u001b[1;37m    ^\u001b[0m\n\u001b[1;31mSyntaxError\u001b[0m\u001b[1;31m:\u001b[0m invalid syntax\n"]}], "source": ["import numpy as np\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.metrics import accuracy_score\n", "from sklearn.tree import export_text\n", "from sklearn.utils import check_random_state\n", "import random\n", "\n", "# 示例数据集\n", "X = [[0, 0], [1, 1], [2, 2], [3, 3]]\n", "y = [0, 1, 2, 3]\n", "\n", "# 随机森林模型训练\n", "rf = RandomForestClassifier(n_estimators=10, random_state=check_random_state(0))\n", "rf.fit(X, y)\n", "\n", "# 提取随机森林中的规则\n", "rules = []\n", "for tree in rf.estimators_:\n", "    tree_rules = export_text(tree, feature_names=['feature1', 'feature2']).split('\\n')\n", "    rules.extend(tree_rules)\n", "\n", "# 规则提取的目标函数，这里使用准确率作为适应度评估\n", "def fitness_function(rule):\n", "    pred_labels = [eval(rule.replace('<=', '<=').replace('>', '>=')) for data in X]\n", "    return accuracy_score(y, pred_labels)\n", "\n", "# 遗传算法参数设置\n", "population_size = 20\n", "generations = 50\n", "mutation_rate = 0.1\n", "\n", "# 初始化种群\n", "population = []\n", "for _ in range(population_size):\n", "    rule = random.choice(rules)\n", "    population.append(rule)\n", "\n", "# 遗传算法主循环\n", "for generation in range(generations):\n", "    # 评估种群中每个个体的适应度\n", "    fitness_scores = [fitness_function(rule) for rule in population]\n", "    \n", "    # 选择操作\n", "    selected = random.choices(population, weights=fitness_scores, k=population_size)\n", "    \n", "    # 交叉操作\n", "    offspring = []\n", "    for i in range(0, population_size, 2):\n", "        parent1 = selected[i]\n", "        parent2 = selected[i+1]\n", "        cross_point = random.randint(1, len(parent1) - 1)\n", "        child1 = parent1[:cross_point] + parent2[cross_point:]\n", "        child2 = parent2[:cross_point] + parent1[cross_point:]\n", "        offspring.extend([child1, child2])\n", "    \n", "    # 变异操作\n", "    for i in range(population_size):\n", "        if random.random() < mutation_rate:\n", "            mutation_point = random.randint(0, len(offspring[i]) - 1)\n", "            mutated_rule = offspring[i][:mutation_point] + random.choice(rules)[mutation_point:]\n", "            offspring[i] = mutated_rule\n", "    \n", "    # 更新种群\n", "    population = offspring\n", "\n", "# 选择适应度最高的规则\n", "best_rule = max(population, key=fitness_function)\n", "best_fitness = fitness_function(best_rule)\n", "\n", "print(\"Best Rule:\", best_rule)\n", "print(\"Best Fitness:\", best_fitness)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "invalid syntax (<string>, line 1)", "output_type": "error", "traceback": ["Traceback \u001b[1;36m(most recent call last)\u001b[0m:\n", "  File \u001b[0;32mc:\\ProgramData\\Anaconda3\\lib\\site-packages\\IPython\\core\\interactiveshell.py:3369\u001b[0m in \u001b[0;35mrun_code\u001b[0m\n    exec(code_obj, self.user_global_ns, self.user_ns)\n", "  Input \u001b[0;32mIn [3]\u001b[0m in \u001b[0;35m<cell line: 37>\u001b[0m\n    fitness_scores = [fitness_function(rule) for rule in population]\n", "  Input \u001b[0;32mIn [3]\u001b[0m in \u001b[0;35m<listcomp>\u001b[0m\n    fitness_scores = [fitness_function(rule) for rule in population]\n", "  Input \u001b[0;32mIn [3]\u001b[0m in \u001b[0;35mfitness_function\u001b[0m\n    coverage = sum([1 for data in X if eval(rule.replace('<=', '<=').replace('>', '>='))])\n", "\u001b[1;36m  Input \u001b[1;32mIn [3]\u001b[1;36m in \u001b[1;35m<listcomp>\u001b[1;36m\u001b[0m\n\u001b[1;33m    coverage = sum([1 for data in X if eval(rule.replace('<=', '<=').replace('>', '>='))])\u001b[0m\n", "\u001b[1;36m  File \u001b[1;32m<string>:1\u001b[1;36m\u001b[0m\n\u001b[1;33m    |   |   |   |--- class: 2.0\u001b[0m\n\u001b[1;37m    ^\u001b[0m\n\u001b[1;31mSyntaxError\u001b[0m\u001b[1;31m:\u001b[0m invalid syntax\n"]}], "source": ["import numpy as np\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.tree import export_text\n", "from sklearn.utils import check_random_state\n", "\n", "# 示例数据集\n", "X = [[0, 0], [1, 1], [2, 2], [3, 3]]\n", "y = [0, 1, 2, 3]\n", "\n", "# 随机森林模型训练\n", "rf = RandomForestClassifier(n_estimators=10, random_state=check_random_state(0))\n", "rf.fit(X, y)\n", "\n", "# 提取随机森林中的规则\n", "rules = []\n", "for tree in rf.estimators_:\n", "    tree_rules = export_text(tree, feature_names=['feature1', 'feature2']).split('\\n')\n", "    rules.extend(tree_rules)\n", "\n", "# 规则提取的目标函数，这里使用简单的覆盖率作为适应度评估\n", "def fitness_function(rule):\n", "    coverage = sum([1 for data in X if eval(rule.replace('<=', '<=').replace('>', '>='))])\n", "    return coverage / len(X)\n", "\n", "# 遗传算法参数设置\n", "population_size = 20\n", "generations = 50\n", "mutation_rate = 0.1\n", "\n", "# 初始化种群\n", "population = []\n", "for _ in range(population_size):\n", "    rule = random.choice(rules)\n", "    population.append(rule)\n", "\n", "# 遗传算法主循环\n", "for generation in range(generations):\n", "    # 评估种群中每个个体的适应度\n", "    fitness_scores = [fitness_function(rule) for rule in population]\n", "    \n", "    # 选择操作\n", "    selected = random.choices(population, weights=fitness_scores, k=population_size)\n", "    \n", "    # 交叉操作\n", "    offspring = []\n", "    for i in range(0, population_size, 2):\n", "        parent1 = selected[i]\n", "        parent2 = selected[i+1]\n", "        cross_point = random.randint(1, len(parent1) - 1)\n", "        child1 = parent1[:cross_point] + parent2[cross_point:]\n", "        child2 = parent2[:cross_point] + parent1[cross_point:]\n", "        offspring.extend([child1, child2])\n", "    \n", "    # 变异操作\n", "    for i in range(population_size):\n", "        if random.random() < mutation_rate:\n", "            mutation_point = random.randint(0, len(offspring[i]) - 1)\n", "            mutated_rule = offspring[i][:mutation_point] + random.choice(rules)[mutation_point:]\n", "            offspring[i] = mutated_rule\n", "    \n", "    # 更新种群\n", "    population = offspring\n", "\n", "# 选择适应度最高的规则\n", "best_rule = max(population, key=fitness_function)\n", "best_fitness = fitness_function(best_rule)\n", "\n", "print(\"Best Rule:\", best_rule)\n", "print(\"Best Fitness:\", best_fitness)\n"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\deap\\creator.py:138: RuntimeWarning: A class named 'FitnessMax' has already been created and it will be overwritten. Consider deleting previous creation of that class or rename it.\n", "  warnings.warn(\"A class named '{0}' has already been created and it \"\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\deap\\creator.py:138: RuntimeWarning: A class named 'Individual' has already been created and it will be overwritten. Consider deleting previous creation of that class or rename it.\n", "  warnings.warn(\"A class named '{0}' has already been created and it \"\n"]}, {"ename": "TypeError", "evalue": "__cinit__() takes exactly 3 positional arguments (0 given)", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mTypeError\u001b[0m                                 Trace<PERSON> (most recent call last)", "\u001b[1;32mc:\\Users\\<USER>\\Desktop\\Water\\Code\\规则抽取.ipynb Cell 3\u001b[0m in \u001b[0;36m<cell line: 58>\u001b[1;34m()\u001b[0m\n\u001b[0;32m     <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E8%A7%84%E5%88%99%E6%8A%BD%E5%8F%96.ipynb#W4sZmlsZQ%3D%3D?line=58'>59</a>\u001b[0m offspring \u001b[39m=\u001b[39m algorithms\u001b[39m.\u001b[39mvarAnd(population, toolbox, CXPB, MUTPB)\n\u001b[0;32m     <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E8%A7%84%E5%88%99%E6%8A%BD%E5%8F%96.ipynb#W4sZmlsZQ%3D%3D?line=59'>60</a>\u001b[0m fits \u001b[39m=\u001b[39m toolbox\u001b[39m.\u001b[39mmap(toolbox\u001b[39m.\u001b[39mevaluate, offspring)\n\u001b[1;32m---> <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E8%A7%84%E5%88%99%E6%8A%BD%E5%8F%96.ipynb#W4sZmlsZQ%3D%3D?line=60'>61</a>\u001b[0m \u001b[39mfor\u001b[39;00m fit, ind \u001b[39min\u001b[39;00m \u001b[39mzip\u001b[39m(fits, offspring):\n\u001b[0;32m     <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E8%A7%84%E5%88%99%E6%8A%BD%E5%8F%96.ipynb#W4sZmlsZQ%3D%3D?line=61'>62</a>\u001b[0m     ind\u001b[39m.\u001b[39mfitness\u001b[39m.\u001b[39mvalues \u001b[39m=\u001b[39m fit\n\u001b[0;32m     <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E8%A7%84%E5%88%99%E6%8A%BD%E5%8F%96.ipynb#W4sZmlsZQ%3D%3D?line=62'>63</a>\u001b[0m population \u001b[39m=\u001b[39m toolbox\u001b[39m.\u001b[39mselect(offspring, k\u001b[39m=\u001b[39m\u001b[39mlen\u001b[39m(population))\n", "\u001b[1;32mc:\\Users\\<USER>\\Desktop\\Water\\Code\\规则抽取.ipynb Cell 3\u001b[0m in \u001b[0;36mfitness\u001b[1;34m(individual, rules)\u001b[0m\n\u001b[0;32m     <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E8%A7%84%E5%88%99%E6%8A%BD%E5%8F%96.ipynb#W4sZmlsZQ%3D%3D?line=25'>26</a>\u001b[0m \u001b[39mif\u001b[39;00m \u001b[39mnot\u001b[39;00m selected_rules:\n\u001b[0;32m     <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E8%A7%84%E5%88%99%E6%8A%BD%E5%8F%96.ipynb#W4sZmlsZQ%3D%3D?line=26'>27</a>\u001b[0m     \u001b[39mreturn\u001b[39;00m \u001b[39m0\u001b[39m,\n\u001b[1;32m---> <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E8%A7%84%E5%88%99%E6%8A%BD%E5%8F%96.ipynb#W4sZmlsZQ%3D%3D?line=28'>29</a>\u001b[0m ensemble \u001b[39m=\u001b[39m selected_rules[\u001b[39m0\u001b[39;49m]\u001b[39m.\u001b[39;49m\u001b[39m__class__\u001b[39;49m()\n\u001b[0;32m     <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E8%A7%84%E5%88%99%E6%8A%BD%E5%8F%96.ipynb#W4sZmlsZQ%3D%3D?line=29'>30</a>\u001b[0m \u001b[39mfor\u001b[39;00m rule \u001b[39min\u001b[39;00m selected_rules:\n\u001b[0;32m     <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E8%A7%84%E5%88%99%E6%8A%BD%E5%8F%96.ipynb#W4sZmlsZQ%3D%3D?line=30'>31</a>\u001b[0m     ensemble \u001b[39m=\u001b[39m ensemble \u001b[39m+\u001b[39m rule\n", "File \u001b[1;32msklearn\\tree\\_tree.pyx:588\u001b[0m, in \u001b[0;36msklearn.tree._tree.Tree.__cinit__\u001b[1;34m()\u001b[0m\n", "\u001b[1;31mTypeError\u001b[0m: __cinit__() takes exactly 3 positional arguments (0 given)"]}], "source": ["import numpy as np\n", "from sklearn.datasets import load_iris\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import accuracy_score\n", "from deap import base, creator, tools, algorithms\n", "import random\n", "\n", "# 加载 Iris 数据集\n", "iris = load_iris()\n", "X_train, X_test, y_train, y_test = train_test_split(iris.data, iris.target, test_size=0.3, random_state=42)\n", "\n", "# 训练随机森林\n", "rf = RandomForestClassifier(n_estimators=100)\n", "rf.fit(X_train, y_train)\n", "\n", "# 获取随机森林的决策规则\n", "rules = []\n", "\n", "for tree in rf.estimators_:\n", "    rules.append(tree.tree_)\n", "\n", "# 适应度函数\n", "def fitness(individual, rules):\n", "    selected_rules = [rule for rule, selected in zip(rules, individual) if selected]\n", "    if not selected_rules:\n", "        return 0,\n", "\n", "    ensemble = selected_rules[0].__class__()\n", "    for rule in selected_rules:\n", "        ensemble = ensemble + rule\n", "    ensemble = ensemble / len(selected_rules)\n", "    \n", "    y_pred = ensemble.predict(X_test)\n", "\n", "    return accuracy_score(y_test, y_pred),\n", "\n", "# 定义遗传算法\n", "creator.create(\"FitnessMax\", base.Fitness, weights=(1.0,))\n", "creator.create(\"Individual\", list, fitness=creator.FitnessMax)\n", "\n", "toolbox = base.Toolbox()\n", "toolbox.register(\"attr_bool\", random.randint, 0, 1)\n", "toolbox.register(\"individual\", tools.init<PERSON><PERSON><PERSON>, creator.Individual, toolbox.attr_bool, len(rules))\n", "toolbox.register(\"population\", tools.initRepeat, list, toolbox.individual)\n", "\n", "toolbox.register(\"evaluate\", fitness, rules=rules)\n", "toolbox.register(\"mate\", tools.cxTwoPoint)\n", "toolbox.register(\"mutate\", tools.mutFlipBit, indpb=0.1)\n", "toolbox.register(\"select\", tools.selBest)\n", "\n", "# 运行遗传算法\n", "population = toolbox.population(n=50)\n", "NGEN = 50\n", "CXPB = 0.8\n", "MUTPB = 0.1\n", "\n", "for gen in range(NGEN):\n", "    offspring = algorithms.varAnd(population, toolbox, CXPB, MUTPB)\n", "    fits = toolbox.map(toolbox.evaluate, offspring)\n", "    for fit, ind in zip(fits, offspring):\n", "        ind.fitness.values = fit\n", "    population = toolbox.select(offspring, k=len(population))\n", "\n", "top_individual = tools.selBest(population, k=1)[0]\n", "selected_rules = [rule for rule, selected in zip(rules, top_individual) if selected]\n", "\n", "# 输出结果\n", "print(f\"选出的决策规则数量：{len(selected_rules)} / {len(rules)}\")\n", "y_pred = rf.predict(X_test)\n", "print(f\"随机森林的准确率：{accuracy_score(y_test, y_pred)}\")\n", "\n", "simplified_ensemble = sum(selected_rules) / len(selected_rules)\n", "y_pred_simplified = simplified_ensemble.predict(X_test)\n", "print(f\"简化后的模型准确率：{accuracy_score(y_test, y_pred_simplified)}\")"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'select_best' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[1;32mc:\\Users\\<USER>\\Desktop\\Water\\Code\\规则抽取.ipynb Cell 4\u001b[0m in \u001b[0;36m<cell line: 35>\u001b[1;34m()\u001b[0m\n\u001b[0;32m     <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E8%A7%84%E5%88%99%E6%8A%BD%E5%8F%96.ipynb#W3sZmlsZQ%3D%3D?line=49'>50</a>\u001b[0m             chrom[pos] \u001b[39m=\u001b[39m random\u001b[39m.\u001b[39mchoice([\u001b[39m0\u001b[39m,\u001b[39m1\u001b[39m]) \n\u001b[0;32m     <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E8%A7%84%E5%88%99%E6%8A%BD%E5%8F%96.ipynb#W3sZmlsZQ%3D%3D?line=51'>52</a>\u001b[0m     \u001b[39m# 选择最好的npop个个体       \u001b[39;00m\n\u001b[1;32m---> <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E8%A7%84%E5%88%99%E6%8A%BD%E5%8F%96.ipynb#W3sZmlsZQ%3D%3D?line=52'>53</a>\u001b[0m     population \u001b[39m=\u001b[39m select_best(population \u001b[39m+\u001b[39m children, npop)\n\u001b[0;32m     <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E8%A7%84%E5%88%99%E6%8A%BD%E5%8F%96.ipynb#W3sZmlsZQ%3D%3D?line=54'>55</a>\u001b[0m \u001b[39m# 从种群中选出最简单的规则集  \u001b[39;00m\n\u001b[0;32m     <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E8%A7%84%E5%88%99%E6%8A%BD%E5%8F%96.ipynb#W3sZmlsZQ%3D%3D?line=55'>56</a>\u001b[0m simple_rules \u001b[39m=\u001b[39m []    \n", "\u001b[1;31mNameError\u001b[0m: name 'select_best' is not defined"]}], "source": ["import numpy as np \n", "import random\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.tree import export_text\n", "# 加载 Iris 数据集\n", "iris = load_iris()\n", "X_train, X_test, y_train, y_test = train_test_split(iris.data, iris.target, test_size=0.3, random_state=42)\n", "\n", "# 训练随机森林\n", "rf = RandomForestClassifier(n_estimators=100)\n", "rf.fit(X_train, y_train)\n", "\n", "# 获取所有决策树\n", "# trees = rf.estimators_   \n", "\n", "# 获得决策树中的所有规则\n", "rules = []\n", "for tree in rf.estimators_:\n", "    rules.append(tree.tree_)\n", "\n", "# 编码规则,每个规则是一个基因\n", "population = []\n", "for rule in rules:\n", "    # 规则的特征索引,阈值,方向(<=或>)\n", "    gene = [rule.feature, rule.threshold] \n", "    population.append(gene)\n", "\n", "# 遗传算法参数    \n", "ngen = 100      # 迭代次数\n", "npop = 50       # 种群大小 \n", "pcross = 0.8    # 交叉概率\n", "pmute = 0.1     # 变异概率\n", "\n", "# 运行遗传算法   \n", "for gen in range(ngen):\n", "    # 交叉配对,产生子代\n", "    children = []\n", "    for _ in range(npop):\n", "        father = random.choice(population)\n", "        mother = random.choice(population)\n", "        child = father[:]\n", "        cross_pos = random.randint(1, len(father)-1)\n", "        child[cross_pos:] = mother[cross_pos:]\n", "        children.append(child)\n", "        \n", "    # 变异\n", "    for chrom in children:\n", "        if random.random() < pmute:\n", "            pos = random.randint(0, len(chrom)-1)\n", "            chrom[pos] = random.choice([0,1]) \n", "            \n", "    # 选择最好的npop个个体       \n", "    population = select_best(population + children, npop)\n", "\n", "# 从种群中选出最简单的规则集  \n", "simple_rules = []    \n", "for rule in population: \n", "    if len(simple_rules) < 5: # 选择5条规则\n", "        simple_rules.append(rule)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "object of type 'numpy.int32' has no len()", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mTypeError\u001b[0m                                 Trace<PERSON> (most recent call last)", "\u001b[1;32mc:\\Users\\<USER>\\Desktop\\Water\\Code\\规则抽取.ipynb Cell 6\u001b[0m in \u001b[0;36m<cell line: 66>\u001b[1;34m()\u001b[0m\n\u001b[0;32m     <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E8%A7%84%E5%88%99%E6%8A%BD%E5%8F%96.ipynb#X13sZmlsZQ%3D%3D?line=67'>68</a>\u001b[0m     parents \u001b[39m=\u001b[39m np\u001b[39m.\u001b[39marray([select_parents(population, fitness_scores) \u001b[39mfor\u001b[39;00m i \u001b[39min\u001b[39;00m \u001b[39mrange\u001b[39m(population_size \u001b[39m/\u001b[39m\u001b[39m/\u001b[39m \u001b[39m2\u001b[39m)])\n\u001b[0;32m     <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E8%A7%84%E5%88%99%E6%8A%BD%E5%8F%96.ipynb#X13sZmlsZQ%3D%3D?line=68'>69</a>\u001b[0m     children \u001b[39m=\u001b[39m np\u001b[39m.\u001b[39marray([crossover(p) \u001b[39mfor\u001b[39;00m p \u001b[39min\u001b[39;00m parents])\n\u001b[1;32m---> <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E8%A7%84%E5%88%99%E6%8A%BD%E5%8F%96.ipynb#X13sZmlsZQ%3D%3D?line=69'>70</a>\u001b[0m     mutated_children \u001b[39m=\u001b[39m np\u001b[39m.\u001b[39marray([mutate(c) \u001b[39mfor\u001b[39;00m c \u001b[39min\u001b[39;00m children\u001b[39m.\u001b[39mflatten()])\u001b[39m.\u001b[39mreshape(children\u001b[39m.\u001b[39mshape)\n\u001b[0;32m     <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E8%A7%84%E5%88%99%E6%8A%BD%E5%8F%96.ipynb#X13sZmlsZQ%3D%3D?line=70'>71</a>\u001b[0m     population \u001b[39m=\u001b[39m np\u001b[39m.\u001b[39mconcatenate([parents\u001b[39m.\u001b[39mflatten(), mutated_children\u001b[39m.\u001b[39mflatten()])\u001b[39m.\u001b[39mreshape((population_size, X_train\u001b[39m.\u001b[39mshape[\u001b[39m1\u001b[39m]))\n\u001b[0;32m     <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E8%A7%84%E5%88%99%E6%8A%BD%E5%8F%96.ipynb#X13sZmlsZQ%3D%3D?line=72'>73</a>\u001b[0m \u001b[39m# 选择最优个体并提取规则\u001b[39;00m\n", "\u001b[1;32mc:\\Users\\<USER>\\Desktop\\Water\\Code\\规则抽取.ipynb Cell 6\u001b[0m in \u001b[0;36m<listcomp>\u001b[1;34m(.0)\u001b[0m\n\u001b[0;32m     <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E8%A7%84%E5%88%99%E6%8A%BD%E5%8F%96.ipynb#X13sZmlsZQ%3D%3D?line=67'>68</a>\u001b[0m     parents \u001b[39m=\u001b[39m np\u001b[39m.\u001b[39marray([select_parents(population, fitness_scores) \u001b[39mfor\u001b[39;00m i \u001b[39min\u001b[39;00m \u001b[39mrange\u001b[39m(population_size \u001b[39m/\u001b[39m\u001b[39m/\u001b[39m \u001b[39m2\u001b[39m)])\n\u001b[0;32m     <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E8%A7%84%E5%88%99%E6%8A%BD%E5%8F%96.ipynb#X13sZmlsZQ%3D%3D?line=68'>69</a>\u001b[0m     children \u001b[39m=\u001b[39m np\u001b[39m.\u001b[39marray([crossover(p) \u001b[39mfor\u001b[39;00m p \u001b[39min\u001b[39;00m parents])\n\u001b[1;32m---> <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E8%A7%84%E5%88%99%E6%8A%BD%E5%8F%96.ipynb#X13sZmlsZQ%3D%3D?line=69'>70</a>\u001b[0m     mutated_children \u001b[39m=\u001b[39m np\u001b[39m.\u001b[39marray([mutate(c) \u001b[39mfor\u001b[39;00m c \u001b[39min\u001b[39;00m children\u001b[39m.\u001b[39mflatten()])\u001b[39m.\u001b[39mreshape(children\u001b[39m.\u001b[39mshape)\n\u001b[0;32m     <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E8%A7%84%E5%88%99%E6%8A%BD%E5%8F%96.ipynb#X13sZmlsZQ%3D%3D?line=70'>71</a>\u001b[0m     population \u001b[39m=\u001b[39m np\u001b[39m.\u001b[39mconcatenate([parents\u001b[39m.\u001b[39mflatten(), mutated_children\u001b[39m.\u001b[39mflatten()])\u001b[39m.\u001b[39mreshape((population_size, X_train\u001b[39m.\u001b[39mshape[\u001b[39m1\u001b[39m]))\n\u001b[0;32m     <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E8%A7%84%E5%88%99%E6%8A%BD%E5%8F%96.ipynb#X13sZmlsZQ%3D%3D?line=72'>73</a>\u001b[0m \u001b[39m# 选择最优个体并提取规则\u001b[39;00m\n", "\u001b[1;32mc:\\Users\\<USER>\\Desktop\\Water\\Code\\规则抽取.ipynb Cell 6\u001b[0m in \u001b[0;36mmutate\u001b[1;34m(individual)\u001b[0m\n\u001b[0;32m     <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E8%A7%84%E5%88%99%E6%8A%BD%E5%8F%96.ipynb#X13sZmlsZQ%3D%3D?line=56'>57</a>\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39mmutate\u001b[39m(individual):\n\u001b[0;32m     <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E8%A7%84%E5%88%99%E6%8A%BD%E5%8F%96.ipynb#X13sZmlsZQ%3D%3D?line=57'>58</a>\u001b[0m     \u001b[39m# 采用位翻转算法进行变异操作\u001b[39;00m\n\u001b[1;32m---> <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E8%A7%84%E5%88%99%E6%8A%BD%E5%8F%96.ipynb#X13sZmlsZQ%3D%3D?line=58'>59</a>\u001b[0m     \u001b[39mfor\u001b[39;00m i \u001b[39min\u001b[39;00m \u001b[39mrange\u001b[39m(\u001b[39mlen\u001b[39;49m(individual)):\n\u001b[0;32m     <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E8%A7%84%E5%88%99%E6%8A%BD%E5%8F%96.ipynb#X13sZmlsZQ%3D%3D?line=59'>60</a>\u001b[0m         \u001b[39mif\u001b[39;00m np\u001b[39m.\u001b[39mrandom\u001b[39m.\u001b[39mrand() \u001b[39m<\u001b[39m mutation_rate:\n\u001b[0;32m     <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E8%A7%84%E5%88%99%E6%8A%BD%E5%8F%96.ipynb#X13sZmlsZQ%3D%3D?line=60'>61</a>\u001b[0m             individual[i] \u001b[39m=\u001b[39m \u001b[39m1\u001b[39m \u001b[39m-\u001b[39m individual[i]\n", "\u001b[1;31mTypeError\u001b[0m: object of type 'numpy.int32' has no len()"]}], "source": ["import numpy as np\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.metrics import accuracy_score\n", "\n", "# 随机森林分类器的超参数\n", "n_estimators = 100\n", "max_depth = 5\n", "max_features = 'sqrt'\n", "random_state = 42\n", "\n", "# 遗传算法的超参数\n", "population_size = 100\n", "num_generations = 50\n", "mutation_rate = 0.05\n", "\n", "# 生成随机森林的训练和测试数据\n", "X_train = np.random.rand(100, 10)\n", "y_train = np.random.randint(0, 2, size=100)\n", "X_test = np.random.rand(20, 10)\n", "y_test = np.random.randint(0, 2, size=20)\n", "\n", "# 定义适应度函数，用于评估每个个体的性能\n", "def fitness_function(individual, X, y):\n", "    # 将个体转换为特征掩码\n", "    feature_mask = individual.astype(bool)\n", "    # 从特征掩码中选择特征\n", "    selected_features = X[:, feature_mask]\n", "    # 使用随机森林分类器对选择的特征进行训练和预测\n", "    clf = RandomForestClassifier(n_estimators=n_estimators, max_depth=max_depth, max_features=max_features, random_state=random_state)\n", "    clf.fit(selected_features, y)\n", "    y_pred = clf.predict(selected_features)\n", "    # 计算准确率作为适应度分数\n", "    return accuracy_score(y, y_pred)\n", "\n", "# 定义遗传算法的操作\n", "def initialize_population():\n", "    # 随机生成种群中的个体\n", "    return np.random.randint(0, 2, size=(population_size, X_train.shape[1]))\n", "\n", "def select_parents(population, fitness_scores):\n", "    # 采用轮盘赌选择算法选择双亲\n", "    fitness_probs = fitness_scores / np.sum(fitness_scores)\n", "    parent_indices = np.random.choice(np.arange(population_size), size=2, replace=False, p=fitness_probs)\n", "    return population[parent_indices]\n", "\n", "def mutate(individual):\n", "    # 将个体转换为布尔类型的numpy数组\n", "    individual_bool = individual.astype(bool)\n", "    # 采用位翻转算法进行变异操作\n", "    for i in range(len(individual_bool)):\n", "        if np.random.rand() < mutation_rate:\n", "            individual_bool[i] = ~individual_bool[i]\n", "    # 将变异后的布尔类型的numpy数组转换为整数类型的numpy数组\n", "    individual_int = individual_bool.astype(int)\n", "    return individual_int\n", "\n", "def mutate(individual):\n", "    # 采用位翻转算法进行变异操作\n", "    for i in range(len(individual)):\n", "        if np.random.rand() < mutation_rate:\n", "            individual[i] = 1 - individual[i]\n", "    return individual\n", "\n", "# 运行遗传算法进行特征选择\n", "population = initialize_population()\n", "for i in range(num_generations):\n", "    fitness_scores = np.array([fitness_function(individual, X_train, y_train) for individual in population])\n", "    parents = np.array([select_parents(population, fitness_scores) for i in range(population_size // 2)])\n", "    children = np.array([crossover(p) for p in parents])\n", "    mutated_children = np.array([mutate(c) for c in children.flatten()]).reshape(children.shape)\n", "    population = np.concatenate([parents.flatten(), mutated_children.flatten()]).reshape((population_size, X_train.shape[1]))\n", "\n", "# 选择最优个体并提取规则\n", "best_individual = population[np.argmax(fitness_scores)]\n", "feature_mask = best_individual.astype(bool)\n", "selected_features = X_train[:, feature_mask]\n", "clf = RandomForestClassifier(n_estimators=n_estimators, max_depth=max_depth, max_features=max_features, random_state=random_state)\n", "clf.fit(selected_features, y_train)\n", "rules = []\n", "for tree in clf.estimators_:\n", "    tree_rules = []\n", "    for i, (feature, threshold) in enumerate(zip(tree.tree_.feature, tree.tree_.threshold)):\n", "        print(feature, threshold)\n", "        if feature != -2:\n", "            tree_rules.append(f'feature_{feature} <= {threshold}')\n", "    rules.append(tree_rules)"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\deap\\creator.py:138: RuntimeWarning: A class named 'FitnessMax' has already been created and it will be overwritten. Consider deleting previous creation of that class or rename it.\n", "  warnings.warn(\"A class named '{0}' has already been created and it \"\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\deap\\creator.py:138: RuntimeWarning: A class named 'Individual' has already been created and it will be overwritten. Consider deleting previous creation of that class or rename it.\n", "  warnings.warn(\"A class named '{0}' has already been created and it \"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[0, 0, 0, 0]\n", "[]\n"]}], "source": ["import random\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.tree import export_text\n", "from deap import algorithms, base, creator, tools\n", "\n", "# 定义问题适应度函数\n", "def evaluate(individual):\n", "    # 将个体转换为特征索引列表\n", "    features = [i for i, f in enumerate(individual) if f]\n", "    if not features:\n", "        return 0,  # 如果没有选择任何特征，适应度为0\n", "\n", "    # 使用选定的特征构建随机森林\n", "    rf = RandomForestClassifier(n_estimators=100)\n", "    rf.fit(X_train[:, features], y_train)\n", "\n", "    # 提取随机森林的决策规则\n", "    decision_rules = []\n", "    for tree in rf.estimators_:\n", "        decision_rules.append(export_text(tree))\n", "\n", "    # 计算规则的复杂度\n", "    complexity = sum(len(rule.split('\\n')) for rule in decision_rules)\n", "\n", "    # 计算准确率和规则复杂度之间的权衡值\n", "    accuracy = rf.score(X_test[:, features], y_test)\n", "    fitness = accuracy - complexity * 0.01  # 通过减少复杂度惩罚来促进准确性\n", "\n", "    return fitness,\n", "\n", "# 加载 Iris 数据集\n", "iris = load_iris()\n", "X_train, X_test, y_train, y_test = train_test_split(iris.data, iris.target, test_size=0.3, random_state=42)\n", "\n", "# 创建遗传算法的基本元素\n", "creator.create('FitnessMax', base.Fitness, weights=(1.0,))\n", "creator.create('Individual', list, fitness=creator.FitnessMax)\n", "toolbox = base.Toolbox()\n", "\n", "# 注册遗传算法操作符\n", "toolbox.register('attr_bool', random.randint, 0, 1)\n", "toolbox.register('individual', tools.init<PERSON><PERSON><PERSON>, creator.Individual, toolbox.attr_bool, n=len(X_train[0]))\n", "toolbox.register('population', tools.initRepeat, list, toolbox.individual)\n", "toolbox.register('evaluate', evaluate)\n", "toolbox.register('mate', tools.cxTwoPoint)\n", "toolbox.register('mutate', tools.mutFlipBit, indpb=0.05)\n", "toolbox.register('select', tools.selTournament, tournsize=3)\n", "\n", "# 设置算法参数\n", "population_size = 50\n", "generations = 10\n", "cx_probability = 0.5\n", "mut_probability = 0.2\n", "\n", "# 创建初始种群\n", "population = toolbox.population(n=population_size)\n", "\n", "# 执行遗传算法的进化过程\n", "for generation in range(generations):\n", "    offspring = algorithms.varAnd(population, toolbox, cxpb=cx_probability, mutpb=mut_probability)\n", "    fitnesses = toolbox.map(toolbox.evaluate, offspring)\n", "    for ind, fit in zip(offspring, fitnesses):\n", "        ind.fitness.values = fit\n", "    population = toolbox.select(offspring, k=population_size)\n", "\n", "# 选择最佳个体\n", "best_individual = tools.selBest(population, k=1)[0]\n", "print(best_individual)\n", "# 将最佳个体转换为特征索引列表\n", "selected_features = [i for i, f in enumerate(best_individual) if f]\n", "print(selected_features)\n", "# # 使用选定的特征构建最终的随机森林\n", "# final_rf = RandomForestClassifier(n_estimators=100)\n", "# final_rf.fit(X_train[:, selected_features], y_train)\n", "\n", "# # 输出最终的决策规则\n", "# final_decision_rules = []\n", "# for tree in final_rf.estimators_:\n", "#     final_decision_rules.append(export_text(tree))\n", "# for rule in final_decision_rules[:5]:  # 输出前5个规则\n", "    # print(rule)\n"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[(9.991799638286698, 9.995995186748756), (2.605424374734712, 4.866384169019482), (0.5685788285307503, 3.828812590608154), (6.885882397626469, 9.754689726354664)]\n", "[(7.367919623091748, 7.7100380029535085), (9.02815127744686, 9.399607583283784), (8.944427518451088, 9.382254149371896), (2.210946180927582, 5.34915385213877)]\n", "[(7.367919623091748, 7.7100380029535085), (9.02815127744686, 9.399607583283784), (8.092469480411284, 8.657194717708999), (2.210946180927582, 5.34915385213877)]\n", "[(7.367919623091748, 7.7100380029535085), (9.02815127744686, 9.399607583283784), (8.092469480411284, 8.657194717708999), (2.210946180927582, 5.34915385213877)]\n", "[(7.031262850223308, 7.606473731764551), (9.256888039605967, 9.778870559861417), (8.092469480411284, 8.657194717708999), (6.885882397626469, 9.754689726354664)]\n"]}], "source": ["import random\n", "from sklearn.datasets import load_iris\n", "from sklearn.ensemble import RandomForestClassifier\n", "\n", "# 加载鸢尾花数据集\n", "iris = load_iris()\n", "X = iris.data\n", "y = iris.target\n", "\n", "# 定义问题特定的参数\n", "population_size = 100  # 种群大小\n", "max_iterations = 50  # 最大迭代次数\n", "mutation_rate = 0.1  # 变异率\n", "\n", "# 定义遗传算法的相关操作\n", "def initialize_population():\n", "    population = []\n", "    for _ in range(population_size):\n", "        individual = generate_individual()\n", "        population.append(individual)\n", "    return population\n", "\n", "def generate_individual():\n", "    individual = []\n", "    for _ in range(4):  # 鸢尾花数据集有4个特征\n", "        condition = generate_condition()\n", "        individual.append(condition)\n", "    return individual\n", "\n", "def generate_condition():\n", "    # 生成特征的条件，例如特征的取值范围或布尔表达式\n", "    # 这里以特征的取值范围为例\n", "    min_value = random.uniform(0, 10)\n", "    max_value = random.uniform(min_value, 10)\n", "    condition = (min_value, max_value)\n", "    return condition\n", "\n", "def evaluate_fitness(individual):\n", "    clf = RandomForestClassifier(n_estimators=10)\n", "    clf.fit(X, y)\n", "    accuracy = clf.score(X, y)\n", "\n", "    # 计算规则的复杂度，例如规则中的条件数量或特征的重要性之和\n", "    complexity = len(individual)  # 这里简单地将规则的长度作为复杂度指标\n", "\n", "    # 综合考虑准确率和复杂度计算适应度\n", "    fitness = accuracy - 0.1 * complexity  # 可根据具体需求调整权重\n", "\n", "    return fitness\n", "\n", "\n", "def selection(population):\n", "    fitness_values = [evaluate_fitness(individual) for individual in population]\n", "    total_fitness = sum(fitness_values)\n", "    probabilities = [fitness / total_fitness for fitness in fitness_values]\n", "    selected_population = random.choices(population, probabilities, k=population_size)\n", "    return selected_population\n", "\n", "def crossover(parent1, parent2):\n", "    child = []\n", "    for i in range(len(parent1)):\n", "        if random.random() < 0.5:\n", "            child.append(parent1[i])\n", "        else:\n", "            child.append(parent2[i])\n", "    return child\n", "\n", "def mutation(individual):\n", "    mutated_individual = individual.copy()\n", "    if random.random() < mutation_rate:\n", "        index = random.randint(0, len(mutated_individual) - 1)\n", "        mutated_individual[index] = generate_condition()\n", "    return mutated_individual\n", "\n", "def extract_rules_from_forest():\n", "    population = initialize_population()\n", "\n", "    for _ in range(max_iterations):\n", "        selected_population = selection(population)\n", "\n", "        new_population = []\n", "        while len(new_population) < population_size:\n", "            parent1 = random.choice(selected_population)\n", "            parent2 = random.choice(selected_population)\n", "            child = crossover(parent1, parent2)\n", "            mutated_child = mutation(child)\n", "            new_population.append(mutated_child)\n", "\n", "        population = new_population\n", "\n", "    fitness_values = [evaluate_fitness(individual) for individual in population]\n", "\n", "    sorted_population = [x for _, x in sorted(zip(fitness_values, population), reverse=True)]\n", "    selected_rules = sorted_population[:5]  # 选择适应度最高的5个规则\n", "        \n", "    return selected_rules\n", "\n", "# 主程序\n", "def main():\n", "    extracted_rules = extract_rules_from_forest()\n", "\n", "    for rule in extracted_rules:\n", "        print(rule)\n", "\n", "if __name__ == '__main__':\n", "    main()\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(sepal length (cm) > 4.3) and (sepal width (cm) <= 2.0) and (petal length (cm) > 1.0) and (petal width (cm) <= 0.1) \n", "(sepal length (cm) <= 4.3) and (sepal width (cm) <= 2.0) and (petal length (cm) > 1.0) and (petal width (cm) > 0.1) \n", "(sepal length (cm) <= 4.3) and (sepal width (cm) <= 2.0) and (petal length (cm) <= 1.0) and (petal width (cm) > 0.1) \n", "(sepal length (cm) > 4.3) and (sepal width (cm) <= 2.0) and (petal length (cm) > 1.0) and (petal width (cm) <= 0.1) \n", "(sepal length (cm) <= 4.3) and (sepal width (cm) <= 2.0) and (petal length (cm) > 1.0) and (petal width (cm) > 0.1) \n"]}], "source": ["import numpy as np\n", "from sklearn.datasets import load_iris\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.model_selection import train_test_split\n", "\n", "# 加载鸢尾花数据集\n", "data = load_iris()\n", "X = data.data\n", "y = data.target\n", "\n", "# 划分训练集和测试集\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "\n", "# 构建随机森林模型\n", "rf = RandomForestClassifier(n_estimators=100, random_state=42)\n", "rf.fit(X_train, y_train)\n", "\n", "# 定义遗传算法的相关函数\n", "def initialize_population(population_size, num_features):\n", "    population = []\n", "    for _ in range(population_size):\n", "        rule = np.random.randint(2, size=num_features)  # 随机生成规则\n", "        population.append(rule)\n", "    return population\n", "\n", "def evaluate_fitness(population):\n", "    fitness_scores = []\n", "    for rule in population:\n", "        predicted = rf.predict([rule])[0]\n", "        accuracy = (predicted == y_train).mean()\n", "        fitness_scores.append(accuracy)\n", "    return fitness_scores\n", "\n", "def crossover(parent1, parent2):\n", "    crossover_point = np.random.randint(1, len(parent1))\n", "    child1 = np.concatenate((parent1[:crossover_point], parent2[crossover_point:]))\n", "    child2 = np.concatenate((parent2[:crossover_point], parent1[crossover_point:]))\n", "    return child1, child2\n", "\n", "def mutate(individual, mutation_rate):\n", "    mutated_individual = individual.copy()\n", "    for i in range(len(mutated_individual)):\n", "        if np.random.random() < mutation_rate:\n", "            mutated_individual[i] = 1 - mutated_individual[i]  # 随机变异一个位点\n", "    return mutated_individual\n", "\n", "def select_parents(population, fitness_scores):\n", "    sorted_indices = np.argsort(fitness_scores)[::-1]  # 根据适应度评估结果排序\n", "    parent1 = population[sorted_indices[0]]\n", "    parent2 = population[sorted_indices[1]]\n", "    return parent1, parent2\n", "\n", "# 设置遗传算法的参数\n", "population_size = 50\n", "num_generations = 100\n", "mutation_rate = 0.1\n", "\n", "# 初始化种群\n", "num_features = X_train.shape[1]\n", "population = initialize_population(population_size, num_features)\n", "\n", "# 遗传算法迭代\n", "for generation in range(num_generations):\n", "    fitness_scores = evaluate_fitness(population)\n", "\n", "    # 选择和繁殖下一代\n", "    new_population = []\n", "    for _ in range(population_size // 2):\n", "        parent1, parent2 = select_parents(population, fitness_scores)\n", "        child1, child2 = crossover(parent1, parent2)\n", "        mutated_child1 = mutate(child1, mutation_rate)\n", "        mutated_child2 = mutate(child2, mutation_rate)\n", "        new_population.extend([mutated_child1, mutated_child2])\n", "    \n", "    population = new_population\n", "\n", "# 提取适应度最高的规则\n", "final_fitness_scores = evaluate_fitness(population)\n", "best_rule_indices = np.argsort(final_fitness_scores)[::-1][:5]\n", "best_rules = [population[idx] for idx in best_rule_indices]\n", "\n", "# 解释规则\n", "feature_names = data.feature_names\n", "for rule in best_rules:\n", "    rule_str = \"\"\n", "    for i, feature in enumerate(feature_names):\n", "        if rule[i] == 0:\n", "            rule_str += f\"({feature} <= {X_train[:, i].min()}) and \"\n", "        else:\n", "            rule_str += f\"({feature} > {X_train[:, i].min()}) and \"\n", "    rule_str = rule_str[:-4]  # 去除最后的 \"and\"\n", "    print(rule_str)\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(sepal width (cm) > 2.0) and (petal length (cm) <= 1.0) and (petal width (cm) > 0.1) \n"]}], "source": ["import numpy as np\n", "import random\n", "from sklearn.datasets import load_iris\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.model_selection import train_test_split\n", "\n", "# 加载鸢尾花数据集\n", "data = load_iris()\n", "X = data.data\n", "y = data.target\n", "feature_names = data.feature_names\n", "\n", "# 划分训练集和测试集\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "\n", "# 构建随机森林模型\n", "rf = RandomForestClassifier(n_estimators=100, random_state=42)\n", "rf.fit(X_train, y_train)\n", "\n", "# 定义遗传算法的相关函数\n", "def initialize_population(population_size, num_features, num_selected_features):\n", "    population = []\n", "    for _ in range(population_size):\n", "        rule = [random.randint(0, 1) for _ in range(num_features)]\n", "        selected_features = random.sample(range(num_features), num_selected_features)\n", "        for i in range(num_features):\n", "            if i not in selected_features:\n", "                rule[i] = -1  # 使用 -1 表示舍弃的特征\n", "        population.append(rule)\n", "    return population\n", "\n", "def evaluate_fitness(population):\n", "    fitness_scores = []\n", "    for rule in population:\n", "        predicted = rf.predict([rule])[0]\n", "        accuracy = (predicted == y_train).mean()\n", "        fitness_scores.append(accuracy)\n", "    return fitness_scores\n", "\n", "def crossover(parent1, parent2):\n", "    crossover_point = random.randint(1, len(parent1)-1)\n", "    child1 = parent1[:crossover_point] + parent2[crossover_point:]\n", "    child2 = parent2[:crossover_point] + parent1[crossover_point:]\n", "    return child1, child2\n", "\n", "def mutate(individual, mutation_rate):\n", "    mutated_individual = individual.copy()\n", "    for i in range(len(mutated_individual)):\n", "        if np.random.random() < mutation_rate:\n", "            mutated_individual[i] = 1 - mutated_individual[i]  # 随机变异一个位点\n", "    return mutated_individual\n", "\n", "def select_parents(population, fitness_scores):\n", "    sorted_indices = np.argsort(fitness_scores)[::-1]  # 根据适应度评估结果排序\n", "    parent1 = population[sorted_indices[0]]\n", "    parent2 = population[sorted_indices[1]]\n", "    return parent1, parent2\n", "\n", "# 设置遗传算法的参数\n", "population_size = 50\n", "num_generations = 100\n", "mutation_rate = 0.1\n", "num_selected_features = 3  # 指定所需的特征数量\n", "\n", "# 初始化种群\n", "num_features = X_train.shape[1]\n", "population = initialize_population(population_size, num_features, num_selected_features)\n", "\n", "# 遗传算法迭代\n", "for generation in range(num_generations):\n", "    fitness_scores = evaluate_fitness(population)\n", "\n", "    # 选择和繁殖下一代\n", "    new_population = []\n", "    for _ in range(population_size // 2):\n", "        parent1, parent2 = select_parents(population, fitness_scores)\n", "        child1, child2 = crossover(parent1, parent2)\n", "        mutated_child1 = mutate(child1, mutation_rate)\n", "        mutated_child2 = mutate(child2, mutation_rate)\n", "        new_population.extend([mutated_child1, mutated_child2])\n", "    \n", "    population = new_population\n", "\n", "# 提取适应度最高的规则\n", "final_fitness_scores = evaluate_fitness(population)\n", "best_rule_index = np.argmax(final_fitness_scores)\n", "best_rule = population[best_rule_index]\n", "\n", "# 解释规则\n", "selected_features = [feature_names[i] for i, bit in enumerate(best_rule) if bit != -1]\n", "thresholds = [X_train[:, i].min() for i, bit in enumerate(best_rule) if bit != -1]\n", "symbols = ['>' if bit == 1 else '<=' for bit in best_rule if bit != -1]\n", "\n", "rule_str = \"\"\n", "for feature, threshold, symbol in zip(selected_features, thresholds, symbols):\n", "    rule_str += f\"({feature} {symbol} {threshold}) and \"\n", "rule_str = rule_str[:-4]  # 去除最后的 \"and\"\n", "\n", "print(rule_str)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(sepal length (cm) > 4.3) and (sepal width (cm) <= 2.0) and (petal length (cm) > 1.0) \n", "(sepal length (cm) > 4.3) and (sepal width (cm) <= 2.0) and (petal length (cm) > 1.0) and (petal width (cm) > 0.1) \n", "(sepal length (cm) > 4.3) and (sepal width (cm) <= 2.0) and (petal length (cm) > 1.0) and (petal width (cm) > 0.1) \n"]}], "source": ["import numpy as np\n", "import random\n", "from sklearn.datasets import load_iris\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.model_selection import train_test_split\n", "\n", "# 加载鸢尾花数据集\n", "data = load_iris()\n", "X = data.data\n", "y = data.target\n", "feature_names = data.feature_names\n", "\n", "# 划分训练集和测试集\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "\n", "# 构建随机森林模型\n", "rf = RandomForestClassifier(n_estimators=100, random_state=42)\n", "rf.fit(X_train, y_train)\n", "\n", "# 定义遗传算法的相关函数\n", "def initialize_population(population_size, num_features, num_selected_features):\n", "    population = []\n", "    for _ in range(population_size):\n", "        rule = [random.randint(0, 1) for _ in range(num_features)]\n", "        selected_features = random.sample(range(num_features), num_selected_features)\n", "        for i in range(num_features):\n", "            if i not in selected_features:\n", "                rule[i] = -1  # 使用 -1 表示舍弃的特征\n", "        population.append(rule)\n", "    return population\n", "\n", "def evaluate_fitness(population):\n", "    fitness_scores = []\n", "    for rule in population:\n", "        predicted = rf.predict([rule])[0]\n", "        accuracy = (predicted == y_train).mean()\n", "        fitness_scores.append(accuracy)\n", "    return fitness_scores\n", "\n", "def crossover(parent1, parent2):\n", "    crossover_point = random.randint(1, len(parent1)-1)\n", "    child1 = parent1[:crossover_point] + parent2[crossover_point:]\n", "    child2 = parent2[:crossover_point] + parent1[crossover_point:]\n", "    return child1, child2\n", "\n", "def mutate(individual, mutation_rate):\n", "    mutated_individual = individual.copy()\n", "    for i in range(len(mutated_individual)):\n", "        if np.random.random() < mutation_rate:\n", "            mutated_individual[i] = 1 - mutated_individual[i]  # 随机变异一个位点\n", "    return mutated_individual\n", "\n", "def select_parents(population, fitness_scores):\n", "    sorted_indices = np.argsort(fitness_scores)[::-1]  # 根据适应度评估结果排序\n", "    parent1 = population[sorted_indices[0]]\n", "    parent2 = population[sorted_indices[1]]\n", "    return parent1, parent2\n", "\n", "# 设置遗传算法的参数\n", "population_size = 50\n", "num_generations = 100\n", "mutation_rate = 0.1\n", "num_selected_features = 3  # 指定所需的特征数量\n", "num_feature_combinations = 3  # 指定所需的特征组合数量\n", "\n", "# 初始化种群\n", "num_features = X_train.shape[1]\n", "population = initialize_population(population_size, num_features, num_selected_features)\n", "\n", "# 遗传算法迭代\n", "for generation in range(num_generations):\n", "    fitness_scores = evaluate_fitness(population)\n", "\n", "    # 选择和繁殖下一代\n", "    new_population = []\n", "    for _ in range(population_size // 2):\n", "        parent1, parent2 = select_parents(population, fitness_scores)\n", "        child1, child2 = crossover(parent1, parent2)\n", "        mutated_child1 = mutate(child1, mutation_rate)\n", "        mutated_child2 = mutate(child2, mutation_rate)\n", "        new_population.extend([mutated_child1, mutated_child2])\n", "    \n", "    population = new_population\n", "\n", "# 提取适应度最高的规则\n", "final_fitness_scores = evaluate_fitness(population)\n", "best_rule_indices = np.argsort(final_fitness_scores)[::-1][:num_feature_combinations]\n", "best_rules = [population[idx] for idx in best_rule_indices]\n", "\n", "# 解释规则\n", "for rule in best_rules:\n", "    selected_features = [feature_names[i] for i, bit in enumerate(rule) if bit != -1]\n", "    thresholds = [X_train[:, i].min() for i, bit in enumerate(rule) if bit != -1]\n", "    symbols = ['>' if bit == 1 else '<=' for bit in rule if bit != -1]\n", "    \n", "    rule_str = \"\"\n", "    for feature, threshold, symbol in zip(selected_features, thresholds, symbols):\n", "        rule_str += f\"({feature} {symbol} {threshold}) and \"\n", "    rule_str = rule_str[:-4]  # 去除最后的 \"and\"\n", "    print(rule_str)\n", "    "]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["最佳特征子集： [1, 2, 3]\n", "对应阈值： [1.9822781541083834, 1.7271034320133205, 4.5994886112230535, 1.4947840683270561]\n", "最佳适应度： 1.0\n"]}], "source": ["import random\n", "from sklearn.datasets import load_iris\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.tree import DecisionTreeClassifier\n", "\n", "# 加载数据集\n", "data = load_iris()\n", "X = data.data\n", "y = data.target\n", "\n", "# 定义遗传算法的参数\n", "population_size = 100  # 种群大小\n", "chromosome_length = X.shape[1]  # 染色体长度，即特征数\n", "mutation_rate = 0.01  # 突变率\n", "crossover_rate = 0.8  # 交叉率\n", "generations = 50  # 迭代次数\n", "\n", "# 定义特征对应的阈值范围\n", "threshold_range = {\n", "    0: (0.0, 5.0),\n", "    1: (0.0, 3.0),\n", "    2: (0.0, 7.0),\n", "    3: (0.0, 2.5)\n", "}\n", "\n", "# 初始化种群\n", "def initialize_population():\n", "    population = []\n", "    for _ in range(population_size):\n", "        chromosome = [(random.randint(0, 1), random.uniform(threshold_range[i][0], threshold_range[i][1])) for i in range(chromosome_length)]\n", "        population.append(chromosome)\n", "    return population\n", "\n", "# 计算适应度\n", "def fitness_function(chromosome):\n", "    selected_features = [i for i in range(chromosome_length) if chromosome[i][0] == 1]\n", "    if len(selected_features) == 0:\n", "        return 0\n", "    \n", "    X_selected = X[:, selected_features]\n", "    X_train, X_test, y_train, y_test = train_test_split(X_selected, y, test_size=0.2)\n", "    \n", "    classifier = DecisionTreeClassifier()\n", "    classifier.fit(X_train, y_train)\n", "    accuracy = classifier.score(X_test, y_test)\n", "    return accuracy\n", "\n", "# 选择操作\n", "def selection(population):\n", "    # 锦标赛选择\n", "    tournament_size = 3\n", "    selected = []\n", "    for _ in range(population_size):\n", "        participants = random.sample(population, tournament_size)\n", "        fitnesses = [fitness_function(chromosome) for chromosome in participants]\n", "        winner = participants[fitnesses.index(max(fitnesses))]\n", "        selected.append(winner)\n", "    return selected\n", "\n", "# 交叉操作\n", "def crossover(population):\n", "    offspring = []\n", "    for i in range(population_size):\n", "        parent1 = random.choice(population)\n", "        parent2 = random.choice(population)\n", "        if random.random() < crossover_rate:\n", "            crossover_point = random.randint(1, chromosome_length - 1)\n", "            child = parent1[:crossover_point] + parent2[crossover_point:]\n", "        else:\n", "            child = parent1\n", "        offspring.append(child)\n", "    return offspring\n", "\n", "# 突变操作\n", "def mutation(population):\n", "    for i in range(population_size):\n", "        for j in range(chromosome_length):\n", "            if random.random() < mutation_rate:\n", "                if random.random() < 0.5:  # 突变特征子集\n", "                    population[i][j] = (1 - population[i][j][0], population[i][j][1])\n", "                else:  # 突变阈值\n", "                    threshold_min, threshold_max = threshold_range[j]\n", "                    population[i][j] = (population[i][j][0], random.uniform(threshold_min, threshold_max))\n", "    return population\n", "\n", "# 主函数\n", "def genetic_algorithm():\n", "    population = initialize_population()\n", "    for _ in range(generations):\n", "        population = selection(population)\n", "        population = crossover(population)\n", "        population = mutation(population)\n", "    best_chromosome = max(population, key=fitness_function)\n", "    best_fitness = fitness_function(best_chromosome)\n", "    return best_chromosome, best_fitness\n", "\n", "# 运行遗传算法\n", "best_solution, best_fitness = genetic_algorithm()\n", "selected_features = [i for i in range(chromosome_length) if best_solution[i][0] == 1]\n", "thresholds = [best_solution[i][1] for i in range(chromosome_length)]\n", "print(\"最佳特征子集：\", selected_features)\n", "print(\"对应阈值：\", thresholds)\n", "print(\"最佳适应度：\", best_fitness)\n"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["thresholds"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["''"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["rule_str"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["rule_str += f\"({feature} {symbol} {threshold}) and \""]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["'(petal width (cm) > 0.1) '"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["rule_str[:-4]"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}