{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## ChatGPT修改版"]}, {"cell_type": "code", "execution_count": 97, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["queue [(36, 139), (36, 140), (37, 137), (37, 138), (37, 139), (38, 137), (38, 138), (39, 137)]\n", "[ 36. 140.] [ 39. 137.] [ 39. 137.] [ 39. 137.]\n"]}, {"data": {"text/plain": ["0"]}, "execution_count": 97, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "from itertools import combinations\n", "\n", "arr = ([(36, 139), (36, 140), (37, 137), (37, 138), (37, 139), (38, 137), (38, 138), (39, 137)])\n", "\n", "def vector_angle(points):\n", "    if len(points)<=1:\n", "        return 0\n", "    \n", "    a = np.array([np.inf, -np.inf])\n", "    b = np.array([-np.inf, -np.inf])\n", "    c = np.array([np.inf, np.inf])\n", "    d = np.array([-np.inf, np.inf])\n", "\n", "    for i in range(len(points)):\n", "        if points[i][0] <= a[0] or points[i][1] >= a[1]:\n", "            a[:] = points[i]\n", "        if points[i][0] >= b[0] or points[i][1] >= b[1]:\n", "            b[:] = points[i]\n", "        if points[i][0] <= c[0] or points[i][1] <= c[1]:\n", "            c[:] = points[i]\n", "        if points[i][0] >= d[0] or points[i][1] <= d[1]:\n", "            d[:] = points[i]\n", "    print('queue', points)\n", "    print(a, b, c, d)\n", "    dot1 = np.dot(b-a, d-c)\n", "    norm1_1 = np.linalg.norm(b-a)\n", "    norm1_2 = np.linalg.norm(d-c)\n", "    \n", "    dot2 = np.dot(c-a, d-b)\n", "    norm2_1 = np.linalg.norm(c-a)\n", "    norm2_2 = np.linalg.norm(d-b)\n", " \n", "    if any(x==0 for x in [norm1_1, norm1_2, norm2_1, norm2_2]):\n", "        return 0\n", "    \n", "    theta1 = np.arccos(dot1 / norm1_1 / norm1_2)\n", "    theta2 = np.arccos(dot2 / norm2_1 / norm2_2)\n", "        \n", "    return np.max([theta1, theta2])\n", "\n", "\n", "vector_angle(arr)"]}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 84, "metadata": {}, "output_type": "execute_result"}], "source": ["any(x==y for x, y in combinations([1, 1, 2, 3], 2))"]}, {"cell_type": "code", "execution_count": 132, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1, 2],\n", "       [3, 4],\n", "       [5, 6],\n", "       [0, 9],\n", "       [2, 7]])"]}, "execution_count": 132, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "points = [(1, 2), (3, 4), (5, 6), (0, 9), (2, 7)]\n", "np.array(points)"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(5, 9) (5, 2) (0, 9) (0, 2)\n"]}], "source": ["import numpy as np\n", "from collections import deque\n", "\n", "points = [(1, 2), (3, 4), (5, 6), (0, 9), (2, 7)]\n", "\n", "# 转化为二维数组\n", "arr = ([(36, 139), (36, 140), (37, 137), (37, 138), (37, 139), (38, 137), (38, 138), (39, 137)])\n", "def vector_angle(queue):\n", "    try:\n", "        queue = np.array(queue)\n", "        x_min = np.min(queue[:, 0])\n", "        x_max = np.max(queue[:, 0])\n", "        y_min = np.min(queue[:, 1])\n", "        y_max = np.max(queue[:, 1])\n", "        \n", "        queue[queue[:, 0]==x_min]\n", "        a = np.array([x_min, y_max])\n", "        b = np.array([x_max, y_max])\n", "        c = np.array([x_min, y_min])\n", "        d = np.array([x_max, y_min])\n", "        \n", "        theta1 = np.arccos(np.dot(b-a, d-c) / np.linalg.norm(b-a) / np.linalg.norm(d-c))\n", "        theta2 = np.arccos(np.dot(c-a, d-b) / np.linalg.norm(c-a) / np.linalg.norm(d-b))\n", "\n", "        return np.max([theta1, theta2])\n", "    except:\n", "        return 0"]}, {"cell_type": "code", "execution_count": 100, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{(36, 139), (36, 140)}\n", "[(39, 137), (36, 139), (37, 138), (38, 138), (37, 137), (38, 137), (37, 139), (36, 140)]\n"]}, {"data": {"text/plain": ["146.30993247402023"]}, "execution_count": 100, "metadata": {}, "output_type": "execute_result"}], "source": ["import math\n", "def max_angle(points):\n", "    # 扫描x轴,获取y不变或最大的点\n", "    x_scan = set()\n", "    y_max, y_cur = -float('inf'), -float('inf')\n", "    for x, y in points:\n", "        if y > y_max:\n", "            y_max = y\n", "            x_scan.add((x, y_max))\n", "        elif y == y_cur:\n", "            x_scan.add((x, y))\n", "        y_cur = y\n", "    \n", "    # 扫描y轴,获取x不变或最大的点\n", "    y_scan = set()\n", "    x_max, x_cur = -float('inf'), -float('inf')\n", "    for x, y in points:\n", "        if x > x_max:\n", "            x_max = x\n", "            y_scan.add((x_max, y))\n", "        elif x == x_cur:\n", "            y_scan.add((x, y))\n", "        x_cur = x\n", "    \n", "    # 求最大夹角\n", "    points = list(x_scan | y_scan)\n", "    \n", "    print(points)\n", "    \n", "    n = len(points)\n", "    max_angle = 0\n", "    for i in range(n):\n", "        j = (i + 1) % n\n", "        x1, y1 = points[i]\n", "        x2, y2 = points[j]\n", "        angle = math.atan2(y2 - y1, x2 - x1) * 180 / math.pi\n", "        max_angle = max(max_angle, angle)\n", "        \n", "    return max_angle\n", "\n", "arr = ([(36, 139), (36, 140), (37, 137), (37, 138), (37, 139), (38, 137), (38, 138), (39, 137)])\n", "\n", "max_angle(arr)"]}, {"cell_type": "code", "execution_count": 117, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[(36, 140), (39, 137), (36, 139), (37, 137)]\n", "146.30993247402023\n"]}], "source": ["import math\n", "\n", "def max_angle(points):\n", "    \n", "    # 扫描x轴,获取x最大和第一个x相等的点\n", "    x_scan = set()\n", "    x_min, x_max = np.inf, -np.inf\n", "    for x, y in points:\n", "        if x > x_max:\n", "            x_max = x\n", "            y_max = y\n", "        if x < x_min:\n", "            x_min = x\n", "            y_min = y\n", "            \n", "    x_scan.add((x_max, y_max))\n", "    x_scan.add((x_min, y_min))\n", "    \n", "    # 扫描y轴,获取y最大和第一个y相等的点\n", "    y_scan = set()\n", "    y_min, y_max = np.inf, -np.inf\n", "    for x, y in points:\n", "        if y > y_max:\n", "            y_max = y\n", "            x_max = x\n", "        if y < y_min:\n", "            y_min = y\n", "            x_min = x\n", "            \n", "    y_scan.add((x_max, y_max))\n", "    y_scan.add((x_min, y_min))\n", "    \n", "    # 求最大夹角            \n", "    points = list(x_scan | y_scan)\n", "    print(points)\n", "    n = len(points)\n", "    max_angle = 0\n", "    for i in range(n):\n", "        j = (i + 1) % n\n", "        x1, y1 = points[i]\n", "        x2, y2 = points[j]\n", "\n", "        angle = math.atan2(y2 - y1, x2 - x1) * 180 / math.pi\n", "        max_angle = max(max_angle, angle)\n", "        \n", "    return max_angle\n", "\n", "arr = ([(36, 139), (36, 140), (37, 137), (37, 138), (37, 139), (38, 137), (38, 138), (39, 137)])\n", "\n", "print(max_angle(arr))"]}, {"cell_type": "code", "execution_count": 121, "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.collections.PathCollection at 0x180dd9b1160>"]}, "execution_count": 121, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "points = np.array([(46, 120), (52, 120), (17, 160), (17, 122)])\n", "plt.scatter(points[:,0], points[:,1], marker='o')"]}, {"cell_type": "code", "execution_count": 140, "metadata": {}, "outputs": [{"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[1;32mc:\\Users\\<USER>\\Desktop\\Water\\Code\\基于密度相连的水体对象识别.ipynb Cell 9\u001b[0m in \u001b[0;36m<cell line: 231>\u001b[1;34m()\u001b[0m\n\u001b[0;32m    <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E5%9F%BA%E4%BA%8E%E5%AF%86%E5%BA%A6%E7%9B%B8%E8%BF%9E%E7%9A%84%E6%B0%B4%E4%BD%93%E5%AF%B9%E8%B1%A1%E8%AF%86%E5%88%AB.ipynb#W1sZmlsZQ%3D%3D?line=237'>238</a>\u001b[0m \u001b[39m# Data Precessing\u001b[39;00m\n\u001b[0;32m    <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E5%9F%BA%E4%BA%8E%E5%AF%86%E5%BA%A6%E7%9B%B8%E8%BF%9E%E7%9A%84%E6%B0%B4%E4%BD%93%E5%AF%B9%E8%B1%A1%E8%AF%86%E5%88%AB.ipynb#W1sZmlsZQ%3D%3D?line=238'>239</a>\u001b[0m arr \u001b[39m=\u001b[39m ras\u001b[39m.\u001b[39mReadAsArray()\n\u001b[1;32m--> <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E5%9F%BA%E4%BA%8E%E5%AF%86%E5%BA%A6%E7%9B%B8%E8%BF%9E%E7%9A%84%E6%B0%B4%E4%BD%93%E5%AF%B9%E8%B1%A1%E8%AF%86%E5%88%AB.ipynb#W1sZmlsZQ%3D%3D?line=240'>241</a>\u001b[0m arr[(arr\u001b[39m<\u001b[39;49m\u001b[39m0\u001b[39;49m) \u001b[39m|\u001b[39;49m (arr\u001b[39m>\u001b[39;49m\u001b[39m10000\u001b[39;49m)] \u001b[39m=\u001b[39m \u001b[39m0\u001b[39m\n\u001b[0;32m    <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E5%9F%BA%E4%BA%8E%E5%AF%86%E5%BA%A6%E7%9B%B8%E8%BF%9E%E7%9A%84%E6%B0%B4%E4%BD%93%E5%AF%B9%E8%B1%A1%E8%AF%86%E5%88%AB.ipynb#W1sZmlsZQ%3D%3D?line=242'>243</a>\u001b[0m \u001b[39m# arr = signal.medfilt(arr, kernel_size=11)\u001b[39;00m\n\u001b[0;32m    <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E5%9F%BA%E4%BA%8E%E5%AF%86%E5%BA%A6%E7%9B%B8%E8%BF%9E%E7%9A%84%E6%B0%B4%E4%BD%93%E5%AF%B9%E8%B1%A1%E8%AF%86%E5%88%AB.ipynb#W1sZmlsZQ%3D%3D?line=243'>244</a>\u001b[0m \n\u001b[0;32m    <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E5%9F%BA%E4%BA%8E%E5%AF%86%E5%BA%A6%E7%9B%B8%E8%BF%9E%E7%9A%84%E6%B0%B4%E4%BD%93%E5%AF%B9%E8%B1%A1%E8%AF%86%E5%88%AB.ipynb#W1sZmlsZQ%3D%3D?line=244'>245</a>\u001b[0m \u001b[39m# kernal = np.ones((3,3), dtype=int)\u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E5%9F%BA%E4%BA%8E%E5%AF%86%E5%BA%A6%E7%9B%B8%E8%BF%9E%E7%9A%84%E6%B0%B4%E4%BD%93%E5%AF%B9%E8%B1%A1%E8%AF%86%E5%88%AB.ipynb#W1sZmlsZQ%3D%3D?line=252'>253</a>\u001b[0m \n\u001b[0;32m    <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E5%9F%BA%E4%BA%8E%E5%AF%86%E5%BA%A6%E7%9B%B8%E8%BF%9E%E7%9A%84%E6%B0%B4%E4%BD%93%E5%AF%B9%E8%B1%A1%E8%AF%86%E5%88%AB.ipynb#W1sZmlsZQ%3D%3D?line=253'>254</a>\u001b[0m \u001b[39m# Export Setting.\u001b[39;00m\n\u001b[0;32m    <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Desktop/Water/Code/%E5%9F%BA%E4%BA%8E%E5%AF%86%E5%BA%A6%E7%9B%B8%E8%BF%9E%E7%9A%84%E6%B0%B4%E4%BD%93%E5%AF%B9%E8%B1%A1%E8%AF%86%E5%88%AB.ipynb#W1sZmlsZQ%3D%3D?line=254'>255</a>\u001b[0m dir_pth \u001b[39m=\u001b[39m \u001b[39mr\u001b[39m\u001b[39m\"\u001b[39m\u001b[39mC:\u001b[39m\u001b[39m\\\u001b[39m\u001b[39mUsers\u001b[39m\u001b[39m\\\u001b[39m\u001b[39mxiaoz\u001b[39m\u001b[39m\\\u001b[39m\u001b[39mDesktop\u001b[39m\u001b[39m\\\u001b[39m\u001b[39mWater\u001b[39m\u001b[39m\\\u001b[39m\u001b[39m2021_zou_tp_part_dbwoe.tif\u001b[39m\u001b[39m\"\u001b[39m\n", "\u001b[1;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["\n", "import numpy as np\n", "from osgeo import gdal, osr\n", "from scipy import signal, ndimage\n", "from collections import deque\n", "from tqdm import tqdm\n", "import matplotlib.pyplot as plt\n", "import random\n", "\n", "def readTif(dataset):\n", "    width = dataset.RasterXSize\n", "    \n", "    height = dataset.RasterYSize\n", "    \n", "    GdalImg_data = dataset.ReadAsArray(0, 0, width, height)\n", "    \n", "    return GdalImg_data\n", "\n", "def array2gtiff(newRasterfn,LowrLft_Origin,pixelWidth,pixelHeight,array,EPSG):\n", "\n", "    #Update users\n", "    print(\"Starting: Write \"+newRasterfn+\" to GeoTiff\")\n", "\n", "    #Get number of bands\n", "    bands = 1 # array.shape[0]\n", "\n", "    #Get number of columns and rows\n", "    rows = array.shape[0]\n", "    cols = array.shape[1]\n", "\n", "    #Get raster origin \n", "    originX = LowrLft_Origin[0]\n", "    originY = LowrLft_Origin[1]\n", "    \n", "    #Get a GeoTiff driver \n", "    driver = gdal.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>('GTiff')\n", "\n", "    #Initialize a output raster with GeoTransform parameters from input tile and array shape  \n", "    outRaster = driver.Create(newRasterfn, cols, rows, bands, gdal.GDT_Int16)\n", "\n", "    outRaster.SetGeoTransform((originX, pixelWidth, 0, originY, 0, pixelHeight))\n", "    \n", "    outRasterSRS = osr.SpatialReference()\n", "    \n", "    outRasterSRS.ImportFromEPSG(EPSG)\n", "    \n", "    outRaster.SetProjection(outRasterSRS.ExportToWkt())\n", "\n", "    #Write each band to the output raster, starting with band 1\n", "    for b in range(1, bands+1):\n", "        #Get the output band @ b\n", "        outband = outRaster.GetRasterBand(b)\n", "        \n", "        #Slice the 3D array at b-1 (2D)\n", "        # ary = array[b-1]\n", "        \n", "        #Write the 2D NumPy array to the band \n", "        outband.WriteArray(array)\n", "        \n", "        #Flush \n", "        outband.FlushCache()\n", "\n", "    #Update user \n", "    print(\"Finished: Write \"+newRasterfn+\" to GeoTiff\")\n", "\n", "def find_neighbors(index, radius, arr, labels, min_threshold):\n", "    row, col = index\n", "    \n", "    row_range = slice(max(0, row - radius), row + radius + 1)\n", "    col_range = slice(max(0, col - radius), col + radius + 1)\n", "    \n", "    neighbors_prob = arr[row_range, col_range]\n", "    labels_judge = labels[row_range, col_range]\n", "    \n", "    # 可用于二次处理，按照形态学、高程等特征对河流进行拆分与合并\n", "    # ratio_judge = np.sum(labels_judge==water_count, axis=0) > ratio*(radius*2+1) ** 2\n", "    # 考虑使用集合元组数据格式 \n", "    # 满足存在条件的才算是邻居 未标记的才算邻居 \n", "    \n", "    indices = np.transpose(np.nonzero((neighbors_prob > min_threshold) & (labels_judge == 0)))\n", "    neighbors_indices = indices + np.array([max(0, row - radius), max(0, col - radius)])\n", "    \n", "    return neighbors_indices\n", "\n", "def max_angle(points):\n", "    if len(points) <= 1:\n", "        return 0\n", "    # 扫描x轴,获取x最大和第一个x相等的点\n", "    x_scan = set()\n", "    x_min, x_max = np.inf, -np.inf\n", "    for x, y in points:\n", "        if x > x_max:\n", "            x_max = x\n", "            y_max = y\n", "        if x < x_min:\n", "            x_min = x\n", "            y_min = y\n", "            \n", "    x_scan.add((x_max, y_max))\n", "    x_scan.add((x_min, y_min))\n", "    \n", "    # 扫描y轴,获取y最大和第一个y相等的点\n", "    y_scan = set()\n", "    y_min, y_max = np.inf, -np.inf\n", "    for x, y in points:\n", "        if y > y_max:\n", "            y_max = y\n", "            x_max = x\n", "        if y < y_min:\n", "            y_min = y\n", "            x_min = x\n", "            \n", "    y_scan.add((x_max, y_max))\n", "    y_scan.add((x_min, y_min))\n", "    \n", "    # 求最大夹角            \n", "    points = list(x_scan | y_scan)\n", "        \n", "    # plt.scatter(np.array(points)[:,0], np.array(points)[:,1], marker='o')\n", "    # plt.show()\n", "    \n", "    n = len(points)\n", "    \n", "    max_angle = 0\n", "    for i in range(n):\n", "        j = (i + 1) % n\n", "        x1, y1 = points[i]\n", "        x2, y2 = points[j]\n", "\n", "        angle = math.atan2(y2 - y1, x2 - x1) * 180 / math.pi\n", "        max_angle = max(max_angle, angle)\n", "        \n", "    return max_angle\n", "\n", "def update_find(arr, labels, current_point, radius, min_threshold, water_count):\n", "    \"\"\"Update labeling for points near current_point.\"\"\"\n", "\n", "    # 建立邻居点索引的集合\n", "    neighbor_set = set()  \n", "    \n", "    # 使用队列实现广度优先搜索\n", "    queue = deque()\n", "    queue.append(current_point)\n", "    \n", "    min_numbers = (2 * radius + 1) ** 2\n", "\n", "    while queue:\n", "        \n", "        current_point = queue.popleft()\n", "\n", "        if labels[current_point] > 0:\n", "            continue\n", "        \n", "        # 给当前点分配新标记\n", "        labels[current_point] = water_count\n", "\n", "        # 搜索当前点周围的邻居点\n", "        neighbors = find_neighbors(current_point, radius, arr, labels, min_threshold)\n", "        for neighbor in neighbors:\n", "            neighbor = tuple(neighbor)\n", "            # 如果邻居点未记录,添加到集合和队列中\n", "            if neighbor not in neighbor_set:\n", "                neighbor_set.add(neighbor)\n", "                queue.append(neighbor)\n", "                \n", "        # 控制河流的连续型，如果比例越小，河流越有可能连接\n", "        # if len(queue) / min_numbers < 0.1:\n", "        #     break\n", "\n", "       # 区分干流、支流、湖泊等\n", "        # print(max_angle(queue))\n", "       \n", "        # print('队列长度', len(queue))\n", "        # plt.scatter(np.array(queue)[:, 0], np.array(queue)[:, 1])\n", "        # plt.show()\n", "        \n", "        # plt.imshow(labels)\n", "        # plt.show()\n", "    # 直接使用 NumPy 数组存储邻居点索引\n", "    # neighbor_indices = np.array(list(neighbor_indices))  \n", "    \n", "    return labels\n", "\n", "def DBWOE(arr, radius, min_threshold=2500, fuzzy=10):\n", "    '''\n", "    arr: 水体出现概率矩阵\n", "    radius: 搜索半径\n", "    min_threshold: 存在条件\n", "    fuzzy: 模糊度\n", "    '''\n", "    # 预设标注矩阵\n", "    labels = np.full(arr.shape, 0, dtype=np.uint32)\n", "    \n", "    # 设置源头点\n", "    source_prob = 9500\n", "    row_start, col_start = np.where(arr>source_prob)\n", "    \n", "    # startPts = [(1,61)]\n", "    \n", "    start_points = list(zip(row_start, col_start))\n", "    num = len(start_points)\n", "\n", "    \n", "    num_sample = num // fuzzy\n", "    \n", "    # 设置源头点\n", "    # 找到更有可能的源头点\n", "    # 设置一个成本矩阵\n", "    \n", "    # random.shuffle(start_points)\n", "    \n", "    start_points = start_points[ : : fuzzy]\n", "    \n", "    # print(start_points)\n", "    \n", "    # 预设标注值\n", "    water_count = 1\n", "        \n", "    for idx in tqdm(start_points):\n", "        # 是否需要引入迭代次数的参数，对于交汇的河流，允许迭代次数更多的河流覆盖迭代次数较少的河流\n", "        if labels[idx] > 0:\n", "            continue\n", "        \n", "        # print('源头点', idx)\n", "        \n", "        labels = update_find(arr, labels, idx, radius, min_threshold, water_count)\n", "        \n", "        water_count += 1\n", "\n", "    return labels\n", "\n", "if __name__ == '__main__':\n", "    # Input.\n", "    # 2021_new_test\n", "    inws = r'C:\\Users\\<USER>\\Desktop\\Water\\2021_zou_tp_part.tif'\n", "    \n", "    ras = gdal.Open(inws)\n", "    \n", "    # Data Precessing\n", "    arr = ras.ReadAsArray()\n", "    \n", "    arr[(arr<0) | (arr>10000)] = 0\n", "    \n", "    # arr = signal.medfilt(arr, kernel_size=11)\n", "    \n", "    # kernal = np.ones((3,3), dtype=int)\n", "    # binary_arr = np.full(arr.shape, -1)\n", "    # binary_arr[arr>7500] = 1\n", "    # arr = ndimage.binary_closing(binary_arr, structure=kernal)\n", "    \n", "    # 加入噪声剔除，间断点填补相关处理\n", "    # 去噪的思路：随机种子、设定迭代阈值\n", "    \n", "    \n", "    # Export Setting.\n", "    dir_pth = r\"C:\\Users\\<USER>\\Desktop\\Water\\2021_zou_tp_part_dbwoe.tif\"\n", "    GeoTran = ras.GetGeoTransform()\n", "    # Get origin of raster\n", "    RastOrigin = (GeoTran[0], GeoTran[3])\n", "    # Get pixel dimensions \n", "    PixWidth = GeoTran[1]\n", "    PixHeight = GeoTran[5]\n", "    EPSG = 4326\n", "    \n", "    # Write.\n", "    labels = DBWOE(arr, 7, 2500, 100)\n", "\n", "    array2gtiff(dir_p<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>x<PERSON>idth, PixHeight, labels, EPSG)\n", "    "]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([  1,  21,  41,  61,  81, 101, 121, 141, 161, 181, 201, 221, 241,\n", "       261, 281, 301, 321, 341, 361, 381, 401, 421, 441, 461, 481, 501,\n", "       521, 541, 561, 581, 601, 621, 641, 661, 681, 701, 721, 741, 761,\n", "       781, 801, 821, 841, 861, 881, 901, 921, 941, 961, 981])"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["# 1. 存储河流对象的级别\n", "# 2. 存储河流对象的全年、季节类型\n", "\n", "# 关于区分河流级别的想法\n", "# 求邻居特征点的夹角想法可行\n", "# 但是在存储每次搜素的邻居时，需要划分两个数组，第一个数组为当前正在搜索的邻居，第二个数组为搜索过程中的新邻居\n", "# 当第一个数组搜索完成后，开始搜索第二个数组，此时第一个数组存储第二个数组的新邻居\n", "# 如此的数据结构，可以有效展示当前搜索的邻居形态特征，进而通过新邻居数组的夹角判断是否为河流级别交点"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from multiprocessing import Process, <PERSON><PERSON>, Manager\n", "from concurrent.futures import ThreadPoolExecutor\n", "import numpy as np\n", "import random\n", "from collections import deque\n", "from tqdm import tqdm\n", "\n", "def find_neighbors(index, radius, arr, labels, min_threshold, ratio, water_count):\n", "\n", "    rows, cols = arr.shape\n", "    row, col = tuple(index)\n", "\n", "    row_range = np.arange(max(0, row-radius), min(rows, row+radius+1)) \n", "    col_range = np.arange(max(0, col-radius), min(cols, col+radius+1)) \n", "    xx, yy = np.meshgrid(row_range, col_range, indexing='ij') \n", "    idx_pairs = np.dstack((xx, yy)).reshape(-1, 2)   \n", "\n", "    neighbors_prob = arr[idx_pairs[:,0], idx_pairs[:,1]]\n", "    labels_judge = labels[idx_pairs[:,0], idx_pairs[:,1]]\n", "    \n", "    # 可用于二次处理，按照形态学、高程等特征对河流进行拆分与合并\n", "    ratio_judge = np.sum(labels_judge==water_count, axis=0) > ratio*(radius*2+1) ** 2\n", "    # 考虑使用集合元组数据格式 \n", "    # 满足存在条件的才算是邻居 未标记的才算邻居 \n", "    neighbors_indies = idx_pairs[(neighbors_prob>min_threshold) & (labels_judge==0)] \n", "\n", "    return neighbors_indies \n", "\n", "def parallel_update_find(arr, labels, current_point, radius, min_threshold, ratio, water_count, queue):\n", "    \"\"\"Parallel version of update_find.\"\"\"\n", "    # 给当前点分配新标记\n", "    labels[current_point] = water_count\n", "\n", "    # 搜索当前点周围的邻居点\n", "    for neighbor in find_neighbors(current_point, radius, arr, labels, min_threshold, ratio, water_count):\n", "        # 如果邻居点未记录,添加到集合和队列中\n", "        neighbor = tuple(neighbor)\n", "        if labels[neighbor] == 0:\n", "            queue.put(neighbor)\n", "    \n", "    return labels\n", "\n", "def parallel_DBWOE(arr, radius, min_threshold=2500, fuzzy=10):\n", "    \"\"\"Parallel version of DBWOE.\"\"\"\n", "    labels = np.full(arr.shape, 0, dtype=np.uint32)\n", "    row_start, col_start = np.where(arr>min_threshold)\n", "    \n", "    start_points = list(zip(row_start, col_start))\n", "    num = len(start_points)\n", "    water_count = 1\n", "    \n", "    num_sample = num // fuzzy\n", "    \n", "    random.shuffle(start_points)\n", "    \n", "    start_points = start_points[: num_sample]\n", "    \n", "    with ThreadPoolExecutor() as executor:\n", "        futures = []\n", "        queue = Queue()\n", "        \n", "        for idx in start_points:\n", "            if labels[idx] > 0:\n", "                continue\n", "            \n", "            # 每个起点作为一个任务，加入线程池中并提交给线程池执行\n", "            futures.append(executor.submit(parallel_update_find, arr, labels, idx, radius, min_threshold, 0.5, water_count, queue))\n", "            \n", "            # 不断从队列中取出任务结果并更新标签数组\n", "            while not queue.empty():\n", "                result = queue.get()\n", "                labels = parallel_update_find(arr, labels, result, radius, min_threshold, 0.5, water_count, queue)\n", "        \n", "            water_count += 1\n", "            \n", "    return labels\n", "\n", "if __name__ == '__main__':\n", "    # Input.\n", "    inws = r'C:\\Users\\<USER>\\Desktop\\Water\\2021_new_test.tif'\n", "    \n", "    ras = gdal.Open(inws)\n", "    \n", "    # Data Precessing\n", "    arr = ras.ReadAsArray()\n", "\n", "    mask = (arr<0) | (arr>10000)\n", "    \n", "    arr[mask] = 0\n", "    \n", "    # arr = signal.medfilt(arr, kernel_size=11)\n", "    \n", "    # kernal = np.ones((3,3), dtype=int)\n", "    # binary_arr = np.full(arr.shape, -1)\n", "    # binary_arr[arr>7500] = 1\n", "    # arr = ndimage.binary_closing(binary_arr, structure=kernal)\n", "    \n", "    # 加入噪声剔除，间断点填补相关处理\n", "    # 去噪的思路：随机种子、设定迭代阈值\n", "    \n", "    \n", "    # Export Setting.\n", "    dir_pth = r\"C:\\Users\\<USER>\\Desktop\\Water\\2021_new_object_dbwoe_medfilt.tif\"\n", "    GeoTran = ras.GetGeoTransform()\n", "    # Get origin of raster\n", "    RastOrigin = (GeoTran[0], GeoTran[3])\n", "    # Get pixel dimensions \n", "    PixWidth = GeoTran[1]\n", "    PixHeight = GeoTran[5]\n", "    EPSG = 4326\n", "    \n", "    # Write.\n", "    labels = parallel_DBWOE(arr, 5, 7500, 20)\n", "\n", "    array2gtiff(dir_p<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>x<PERSON>idth, PixHeight, labels, EPSG)  \n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/2339 [00:00<?, ?it/s]"]}], "source": ["from multiprocessing import Process, Value\n", "from tqdm import tqdm_notebook as tqdm\n", "import os\n", "import numpy as np\n", "from osgeo import gdal, osr\n", "from scipy import signal, ndimage\n", "from collections import deque\n", "import random\n", "\n", "def readTif(dataset):\n", "    width = dataset.RasterXSize\n", "    \n", "    height = dataset.RasterYSize\n", "    \n", "    GdalImg_data = dataset.ReadAsArray(0, 0, width, height)\n", "    \n", "    return GdalImg_data\n", "\n", "def array2gtiff(newRasterfn,LowrLft_Origin,pixelWidth,pixelHeight,array,EPSG):\n", "\n", "    #Update users\n", "    print(\"Starting: Write \"+newRasterfn+\" to GeoTiff\")\n", "\n", "    #Get number of bands\n", "    bands = 1 # array.shape[0]\n", "\n", "    #Get number of columns and rows\n", "    rows = array.shape[0]\n", "    cols = array.shape[1]\n", "\n", "    #Get raster origin \n", "    originX = LowrLft_Origin[0]\n", "    originY = LowrLft_Origin[1]\n", "    \n", "    #Get a GeoTiff driver \n", "    driver = gdal.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>('GTiff')\n", "\n", "    #Initialize a output raster with GeoTransform parameters from input tile and array shape  \n", "    outRaster = driver.Create(newRasterfn, cols, rows, bands, gdal.GDT_Int16)\n", "\n", "    outRaster.SetGeoTransform((originX, pixelWidth, 0, originY, 0, pixelHeight))\n", "    \n", "    outRasterSRS = osr.SpatialReference()\n", "    \n", "    outRasterSRS.ImportFromEPSG(EPSG)\n", "    \n", "    outRaster.SetProjection(outRasterSRS.ExportToWkt())\n", "\n", "    #Write each band to the output raster, starting with band 1\n", "    for b in range(1, bands+1):\n", "        #Get the output band @ b\n", "        outband = outRaster.GetRasterBand(b)\n", "        \n", "        #Slice the 3D array at b-1 (2D)\n", "        # ary = array[b-1]\n", "        \n", "        #Write the 2D NumPy array to the band \n", "        outband.WriteArray(array)\n", "        \n", "        #Flush \n", "        outband.FlushCache()\n", "\n", "    #Update user \n", "    print(\"Finished: Write \"+newRasterfn+\" to GeoTiff\")\n", "\n", "from multiprocessing import Pool\n", "from tqdm import tqdm\n", "\n", "def find_neighbors(args):\n", "    index, radius, arr, labels, min_threshold, ratio, water_count = args\n", "    rows, cols = arr.shape\n", "    row, col = tuple(index)\n", "\n", "    row_range = np.arange(max(0, row-radius), min(rows, row+radius+1)) \n", "    col_range = np.arange(max(0, col-radius), min(cols, col+radius+1)) \n", "    xx, yy = np.meshgrid(row_range, col_range, indexing='ij') \n", "    idx_pairs = np.dstack((xx, yy)).reshape(-1, 2)   \n", "\n", "    neighbors_prob = arr[idx_pairs[:,0], idx_pairs[:,1]]\n", "    labels_judge = labels[idx_pairs[:,0], idx_pairs[:,1]]\n", "\n", "    # 可用于二次处理，按照形态学、高程等特征对河流进行拆分与合并\n", "    ratio_judge = np.sum(labels_judge==water_count, axis=0) > ratio*(radius*2+1) ** 2\n", "    \n", "    # 满足存在条件的才算是邻居 未标记的才算邻居 \n", "    neighbors_indies = idx_pairs[(neighbors_prob>min_threshold) & (labels_judge==0)] \n", "\n", "    return neighbors_indies \n", "\n", "def update_find(args):\n", "    arr, labels, current_point, radius, min_threshold, ratio, water_count = args\n", "    \n", "    # 使用队列实现广度优先搜索\n", "    queue = deque()\n", "    queue.append(current_point)\n", "\n", "    while queue:\n", "        current_point = queue.popleft()\n", "        if labels[current_point] > 0:\n", "            continue\n", "\n", "        # 给当前点分配新标记\n", "        labels[current_point] = water_count\n", "\n", "        # 搜索当前点周围的邻居点\n", "        with Pool() as pool:\n", "            neighbor_results = pool.map(find_neighbors, [(neighbor, radius, arr, labels, min_threshold, ratio, water_count) for neighbor in find_neighbors(current_point, radius, arr, labels, min_threshold, ratio, water_count)])\n", "        neighbor_indices = np.concatenate(neighbor_results, axis=0)\n", "        \n", "        # 如果邻居点未记录,添加到队列中\n", "        for neighbor in neighbor_indices:\n", "            neighbor = tuple(neighbor)\n", "            if neighbor not in queue:\n", "                queue.append(neighbor)\n", "\n", "    return labels\n", "\n", "def DBWOE(arr, radius, min_threshold=2500, fuzzy=10, num_workers=4):\n", "    labels = np.full(arr.shape, 0, dtype=np.uint32)\n", "\n", "    row_start, col_start = np.where(arr>min_threshold)\n", "\n", "    start_points = list(zip(row_start, col_start))\n", "    num = len(start_points)\n", "    water_count = 1\n", "\n", "    num_sample = num // fuzzy\n", "\n", "    random.shuffle(start_points)\n", "\n", "    start_points = start_points[: num_sample]\n", "\n", "    with Pool(num_workers) as pool, tqdm(total=len(start_points)) as pbar:\n", "        for idx, label in pool.imap_unordered(update_find, [(arr, labels, idx, radius, min_threshold, 0.5, water_count) for idx in start_points]):\n", "            labels = label\n", "            water_count += 1\n", "            pbar.update()\n", "\n", "    return labels\n", "\n", "if __name__ == '__main__':\n", "    # Input.\n", "    inws = r'C:\\Users\\<USER>\\Desktop\\Water\\2021_new_test.tif'\n", "    \n", "    ras = gdal.Open(inws)\n", "    \n", "    # Data Precessing\n", "    arr = ras.ReadAsArray()\n", "\n", "    mask = (arr<0) | (arr>10000)\n", "    \n", "    arr[mask] = 0\n", "    \n", "    # arr = signal.medfilt(arr, kernel_size=11)\n", "    \n", "    # kernal = np.ones((3,3), dtype=int)\n", "    # binary_arr = np.full(arr.shape, -1)\n", "    # binary_arr[arr>7500] = 1\n", "    # arr = ndimage.binary_closing(binary_arr, structure=kernal)\n", "    \n", "    # 加入噪声剔除，间断点填补相关处理\n", "    # 去噪的思路：随机种子、设定迭代阈值\n", "    \n", "    \n", "    # Export Setting.\n", "    # 2021_new_test\n", "    \n", "    dir_pth = r\"C:\\Users\\<USER>\\Desktop\\Water\\2021_new_test_dbwoe_medfilt.tif\"\n", "    GeoTran = ras.GetGeoTransform()\n", "    # Get origin of raster\n", "    RastOrigin = (GeoTran[0], GeoTran[3])\n", "    # Get pixel dimensions \n", "    PixWidth = GeoTran[1]\n", "    PixHeight = GeoTran[5]\n", "    EPSG = 4326\n", "    \n", "    # Write.\n", "    labels = DBWOE(arr, 5, 7500, 20, 1)\n", "\n", "    array2gtiff(dir_p<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>x<PERSON>idth, PixHeight, labels, EPSG)\n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from scipy import ndimage\n", "\n", "# 定义一个随机数组\n", "arr = np.random.randint(0, 10, size=10)\n", "\n", "# 定义结构元素，这里使用大小为3的矩形结构元素\n", "kernel = np.ones((3,), dtype=int)\n", "\n", "# 对数组进行闭运算\n", "\n", "# 输出结果\n", "print(\"原始数组：\", arr)\n", "print(\"闭运算后的数组：\", closed_arr)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kernal = np.ones((3,3), dtype=int)\n", "binary_arr = np.full(arr.shape, -1)\n", "binary_arr[arr>7500] = 1\n", "arr = ndimage.binary_closing(binary_arr, structure=kernal)\n", "\n", "array2gtiff(dir_<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, arr, EPSG)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["binary_arr"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}