{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os\n", "import glob\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from matplotlib.font_manager import FontProperties\n", "from scipy import stats\n", "from matplotlib import rcParams\n", "from statistics import mean\n", "# from sklearn.metrics import explained_variance_score,r2_score,median_absolute_error,mean_squared_error,mean_absolute_error\n", "from scipy.stats import pearsonr\n", "import seaborn as sns\n", "import geopandas as gpd"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["plt.rcParams['font.family'] = 'Times New Roman'\n", "plt.rcParams['font.size'] = 18\n", "# plt.rcParams['axes.formatter.use_mathtext'] = True\n", "# plt.rcParams['axes.formatter.limits'] = (-2, 2)\n", "# plt.rcParams['axes.formatter.min_exponent'] = 0\n", "# plt.rcParams['axes.formatter.useoffset'] = False"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["plt.rcParams.update({'font.family': 'Times New Roman',\n", "                    'font.size': 12,\n", "                    'axes.formatter.use_mathtext': True,\n", "                    'axes.formatter.limits': (-3, 3),\n", "                    'axes.formatter.min_exponent': 4,\n", "})"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x1e532d53b80>]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# water_level = pd.read_csv(r'../Time_series_validation\\挑选\\lake\\water_level.csv')\n", "# water_level = pd.read_csv(r'../Time_series_validation\\挑选\\reservoir\\water_level.csv')\n", "water_level = pd.read_excel(r'../Time_series_validation\\挑选\\reservoir\\level_elwell.xlsx')\n", "\n", "water_level['year'] = pd.to_datetime(water_level['datetime']).dt.year\n", "water_level['month'] = pd.to_datetime(water_level['datetime']).dt.month\n", "water_level = pd.DataFrame(water_level.groupby(['year', 'month'])['water_level'].mean())\n", "\n", "plt.plot(np.arange(len(water_level)), water_level)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# water_area = pd.read_csv(r'..\\Time_series_validation\\挑选\\lake\\lake_cer90_filter_jrc.csv')\n", "# water_area = pd.read_csv(r'..\\Time_series_validation\\挑选\\reservoir\\reservoir2_cer90_filter_jrc.csv')\n", "# water_area = pd.read_csv(r'..\\Time_series_validation\\挑选\\reservoir\\Allegheny_cer90_filter_jrc.csv')\n", "water_area = pd.read_csv(r'..\\Time_series_validation\\挑选\\reservoir\\water_elwell.csv')\n", "\n", "water_area = water_area.drop(['system:index', '.geo'], axis=1)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["water_area_agf = pd.read_csv(r'../Time_series_validation/reservoir_cer90_filter_agf.csv')"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["df = pd.merge(water_area, water_level, on=['year', 'month'], how='inner')"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.7227722772277227"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# 利用水位数据验证\n", "data = df[df['cer_gap_rate']<0.05]\n", "# data = df\n", "data_benchmark = df[df['gaps_rate']<0.05]\n", "data = pd.concat([data, data_benchmark], axis=0)\n", "data = data.drop_duplicates()\n", "(len(data)-len(data_benchmark)) / len(data_benchmark)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(0.0035491649699969)"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["data['cer_gap_rate'].min()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["data = data.sort_values(['year', 'month'])"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["# data = data[~((data['year']==2021) & (data['month']==11))]"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 's<PERSON><PERSON><PERSON>'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[16], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01ms<PERSON>arn\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmetrics\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m r2_score\n\u001b[0;32m      3\u001b[0m degree \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m  \u001b[38;5;66;03m# 多项式的阶数\u001b[39;00m\n\u001b[0;32m      4\u001b[0m coefficients \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39mpolyfit(data[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124marea\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39mvalues, data[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mwater_level\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39mvalues, degree)\n", "\u001b[1;31mModuleNotFoundError\u001b[0m: No module named 'sklearn'"]}], "source": ["from sklearn.metrics import r2_score\n", "\n", "degree = 1  # 多项式的阶数\n", "coefficients = np.polyfit(data['area'].values, data['water_level'].values, degree)\n", "\n", "predicted_values = np.polyval(coefficients, data['area'].values)\n", "r_squared = r2_score(data['water_level'].values, predicted_values)\n", "r_squared\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["# 确定度\n", "# 重建水面积\n", "# 无云水面积\n", "# 水位"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.5710595233881296 1.9079197401700822e-16\n"]}], "source": ["from scipy.stats import spearmanr\n", "from scipy.stats import pearsonr\n", "corr, p_value = spearmanr(data['area'].values, data['water_level'])\n", "print(corr, p_value)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.6754999286572865 9.304601585898615e-15\n"]}], "source": ["from scipy.stats import spearmanr\n", "from scipy.stats import pearsonr\n", "corr, p_value = spearmanr(data_benchmark['area'].values, data_benchmark['water_level'])\n", "print(corr, p_value)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x190fb9fd820>]"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(range(len(data['area'])), data['area'])"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x190fb81b6d0>]"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(range(len(data['water_level'])), data['water_level'])"]}, {"cell_type": "code", "execution_count": 167, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["182\n", "136\n"]}], "source": ["print(len(data))\n", "print(len(data_benchmark))"]}, {"cell_type": "code", "execution_count": 168, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>area</th>\n", "      <th>cer_gap_rate</th>\n", "      <th>gaps_rate</th>\n", "      <th>month</th>\n", "      <th>year</th>\n", "      <th>water_level</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>164</th>\n", "      <td>5.060433e+08</td>\n", "      <td>1.0</td>\n", "      <td>5.667351e-01</td>\n", "      <td>6</td>\n", "      <td>1995</td>\n", "      <td>359.065</td>\n", "    </tr>\n", "    <tr>\n", "      <th>165</th>\n", "      <td>5.043123e+08</td>\n", "      <td>1.0</td>\n", "      <td>5.643781e-05</td>\n", "      <td>7</td>\n", "      <td>1995</td>\n", "      <td>360.699</td>\n", "    </tr>\n", "    <tr>\n", "      <th>166</th>\n", "      <td>5.069153e+08</td>\n", "      <td>1.0</td>\n", "      <td>1.544049e-05</td>\n", "      <td>8</td>\n", "      <td>1995</td>\n", "      <td>359.922</td>\n", "    </tr>\n", "    <tr>\n", "      <th>167</th>\n", "      <td>5.111322e+08</td>\n", "      <td>1.0</td>\n", "      <td>6.906722e-06</td>\n", "      <td>9</td>\n", "      <td>1995</td>\n", "      <td>360.417</td>\n", "    </tr>\n", "    <tr>\n", "      <th>168</th>\n", "      <td>5.159175e+08</td>\n", "      <td>1.0</td>\n", "      <td>1.117654e-02</td>\n", "      <td>10</td>\n", "      <td>1995</td>\n", "      <td>361.863</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>159</th>\n", "      <td>3.369935e+08</td>\n", "      <td>1.0</td>\n", "      <td>8.573277e-06</td>\n", "      <td>9</td>\n", "      <td>2019</td>\n", "      <td>328.300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>160</th>\n", "      <td>3.432131e+08</td>\n", "      <td>1.0</td>\n", "      <td>1.000000e+00</td>\n", "      <td>1</td>\n", "      <td>2020</td>\n", "      <td>332.576</td>\n", "    </tr>\n", "    <tr>\n", "      <th>161</th>\n", "      <td>3.552839e+08</td>\n", "      <td>1.0</td>\n", "      <td>5.192430e-02</td>\n", "      <td>2</td>\n", "      <td>2020</td>\n", "      <td>333.618</td>\n", "    </tr>\n", "    <tr>\n", "      <th>162</th>\n", "      <td>3.545034e+08</td>\n", "      <td>1.0</td>\n", "      <td>1.003785e-01</td>\n", "      <td>3</td>\n", "      <td>2020</td>\n", "      <td>334.708</td>\n", "    </tr>\n", "    <tr>\n", "      <th>163</th>\n", "      <td>3.552041e+08</td>\n", "      <td>1.0</td>\n", "      <td>9.678447e-07</td>\n", "      <td>5</td>\n", "      <td>2020</td>\n", "      <td>333.071</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>182 rows × 6 columns</p>\n", "</div>"], "text/plain": ["             area  cer_gap_rate     gaps_rate  month  year  water_level\n", "164  5.060433e+08           1.0  5.667351e-01      6  1995      359.065\n", "165  5.043123e+08           1.0  5.643781e-05      7  1995      360.699\n", "166  5.069153e+08           1.0  1.544049e-05      8  1995      359.922\n", "167  5.111322e+08           1.0  6.906722e-06      9  1995      360.417\n", "168  5.159175e+08           1.0  1.117654e-02     10  1995      361.863\n", "..            ...           ...           ...    ...   ...          ...\n", "159  3.369935e+08           1.0  8.573277e-06      9  2019      328.300\n", "160  3.432131e+08           1.0  1.000000e+00      1  2020      332.576\n", "161  3.552839e+08           1.0  5.192430e-02      2  2020      333.618\n", "162  3.545034e+08           1.0  1.003785e-01      3  2020      334.708\n", "163  3.552041e+08           1.0  9.678447e-07      5  2020      333.071\n", "\n", "[182 rows x 6 columns]"]}, "execution_count": 168, "metadata": {}, "output_type": "execute_result"}], "source": ["data"]}, {"cell_type": "code", "execution_count": 169, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>area</th>\n", "      <th>cer_gap_rate</th>\n", "      <th>gaps_rate</th>\n", "      <th>month</th>\n", "      <th>year</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>5.264857e+08</td>\n", "      <td>1.0</td>\n", "      <td>1.000000</td>\n", "      <td>1</td>\n", "      <td>1990</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5.247509e+08</td>\n", "      <td>1.0</td>\n", "      <td>0.108207</td>\n", "      <td>2</td>\n", "      <td>1990</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>5.167914e+08</td>\n", "      <td>1.0</td>\n", "      <td>0.634988</td>\n", "      <td>11</td>\n", "      <td>1990</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>5.575463e+08</td>\n", "      <td>1.0</td>\n", "      <td>0.001445</td>\n", "      <td>5</td>\n", "      <td>1998</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5.565728e+08</td>\n", "      <td>1.0</td>\n", "      <td>0.000102</td>\n", "      <td>6</td>\n", "      <td>1998</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>377</th>\n", "      <td>5.080967e+08</td>\n", "      <td>1.0</td>\n", "      <td>1.000000</td>\n", "      <td>12</td>\n", "      <td>1997</td>\n", "    </tr>\n", "    <tr>\n", "      <th>378</th>\n", "      <td>5.052049e+08</td>\n", "      <td>1.0</td>\n", "      <td>1.000000</td>\n", "      <td>1</td>\n", "      <td>1998</td>\n", "    </tr>\n", "    <tr>\n", "      <th>379</th>\n", "      <td>5.424711e+08</td>\n", "      <td>1.0</td>\n", "      <td>0.959971</td>\n", "      <td>2</td>\n", "      <td>1998</td>\n", "    </tr>\n", "    <tr>\n", "      <th>380</th>\n", "      <td>5.597684e+08</td>\n", "      <td>1.0</td>\n", "      <td>0.022119</td>\n", "      <td>3</td>\n", "      <td>1998</td>\n", "    </tr>\n", "    <tr>\n", "      <th>381</th>\n", "      <td>5.592249e+08</td>\n", "      <td>1.0</td>\n", "      <td>0.001933</td>\n", "      <td>4</td>\n", "      <td>1998</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>382 rows × 5 columns</p>\n", "</div>"], "text/plain": ["             area  cer_gap_rate  gaps_rate  month  year\n", "0    5.264857e+08           1.0   1.000000      1  1990\n", "1    5.247509e+08           1.0   0.108207      2  1990\n", "2    5.167914e+08           1.0   0.634988     11  1990\n", "3    5.575463e+08           1.0   0.001445      5  1998\n", "4    5.565728e+08           1.0   0.000102      6  1998\n", "..            ...           ...        ...    ...   ...\n", "377  5.080967e+08           1.0   1.000000     12  1997\n", "378  5.052049e+08           1.0   1.000000      1  1998\n", "379  5.424711e+08           1.0   0.959971      2  1998\n", "380  5.597684e+08           1.0   0.022119      3  1998\n", "381  5.592249e+08           1.0   0.001933      4  1998\n", "\n", "[382 rows x 5 columns]"]}, "execution_count": 169, "metadata": {}, "output_type": "execute_result"}], "source": ["water_area"]}, {"cell_type": "code", "execution_count": 170, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>water_level</th>\n", "    </tr>\n", "    <tr>\n", "      <th>year</th>\n", "      <th>month</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">1995</th>\n", "      <th>6</th>\n", "      <td>359.065</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>360.699</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>359.922</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>360.417</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>361.863</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <th>...</th>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"3\" valign=\"top\">2020</th>\n", "      <th>2</th>\n", "      <td>333.618</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>334.708</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>333.071</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">2024</th>\n", "      <th>3</th>\n", "      <td>327.729</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>327.284</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>184 rows × 1 columns</p>\n", "</div>"], "text/plain": ["            water_level\n", "year month             \n", "1995 6          359.065\n", "     7          360.699\n", "     8          359.922\n", "     9          360.417\n", "     10         361.863\n", "...                 ...\n", "2020 2          333.618\n", "     3          334.708\n", "     5          333.071\n", "2024 3          327.729\n", "     4          327.284\n", "\n", "[184 rows x 1 columns]"]}, "execution_count": 170, "metadata": {}, "output_type": "execute_result"}], "source": ["water_level"]}, {"cell_type": "code", "execution_count": 171, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>water_level</th>\n", "    </tr>\n", "    <tr>\n", "      <th>year</th>\n", "      <th>month</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">1995</th>\n", "      <th>6</th>\n", "      <td>359.065</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>360.699</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>359.922</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>360.417</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>361.863</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <th>...</th>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"3\" valign=\"top\">2020</th>\n", "      <th>2</th>\n", "      <td>333.618</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>334.708</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>333.071</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">2024</th>\n", "      <th>3</th>\n", "      <td>327.729</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>327.284</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>184 rows × 1 columns</p>\n", "</div>"], "text/plain": ["            water_level\n", "year month             \n", "1995 6          359.065\n", "     7          360.699\n", "     8          359.922\n", "     9          360.417\n", "     10         361.863\n", "...                 ...\n", "2020 2          333.618\n", "     3          334.708\n", "     5          333.071\n", "2024 3          327.729\n", "     4          327.284\n", "\n", "[184 rows x 1 columns]"]}, "execution_count": 171, "metadata": {}, "output_type": "execute_result"}], "source": ["water_level"]}, {"cell_type": "code", "execution_count": 172, "metadata": {}, "outputs": [], "source": ["def add_time(df):\n", "    df['date_str'] = df['year'].astype(str) + '-' + df['month'].astype(str) + '-1'\n", "    df['time'] = pd.to_datetime(df['date_str'])\n", "    df = df.drop('date_str', axis=1)\n", "    \n", "    return df\n", "\n", "df = pd.merge(water_area, water_level, on=['year', 'month'], how='outer')\n", "data = water_area = add_time(df)\n", "data = data.dropna(subset=['area', 'time'])"]}, {"cell_type": "code", "execution_count": 178, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>area</th>\n", "      <th>cer_gap_rate</th>\n", "      <th>gaps_rate</th>\n", "      <th>month</th>\n", "      <th>year</th>\n", "      <th>water_level</th>\n", "      <th>time</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>96</th>\n", "      <td>5.052049e+08</td>\n", "      <td>1.0</td>\n", "      <td>1.000000</td>\n", "      <td>1</td>\n", "      <td>1998</td>\n", "      <td>NaN</td>\n", "      <td>1998-01-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>97</th>\n", "      <td>5.424711e+08</td>\n", "      <td>1.0</td>\n", "      <td>0.959971</td>\n", "      <td>2</td>\n", "      <td>1998</td>\n", "      <td>370.853</td>\n", "      <td>1998-02-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>98</th>\n", "      <td>5.597684e+08</td>\n", "      <td>1.0</td>\n", "      <td>0.022119</td>\n", "      <td>3</td>\n", "      <td>1998</td>\n", "      <td>NaN</td>\n", "      <td>1998-03-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99</th>\n", "      <td>5.592249e+08</td>\n", "      <td>1.0</td>\n", "      <td>0.001933</td>\n", "      <td>4</td>\n", "      <td>1998</td>\n", "      <td>369.088</td>\n", "      <td>1998-04-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>100</th>\n", "      <td>5.575463e+08</td>\n", "      <td>1.0</td>\n", "      <td>0.001445</td>\n", "      <td>5</td>\n", "      <td>1998</td>\n", "      <td>NaN</td>\n", "      <td>1998-05-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>101</th>\n", "      <td>5.565728e+08</td>\n", "      <td>1.0</td>\n", "      <td>0.000102</td>\n", "      <td>6</td>\n", "      <td>1998</td>\n", "      <td>368.767</td>\n", "      <td>1998-06-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>102</th>\n", "      <td>5.571766e+08</td>\n", "      <td>1.0</td>\n", "      <td>0.000015</td>\n", "      <td>7</td>\n", "      <td>1998</td>\n", "      <td>369.842</td>\n", "      <td>1998-07-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>103</th>\n", "      <td>5.578279e+08</td>\n", "      <td>1.0</td>\n", "      <td>0.000284</td>\n", "      <td>8</td>\n", "      <td>1998</td>\n", "      <td>NaN</td>\n", "      <td>1998-08-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>104</th>\n", "      <td>5.589288e+08</td>\n", "      <td>1.0</td>\n", "      <td>0.005002</td>\n", "      <td>9</td>\n", "      <td>1998</td>\n", "      <td>NaN</td>\n", "      <td>1998-09-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>105</th>\n", "      <td>5.587170e+08</td>\n", "      <td>1.0</td>\n", "      <td>0.007273</td>\n", "      <td>10</td>\n", "      <td>1998</td>\n", "      <td>370.734</td>\n", "      <td>1998-10-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>106</th>\n", "      <td>5.607460e+08</td>\n", "      <td>1.0</td>\n", "      <td>0.083751</td>\n", "      <td>11</td>\n", "      <td>1998</td>\n", "      <td>370.001</td>\n", "      <td>1998-11-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>107</th>\n", "      <td>5.052049e+08</td>\n", "      <td>1.0</td>\n", "      <td>1.000000</td>\n", "      <td>12</td>\n", "      <td>1998</td>\n", "      <td>369.955</td>\n", "      <td>1998-12-01</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             area  cer_gap_rate  gaps_rate  month  year  water_level  \\\n", "96   5.052049e+08           1.0   1.000000      1  1998          NaN   \n", "97   5.424711e+08           1.0   0.959971      2  1998      370.853   \n", "98   5.597684e+08           1.0   0.022119      3  1998          NaN   \n", "99   5.592249e+08           1.0   0.001933      4  1998      369.088   \n", "100  5.575463e+08           1.0   0.001445      5  1998          NaN   \n", "101  5.565728e+08           1.0   0.000102      6  1998      368.767   \n", "102  5.571766e+08           1.0   0.000015      7  1998      369.842   \n", "103  5.578279e+08           1.0   0.000284      8  1998          NaN   \n", "104  5.589288e+08           1.0   0.005002      9  1998          NaN   \n", "105  5.587170e+08           1.0   0.007273     10  1998      370.734   \n", "106  5.607460e+08           1.0   0.083751     11  1998      370.001   \n", "107  5.052049e+08           1.0   1.000000     12  1998      369.955   \n", "\n", "          time  \n", "96  1998-01-01  \n", "97  1998-02-01  \n", "98  1998-03-01  \n", "99  1998-04-01  \n", "100 1998-05-01  \n", "101 1998-06-01  \n", "102 1998-07-01  \n", "103 1998-08-01  \n", "104 1998-09-01  \n", "105 1998-10-01  \n", "106 1998-11-01  \n", "107 1998-12-01  "]}, "execution_count": 178, "metadata": {}, "output_type": "execute_result"}], "source": ["data[data['year']==1998].head(50)"]}, {"cell_type": "code", "execution_count": 174, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_16628\\2422958223.py:100: FutureWarning: 'Y' is deprecated and will be removed in a future version, please use 'YE' instead.\n", "  ax2.set_xticks(pd.date_range(start=start_time, end=end_time, freq='4Y'))\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 2500x800 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import statsmodels.api as sm\n", "import matplotlib.dates as mdates\n", "\n", "data = data.sort_values(['year', 'month'])\n", "\n", "fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(25, 8),\n", "                               gridspec_kw={'height_ratios': [3, 1]},)\n", "\n", "time = data['time']\n", "# 转为公顷\n", "area = data['area'] / 1000 / 1000 * 100\n", "# area_JRC = data['area_JRC'] / 1000 / 1000 *100\n", "# area_origin = data['area_shelter'] / 1000 / 1000 * 100\n", "water_level = data['water_level']\n", "gap_rate = data['gaps_rate']\n", "# start_time = pd.Timestamp('2008-01-01')\n", "start_time = data['time'].min()\n", "end_time = pd.Timestamp('2023-09-01')\n", "end_time = data['time'].max()\n", "\n", "# 填补后面积\n", "ax1.plot(time, area, 'ro', markersize=4, markerfacecolor='none', linestyle='-', linewidth=1, label='Water area')\n", "# x = np.arange(0, 450)\n", "# plot_time_series(data, x, area.values, 'red', False, ax1)\n", "# JRC面积\n", "# ax1.plot(time, area_JRC, 'gray', marker='o', markersize=3, markerfacecolor='none', linestyle='none', linewidth=1, alpha=0.5, label='Water area ')\n", "\n", "# 添加趋势线\n", "# X = time.dt.strftime('%Y%m%d').astype(int).values.reshape(-1, 1)\n", "X = np.array(range(len(time))).reshape(-1, 1)\n", "X = sm.add_constant(X)\n", "y = area.values\n", "\n", "model = sm.OLS(y, X)\n", "results_area = model.fit()\n", "y_pred = results_area.predict(X)\n", "\n", "ax1.plot(time, y_pred, linestyle='--', linewidth=1, color='red', label='Trend line')\n", "\n", "# 设置注记\n", "ax1.set_xlim(start_time, end_time)\n", "ax1.set_xticklabels([])\n", "ax1.set_ylabel('Water area / ha')\n", "ax1.set_ylim(y.min()*0.9, y.max()*1.05)\n", "ax1.set_xticks([])\n", "ax1.yaxis.set_label_coords(-0.03, 0.5, transform=ax1.transAxes)\n", "\n", "ax1.tick_params(axis='y', labelcolor='r', direction='in')\n", "ax1.tick_params(axis='x', direction='in')\n", "\n", "ax1.annotate('(a)', xy=(0.001, 0.99), xycoords='axes fraction',\n", "             transform=ax1, fontsize=22,  va='top', ha='left', fontweight='bold')\n", "\n", "# # 突出点位\n", "# highlight_x = pd.Timestamp('1990-08-01')\n", "# highlight_y = data[data['time']==highlight_x]['area'].values\n", "# one_hour = pd.Timedel<PERSON>(hours=1)\n", "\n", "# # 添加矩形框\n", "# rect = patches.Rectangle((highlight_x - one_hour, highlight_y - 1), 0.4, 2, fill=None, color='r', linestyle='--', label='Example')\n", "\n", "# ax1.add_patch(rect)\n", "\n", "# 第二个Y轴对象\n", "ax1f = ax1.twinx()\n", "# 水位数据\n", "ax1f.plot(time, water_level, 'bo', markersize=4, markerfacecolor='none', linestyle='-', linewidth=1, alpha=0.7, label='Water level')\n", "# plot_time_series(data, x, water_level.values, 'blue', False, ax1f)\n", "\n", "# 添加趋势线\n", "# X = time.dt.strftime('%Y%m%d').astype(int).values.reshape(-1, 1)\n", "X = np.array(range(len(time))).reshape(-1, 1)\n", "X = sm.add_constant(X)\n", "y = water_level.values\n", "\n", "model = sm.OLS(y, X)\n", "results_level = model.fit()\n", "y_pred2 = results_level.predict(X)\n", "ax1f.plot(time, y_pred2, linestyle='--', linewidth=1, color='blue', label='Trend line')\n", "\n", "# 设置注记\n", "ax1f.set_ylim(water_level.min()*0.9, water_level.max()*1.05)\n", "ax1f.set_ylabel('Water level / m')\n", "ax1f.tick_params(axis='y', labelcolor='b', direction='in')\n", "ax1f.set_xticks([])\n", "\n", "# axes[0].text(-0.04, 0.95, '(a)', transform=axes[0].transAxes, va='top', ha='left', fontsize=18, fontweight='bold')\n", "\n", "# 设置注记\n", "# correlation_text = \"Pearson'r = 0.70, P < 0.001\"\n", "# ax1.annotate(correlation_text, xy=(0.55, 0.9), xycoords='axes fraction',\n", "#              fontsize=18, ha='center', va='center')\n", "\n", "ax2.fill_between(time, gap_rate*100, alpha=0.6)\n", "ax2.set_xlim(start_time, end_time)\n", "ax2.set_ylim(0, 11)\n", "\n", "date_format = mdates.DateFormatter('%b. %Y')  # 月份和年份格式\n", "ax2.xaxis.set_major_formatter(date_format)\n", "ax2.set_xticks(pd.date_range(start=start_time, end=end_time, freq='4Y'))\n", "ax2.set_yticks(np.arange(20, 120, 40))\n", "ax2.tick_params(axis='y', direction='in')\n", "ax2.tick_params(axis='x', direction='in', size=6)\n", "\n", "ax2.set_xlabel('Date')\n", "ax2.set_ylabel('Gap coverage /%', fontsize=16)\n", "ax2.yaxis.set_label_coords(-0.03, 0.5, transform=ax2.transAxes)\n", "ax2.ticklabel_format(style='plain', axis='y')\n", "\n", "ax2.text(0.001, 0.99, '(b)', transform=ax2.transAxes, fontsize=22,  va='top', ha='left', fontweight='bold')\n", "\n", "# ax2f = ax2.twinx()\n", "# ax2f.plot(time, area_JRC, 'gray', marker='o', markersize=3, markerfacecolor='none', linestyle='-', linewidth=1, alpha=0.5, label='Water area ')\n", "\n", "# plt.legend()\n", "# fig.tight_layout()\n", "\n", "plt.subplots_adjust(hspace=0.1)\n", "# plt.savefig('../Plot/filled_water_series.tif', dpi=600, bbox_inches='tight')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 2}